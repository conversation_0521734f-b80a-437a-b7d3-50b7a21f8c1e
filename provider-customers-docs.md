# Provider Customer Management System Documentation

## Overview

The Provider Customer Management system in the Wasp.js application provides comprehensive functionality for healthcare providers to manage their customer relationships, including CRUD operations, customer folder management, contact information handling, appointment history tracking, search and filtering capabilities, and privacy-compliant data management.

## Table of Contents

1. [Database Schema](#database-schema)
2. [Provider Customer CRUD Operations](#provider-customer-crud-operations)
3. [API Endpoints](#api-endpoints)
4. [Authentication & Authorization](#authentication--authorization)
5. [Business Rules & Workflows](#business-rules--workflows)
6. [Customer Folder System](#customer-folder-system)
7. [Customer Search & Filtering](#customer-search--filtering)
8. [Customer Data Privacy & Security](#customer-data-privacy--security)
9. [Customer Communication Management](#customer-communication-management)
10. [Error Handling](#error-handling)

## Database Schema

### Core Models

#### CustomerFolder Model (Provider-Customer Relationship)
```prisma
model CustomerFolder {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  provider    SProvider @relation(fields: [sProviderId], references: [id])
  sProviderId Int

  customer User   @relation(fields: [userId], references: [id])
  userId   String

  notes    String? @db.Text
  isActive Boolean @default(true)

  appointments Appointment[]

  // Unique constraint: One customer folder per provider-customer pair
  @@unique([sProviderId, userId])
}
```

#### User Model (Customer Profile)
```prisma
model User {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Authentication fields
  username String @unique
  email    String @unique

  // Profile information
  firstName    String?
  lastName     String?
  mobileNumber String? @unique
  nationalId   String? @unique

  // System fields
  role                          Role      @default(CUSTOMER)
  isEmailVerified               Boolean   @default(false)
  emailVerificationSentAt       DateTime?
  passwordResetSentAt           DateTime?
  isRegistrationCompleteViaOtp  Boolean   @default(false)

  // Subscription and credits
  subscriptionStatus String? // 'active', 'cancel_at_period_end', 'past_due', 'deleted'
  subscriptionPlan   String? // 'hobby', 'pro'
  credits            Int     @default(3)
  queues             Int     @default(1)

  // Profile picture
  profilePictureId String?
  profilePicture   File?   @relation("UserProfilePicture", fields: [profilePictureId], references: [id], onDelete: SetNull)

  // Customer relationships
  customerFolders CustomerFolder[] // Relation to folders where this user is the customer
  
  // Provider relationship (if user is also a provider)
  serviceProvider SProvider?

  // Other relations
  appointmentHistory AppointmentHistory[]
  notifications      Notification[] @relation("UserNotifications")
  files              File[]
}
```

### Customer Data Structure

#### Customer Profile Fields
- **Basic Information**: firstName, lastName, mobileNumber, email
- **Identification**: nationalId (optional)
- **Contact Preferences**: Communication settings and notification preferences
- **Provider Notes**: Provider-specific notes stored in CustomerFolder
- **Appointment History**: Complete history of appointments with the provider
- **Account Status**: Active/inactive status for provider relationship

## Provider Customer CRUD Operations

### 1. Create Customer

#### Wasp Operation
```typescript
// File: app/src/provider/operations.ts
export const createProviderCustomer: CreateProviderCustomer<CreateProviderCustomerData, CustomerFolder>
```

#### Validation Schema
```typescript
const createProviderCustomerInputSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  mobileNumber: z.string().min(1, "Mobile number is required"),
  email: z.string().email("Invalid email format").optional(),
  nationalId: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
});
```

#### Business Logic
- **Duplicate Prevention**: Checks for existing users with same mobile/email/nationalId
- **Provider Relationship**: Creates CustomerFolder linking provider to customer
- **User Creation**: Creates new User record if customer doesn't exist
- **Folder Linking**: Links existing User to provider if customer already exists
- **Privacy Compliance**: Ensures data isolation between providers

#### Transaction Flow
1. Validate provider authentication and profile existence
2. Check for existing users with provided identifiers (mobile, email, nationalId)
3. Create new User record if customer doesn't exist
4. Create CustomerFolder linking provider to customer
5. Handle duplicate prevention and conflict resolution
6. Return complete customer folder with user details

### 2. Read/Retrieve Customers

#### Wasp Query
```typescript
// File: app/src/provider/operations.ts
export const getProviderCustomers = async (args: unknown, context: any): Promise<any[]>
```

#### Response Structure
```typescript
interface CustomerResponse {
  id: string;
  firstName: string;
  lastName: string;
  mobileNumber: string;
  email?: string;
  nationalId?: string;
  notes?: string;
  appointmentCount: number;
  createdAt: Date;
}
```

#### Filtering Options
- **Search**: Filter by name, mobile number, or email
- **Pagination**: Support for page-based pagination with configurable limits
- **Sorting**: Sort by name, email, creation date, or appointment count
- **Provider Scope**: Automatic filtering by authenticated provider
- **Active Status**: Filter by active/inactive customer relationships

#### Included Relations
- **Customer Profile**: Complete user profile information
- **Provider Notes**: Provider-specific notes from CustomerFolder
- **Appointment Count**: Total number of appointments with provider
- **Relationship Status**: Active/inactive status of provider-customer relationship

### 3. Update Customer

#### Wasp Operation
```typescript
// File: app/src/provider/operations.ts
export const updateProviderCustomer: UpdateProviderCustomer<UpdateProviderCustomerData, CustomerFolder>
```

#### Validation Schema
```typescript
const updateProviderCustomerInputSchema = z.object({
  customerUserId: z.string().uuid("Invalid Customer User ID format"),
  firstName: z.string().min(1, "First name is required").optional(),
  lastName: z.string().min(1, "Last name is required").optional(),
  mobileNumber: z.string().min(1, "Mobile number is required").optional(),
  email: z.string().email("Invalid email format").optional(),
  nationalId: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
});
```

#### Editable Fields
- **Basic Information**: firstName, lastName, mobileNumber, email
- **Identification**: nationalId
- **Provider Notes**: notes field in CustomerFolder
- **Contact Preferences**: Communication and notification settings

#### Business Rules
- **Provider Scope**: Only update customers linked to authenticated provider
- **Unique Constraints**: Prevent duplicate mobile numbers and emails
- **Data Validation**: Validate email format and mobile number format
- **Privacy Protection**: Cannot access customers of other providers

### 4. Delete/Deactivate Customer

#### Deactivation Policies
- **Soft Deactivation**: Set CustomerFolder.isActive to false
- **Data Retention**: Customer User record remains intact
- **Appointment History**: All appointment history preserved
- **Provider Isolation**: Only affects relationship with specific provider

#### Customer Relationship Management
```typescript
const deactivateCustomerRelationship = async (customerId: string, providerId: number, context: any) => {
  // 1. Verify provider ownership of customer relationship
  const customerFolder = await context.entities.CustomerFolder.findFirst({
    where: {
      userId: customerId,
      sProviderId: providerId
    }
  });
  
  if (!customerFolder) {
    throw new HttpError(404, 'Customer relationship not found');
  }
  
  // 2. Check for active appointments
  const activeAppointments = await context.entities.Appointment.count({
    where: {
      customerFolderId: customerFolder.id,
      status: { notIn: ['canceled', 'completed', 'noshow'] },
      expectedAppointmentStartTime: { gte: new Date() }
    }
  });
  
  if (activeAppointments > 0) {
    throw new HttpError(409, 
      `Cannot deactivate customer: ${activeAppointments} active appointments exist. ` +
      `Please complete or cancel these appointments first.`
    );
  }
  
  // 3. Deactivate customer relationship
  await context.entities.CustomerFolder.update({
    where: { id: customerFolder.id },
    data: { isActive: false }
  });
  
  return { success: true, message: 'Customer relationship deactivated' };
};
```

#### Data Retention Requirements
- **User Data**: Customer User record never deleted (GDPR compliance)
- **Appointment History**: Complete history preserved for legal/medical requirements
- **Provider Notes**: Notes preserved but marked as inactive relationship
- **Communication History**: Message history preserved for audit purposes

## API Endpoints

### REST API Endpoints

#### Provider Customer Management
```http
GET    /api/auth/providers/customers              # Get all provider customers
GET    /api/auth/providers/customers/:id          # Get specific customer
POST   /api/auth/providers/customers              # Create new customer
PUT    /api/auth/providers/customers/:id          # Update customer
```

#### Customer Search and Filtering
```http
GET    /api/auth/providers/customers?search=query # Search customers
GET    /api/auth/providers/customers?page=1&limit=20 # Paginated results
GET    /api/auth/providers/customers?sortBy=name&sortOrder=asc # Sorted results
```

### Wasp Operations

#### Queries
```typescript
// Get all provider customers
query getProviderCustomers {
  fn: import { getProviderCustomers } from "@src/provider/operations",
  entities: [User, SProvider, CustomerFolder, Translation],
  auth: true
}
```

#### Actions
```typescript
// Create customer
action createProviderCustomer {
  fn: import { createProviderCustomer } from "@src/provider/operations",
  entities: [User, SProvider, CustomerFolder, Translation],
  auth: true
}

// Update customer (operation not explicitly defined in main.wasp but implemented)
action updateProviderCustomer {
  fn: import { updateProviderCustomer } from "@src/provider/operations",
  entities: [User, SProvider, CustomerFolder],
  auth: true
}
```

## Authentication & Authorization

### Authentication Requirements
- All provider customer endpoints require `auth: true`
- JWT token validation via Wasp's built-in auth system
- User must have `role: 'CLIENT'` (provider role)

### Authorization Patterns
```typescript
// Customer access verification
const verifyCustomerAccess = async (customerId: string, providerId: number, entities: any) => {
  const customerFolder = await entities.CustomerFolder.findFirst({
    where: {
      userId: customerId,
      sProviderId: providerId,
      isActive: true
    },
    include: {
      customer: {
        select: { id: true, firstName: true, lastName: true }
      }
    }
  });

  if (!customerFolder) {
    throw new HttpError(404, 'Customer not found or you do not have permission to access this customer');
  }

  return customerFolder;
};
```

### Provider-Customer Data Isolation
```typescript
// Ensure provider can only access their own customers
const getProviderCustomersWithIsolation = async (providerId: number, entities: any) => {
  return await entities.CustomerFolder.findMany({
    where: {
      sProviderId: providerId,
      isActive: true
    },
    include: {
      customer: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          mobileNumber: true,
          email: true,
          nationalId: true
        }
      },
      appointments: {
        select: { id: true }
      }
    },
    orderBy: { createdAt: 'desc' }
  });
};
```

### Context Structure
```typescript
interface CustomerApiContext {
  user: {
    id: string;
    role: string;
  };
  entities: {
    User: PrismaUserDelegate;
    CustomerFolder: PrismaCustomerFolderDelegate;
    SProvider: PrismaSProviderDelegate;
    Appointment: PrismaAppointmentDelegate;
    // ... other entities
  };
}
```

## Business Rules & Workflows

### Customer Creation Workflow

#### 1. Pre-Creation Validation
- Provider must have active account and profile
- Customer identifiers (mobile, email, nationalId) must be unique if provided
- Mobile number is required, email and nationalId are optional
- Provider cannot create duplicate customer relationships

#### 2. Customer Creation Process
```typescript
// Customer creation transaction flow
const createCustomerFlow = async (customerData, context) => {
  return await prisma.$transaction(async (tx) => {
    // 1. Validate provider exists and is active
    const provider = await tx.sProvider.findUnique({
      where: { userId: context.user.id },
      select: { id: true }
    });

    if (!provider) {
      throw new HttpError(404, 'Provider profile not found');
    }

    // 2. Check for existing users with provided identifiers
    const orConditions = [];

    if (customerData.mobileNumber) {
      orConditions.push({ mobileNumber: customerData.mobileNumber });
    }
    if (customerData.email) {
      orConditions.push({ email: customerData.email });
    }
    if (customerData.nationalId) {
      orConditions.push({ nationalId: customerData.nationalId });
    }

    const existingUser = orConditions.length > 0
      ? await tx.user.findFirst({ where: { OR: orConditions } })
      : null;

    let customerUser;

    if (existingUser) {
      // 3a. Check if customer folder already exists
      const existingFolder = await tx.customerFolder.findFirst({
        where: {
          sProviderId: provider.id,
          userId: existingUser.id
        }
      });

      if (existingFolder) {
        throw new HttpError(409, 'Customer relationship already exists');
      }

      // 3b. Use existing user
      customerUser = existingUser;
    } else {
      // 3c. Create new user
      customerUser = await tx.user.create({
        data: {
          username: `customer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          email: customerData.email || `temp_${Date.now()}@example.com`,
          firstName: customerData.firstName,
          lastName: customerData.lastName,
          mobileNumber: customerData.mobileNumber,
          nationalId: customerData.nationalId,
          role: 'CUSTOMER',
          isEmailVerified: false
        }
      });
    }

    // 4. Create customer folder
    const customerFolder = await tx.customerFolder.create({
      data: {
        sProviderId: provider.id,
        userId: customerUser.id,
        notes: customerData.notes,
        isActive: true
      },
      include: {
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            mobileNumber: true,
            email: true,
            nationalId: true
          }
        }
      }
    });

    return customerFolder;
  });
};
```

#### 3. Post-Creation Actions
- Initialize customer communication preferences
- Set up default notification settings
- Create audit log entry
- Update provider dashboard statistics

### Customer Data Management Rules

#### Data Uniqueness Constraints
- **Mobile Number**: Must be unique across all users
- **Email**: Must be unique across all users if provided
- **National ID**: Must be unique across all users if provided
- **Provider-Customer Relationship**: One CustomerFolder per provider-customer pair

#### Data Validation Rules
```typescript
const validateCustomerData = (data: CreateCustomerData) => {
  // Mobile number validation
  if (!data.mobileNumber || data.mobileNumber.trim().length === 0) {
    throw new HttpError(400, 'Mobile number is required');
  }

  // Email validation (if provided)
  if (data.email && !isValidEmail(data.email)) {
    throw new HttpError(400, 'Invalid email format');
  }

  // Name validation
  if (!data.firstName || data.firstName.trim().length === 0) {
    throw new HttpError(400, 'First name is required');
  }

  if (!data.lastName || data.lastName.trim().length === 0) {
    throw new HttpError(400, 'Last name is required');
  }

  // Notes length validation
  if (data.notes && data.notes.length > 1000) {
    throw new HttpError(400, 'Notes cannot exceed 1000 characters');
  }
};
```

### Customer Relationship Management

#### Provider-Customer Isolation
- Each provider can only access customers in their CustomerFolder
- Customer data is scoped to specific provider relationships
- Cross-provider customer access is strictly prohibited
- Appointment history is provider-specific

#### Customer Folder Status Management
```typescript
interface CustomerFolderStatus {
  isActive: boolean; // Relationship is active
  lastAppointment?: Date; // Last appointment date
  totalAppointments: number; // Total appointment count
  notes?: string; // Provider-specific notes
}

const updateCustomerFolderStatus = async (folderId: number, status: Partial<CustomerFolderStatus>, context: any) => {
  const updatedFolder = await context.entities.CustomerFolder.update({
    where: { id: folderId },
    data: {
      isActive: status.isActive,
      notes: status.notes,
      updatedAt: new Date()
    }
  });

  return updatedFolder;
};
```

## Customer Folder System

### Folder Structure and Purpose

#### Provider-Customer Relationship Model
```typescript
interface CustomerFolderStructure {
  id: number;
  sProviderId: number; // Provider ID
  userId: string; // Customer User ID
  notes?: string; // Provider-specific notes about customer
  isActive: boolean; // Relationship status
  createdAt: Date; // When relationship was established
  updatedAt: Date; // Last modification
  appointments: Appointment[]; // All appointments between provider and customer
}
```

#### Folder Management Operations
```typescript
// Get customer folder with full details
const getCustomerFolderDetails = async (customerId: string, providerId: number, context: any) => {
  const folder = await context.entities.CustomerFolder.findFirst({
    where: {
      userId: customerId,
      sProviderId: providerId
    },
    include: {
      customer: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          mobileNumber: true,
          email: true,
          nationalId: true,
          createdAt: true
        }
      },
      appointments: {
        include: {
          service: {
            select: { id: true, title: true, duration: true }
          },
          place: {
            select: { id: true, name: true }
          },
          queue: {
            select: { id: true, title: true }
          }
        },
        orderBy: { expectedAppointmentStartTime: 'desc' }
      }
    }
  });

  if (!folder) {
    throw new HttpError(404, 'Customer folder not found');
  }

  return {
    ...folder,
    appointmentStats: {
      total: folder.appointments.length,
      completed: folder.appointments.filter(apt => apt.status === 'completed').length,
      cancelled: folder.appointments.filter(apt => apt.status === 'canceled').length,
      upcoming: folder.appointments.filter(apt =>
        apt.expectedAppointmentStartTime > new Date() &&
        !['canceled', 'completed', 'noshow'].includes(apt.status)
      ).length
    }
  };
};
```

### Folder Privacy and Security

#### Data Isolation Enforcement
```typescript
// Ensure provider can only access their own customer folders
const enforceProviderFolderAccess = async (providerId: number, customerId: string, context: any) => {
  const hasAccess = await context.entities.CustomerFolder.findFirst({
    where: {
      sProviderId: providerId,
      userId: customerId,
      isActive: true
    },
    select: { id: true }
  });

  if (!hasAccess) {
    throw new HttpError(403, 'Access denied: Customer not found in your folder');
  }

  return true;
};
```

#### Cross-Provider Data Protection
- Customer data is never shared between providers without explicit consent
- Each provider maintains separate notes and appointment history
- Customer profile updates are scoped to provider relationship
- Appointment history is provider-specific and isolated

## Customer Search & Filtering

### Search Implementation

#### Multi-Field Search
```typescript
const searchCustomers = (customers: CustomerResponse[], searchQuery: string) => {
  if (!searchQuery || searchQuery.trim().length === 0) {
    return customers;
  }

  const searchLower = searchQuery.toLowerCase();

  return customers.filter(customer => {
    // Search in first name
    if (customer.firstName.toLowerCase().includes(searchLower)) {
      return true;
    }

    // Search in last name
    if (customer.lastName.toLowerCase().includes(searchLower)) {
      return true;
    }

    // Search in full name
    const fullName = `${customer.firstName} ${customer.lastName}`.toLowerCase();
    if (fullName.includes(searchLower)) {
      return true;
    }

    // Search in mobile number
    if (customer.mobileNumber.includes(searchQuery)) {
      return true;
    }

    // Search in email (if provided)
    if (customer.email && customer.email.toLowerCase().includes(searchLower)) {
      return true;
    }

    // Search in national ID (if provided)
    if (customer.nationalId && customer.nationalId.includes(searchQuery)) {
      return true;
    }

    return false;
  });
};
```

#### Advanced Filtering Options
```typescript
interface CustomerFilters {
  search?: string; // Multi-field search
  hasEmail?: boolean; // Filter customers with/without email
  hasNationalId?: boolean; // Filter customers with/without national ID
  appointmentCount?: {
    min?: number;
    max?: number;
  };
  createdAfter?: Date; // Customers added after date
  createdBefore?: Date; // Customers added before date
  lastAppointmentAfter?: Date; // Last appointment after date
  isActive?: boolean; // Active/inactive relationships
}

const applyCustomerFilters = async (providerId: number, filters: CustomerFilters, context: any) => {
  const whereConditions: any = {
    sProviderId: providerId
  };

  // Active status filter
  if (filters.isActive !== undefined) {
    whereConditions.isActive = filters.isActive;
  }

  // Date range filters
  if (filters.createdAfter || filters.createdBefore) {
    whereConditions.createdAt = {};
    if (filters.createdAfter) {
      whereConditions.createdAt.gte = filters.createdAfter;
    }
    if (filters.createdBefore) {
      whereConditions.createdAt.lte = filters.createdBefore;
    }
  }

  // Customer profile filters
  if (filters.hasEmail !== undefined) {
    whereConditions.customer = {
      email: filters.hasEmail ? { not: null } : null
    };
  }

  if (filters.hasNationalId !== undefined) {
    whereConditions.customer = {
      ...whereConditions.customer,
      nationalId: filters.hasNationalId ? { not: null } : null
    };
  }

  const customerFolders = await context.entities.CustomerFolder.findMany({
    where: whereConditions,
    include: {
      customer: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          mobileNumber: true,
          email: true,
          nationalId: true
        }
      },
      appointments: {
        select: { id: true, expectedAppointmentStartTime: true }
      }
    }
  });

  // Apply appointment count filter
  if (filters.appointmentCount) {
    return customerFolders.filter(folder => {
      const count = folder.appointments.length;
      if (filters.appointmentCount.min && count < filters.appointmentCount.min) {
        return false;
      }
      if (filters.appointmentCount.max && count > filters.appointmentCount.max) {
        return false;
      }
      return true;
    });
  }

  return customerFolders;
};
```

### Sorting and Pagination

#### Sorting Options
```typescript
interface CustomerSortOptions {
  sortBy: 'name' | 'email' | 'createdAt' | 'appointmentCount' | 'lastAppointment';
  sortOrder: 'asc' | 'desc';
}

const sortCustomers = (customers: CustomerResponse[], options: CustomerSortOptions) => {
  return customers.sort((a, b) => {
    let aValue: any, bValue: any;

    switch (options.sortBy) {
      case 'name':
        aValue = `${a.firstName} ${a.lastName}`.toLowerCase();
        bValue = `${b.firstName} ${b.lastName}`.toLowerCase();
        break;
      case 'email':
        aValue = a.email?.toLowerCase() || '';
        bValue = b.email?.toLowerCase() || '';
        break;
      case 'appointmentCount':
        aValue = a.appointmentCount;
        bValue = b.appointmentCount;
        break;
      case 'createdAt':
      default:
        aValue = new Date(a.createdAt).getTime();
        bValue = new Date(b.createdAt).getTime();
        break;
    }

    if (options.sortOrder === 'desc') {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    } else {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    }
  });
};
```

#### Pagination Implementation
```typescript
interface PaginationOptions {
  page: number;
  limit: number;
}

interface PaginatedCustomerResponse {
  customers: CustomerResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

const paginateCustomers = (customers: CustomerResponse[], options: PaginationOptions): PaginatedCustomerResponse => {
  const { page, limit } = options;
  const total = customers.length;
  const totalPages = Math.ceil(total / limit);
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;

  const paginatedCustomers = customers.slice(startIndex, endIndex);

  return {
    customers: paginatedCustomers,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  };
};

## Customer Data Privacy & Security

### Privacy Compliance

#### GDPR and Data Protection
```typescript
interface CustomerDataPrivacy {
  dataRetention: {
    customerProfile: 'indefinite'; // User profile never deleted
    appointmentHistory: 'indefinite'; // Medical/legal requirement
    providerNotes: 'indefinite'; // Provider business records
    communicationHistory: '7_years'; // Legal requirement
  };
  dataAccess: {
    providerScope: 'isolated'; // Each provider sees only their customers
    crossProviderSharing: 'prohibited'; // No data sharing between providers
    customerConsent: 'required'; // Explicit consent for data processing
  };
  dataPortability: {
    exportFormat: 'json' | 'csv'; // Customer data export formats
    includeAppointments: boolean; // Include appointment history
    includeNotes: boolean; // Include provider notes
  };
}
```

#### Data Access Control
```typescript
const enforceDataAccessControl = async (providerId: number, customerId: string, context: any) => {
  // 1. Verify provider-customer relationship exists
  const relationship = await context.entities.CustomerFolder.findFirst({
    where: {
      sProviderId: providerId,
      userId: customerId
    },
    select: { id: true, isActive: true }
  });

  if (!relationship) {
    throw new HttpError(403, 'Access denied: No relationship with this customer');
  }

  // 2. Check if relationship is active
  if (!relationship.isActive) {
    throw new HttpError(403, 'Access denied: Customer relationship is inactive');
  }

  return relationship;
};
```

### Data Anonymization and Deletion

#### Customer Data Export
```typescript
const exportCustomerData = async (customerId: string, providerId: number, context: any) => {
  // Verify access
  await enforceDataAccessControl(providerId, customerId, context);

  const customerData = await context.entities.CustomerFolder.findFirst({
    where: {
      userId: customerId,
      sProviderId: providerId
    },
    include: {
      customer: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          mobileNumber: true,
          email: true,
          nationalId: true,
          createdAt: true
        }
      },
      appointments: {
        include: {
          service: { select: { title: true, duration: true } },
          place: { select: { name: true } },
          queue: { select: { title: true } }
        }
      }
    }
  });

  return {
    exportDate: new Date().toISOString(),
    customer: customerData.customer,
    providerNotes: customerData.notes,
    relationshipCreated: customerData.createdAt,
    appointments: customerData.appointments.map(apt => ({
      id: apt.id,
      service: apt.service.title,
      location: apt.place.name,
      queue: apt.queue?.title,
      scheduledTime: apt.expectedAppointmentStartTime,
      status: apt.status,
      notes: apt.notes
    }))
  };
};
```

#### Data Anonymization
```typescript
const anonymizeCustomerData = async (customerId: string, providerId: number, context: any) => {
  // This would be used for GDPR "right to be forgotten" requests
  // Note: In healthcare, full deletion may not be legally possible

  return await prisma.$transaction(async (tx) => {
    // 1. Anonymize customer profile (keep for legal/medical requirements)
    const anonymizedData = {
      firstName: 'ANONYMIZED',
      lastName: 'CUSTOMER',
      email: null,
      nationalId: null,
      mobileNumber: `ANON_${Date.now()}`
    };

    await tx.user.update({
      where: { id: customerId },
      data: anonymizedData
    });

    // 2. Mark customer folder as anonymized
    await tx.customerFolder.update({
      where: {
        userId: customerId,
        sProviderId: providerId
      },
      data: {
        notes: 'CUSTOMER DATA ANONYMIZED',
        isActive: false
      }
    });

    // 3. Anonymize appointment notes but keep medical records
    await tx.appointment.updateMany({
      where: {
        customerFolder: {
          userId: customerId,
          sProviderId: providerId
        }
      },
      data: {
        notes: 'ANONYMIZED'
      }
    });

    return { success: true, message: 'Customer data anonymized' };
  });
};
```

### Security Measures

#### Input Sanitization
```typescript
const sanitizeCustomerInput = (data: any) => {
  return {
    firstName: sanitizeString(data.firstName),
    lastName: sanitizeString(data.lastName),
    mobileNumber: sanitizePhoneNumber(data.mobileNumber),
    email: sanitizeEmail(data.email),
    nationalId: sanitizeString(data.nationalId),
    notes: sanitizeText(data.notes, 1000) // Max 1000 characters
  };
};

const sanitizeString = (input: string): string => {
  if (!input) return '';
  return input.trim().replace(/[<>]/g, ''); // Remove potential XSS characters
};

const sanitizePhoneNumber = (phone: string): string => {
  if (!phone) return '';
  return phone.replace(/[^\d+\-\s()]/g, ''); // Keep only valid phone characters
};

const sanitizeEmail = (email: string): string => {
  if (!email) return '';
  return email.toLowerCase().trim();
};

const sanitizeText = (text: string, maxLength: number): string => {
  if (!text) return '';
  return text.trim().substring(0, maxLength);
};
```

## Customer Communication Management

### Communication Preferences

#### Notification Settings
```typescript
interface CustomerCommunicationPreferences {
  appointmentReminders: {
    enabled: boolean;
    timing: '24h' | '2h' | '30min' | 'all';
    method: 'sms' | 'email' | 'push' | 'all';
  };
  appointmentUpdates: {
    enabled: boolean;
    method: 'sms' | 'email' | 'push' | 'all';
  };
  promotionalMessages: {
    enabled: boolean;
    method: 'sms' | 'email' | 'push';
  };
  queueUpdates: {
    enabled: boolean;
    method: 'push' | 'sms';
  };
}
```

#### Communication History Tracking
```typescript
const trackCommunication = async (customerId: string, providerId: number, communication: any, context: any) => {
  await context.entities.CommunicationLog.create({
    data: {
      customerId: customerId,
      providerId: providerId,
      type: communication.type, // 'sms', 'email', 'push'
      subject: communication.subject,
      content: communication.content,
      status: communication.status, // 'sent', 'delivered', 'failed'
      sentAt: new Date(),
      metadata: communication.metadata
    }
  });
};
```

### Customer Notification Integration

#### Appointment Notifications
```typescript
const sendAppointmentNotification = async (appointmentId: number, type: string, context: any) => {
  const appointment = await context.entities.Appointment.findUnique({
    where: { id: appointmentId },
    include: {
      customerFolder: {
        include: {
          customer: {
            select: { id: true, firstName: true, mobileNumber: true, email: true }
          },
          provider: {
            include: {
              user: { select: { firstName: true, lastName: true } }
            }
          }
        }
      },
      service: { select: { title: true } },
      place: { select: { name: true } }
    }
  });

  if (!appointment) return;

  const customer = appointment.customerFolder.customer;
  const provider = appointment.customerFolder.provider;

  const notificationData = {
    customerId: customer.id,
    providerId: provider.id,
    type: type,
    title: getNotificationTitle(type),
    message: formatNotificationMessage(type, {
      customerName: customer.firstName,
      providerName: `${provider.user.firstName} ${provider.user.lastName}`,
      serviceName: appointment.service.title,
      locationName: appointment.place.name,
      appointmentTime: appointment.expectedAppointmentStartTime
    }),
    appointmentId: appointmentId
  };

  // Send via multiple channels based on customer preferences
  await sendMultiChannelNotification(notificationData, context);
};
```

#### Queue Status Updates
```typescript
const sendQueueStatusUpdate = async (customerId: string, queueData: any, context: any) => {
  const notification = {
    type: 'queue_update',
    title: 'Queue Status Update',
    message: `Your position in queue: ${queueData.position}. Estimated wait time: ${queueData.estimatedWaitMinutes} minutes.`,
    data: {
      queueId: queueData.queueId,
      position: queueData.position,
      estimatedWaitMinutes: queueData.estimatedWaitMinutes
    }
  };

  await sendPushNotification(customerId, notification, context);
};
```

## Error Handling

### HTTP Status Codes

#### Success Responses
- **200 OK**: Successful GET, PUT operations
- **201 Created**: Successful POST operations (customer creation)
- **204 No Content**: Successful DELETE operations

#### Client Error Responses
- **400 Bad Request**: Invalid request data, validation errors
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Access denied, customer not in provider's folder
- **404 Not Found**: Customer not found
- **409 Conflict**: Duplicate customer data (mobile, email, nationalId)
- **422 Unprocessable Entity**: Business logic validation failures

#### Server Error Responses
- **500 Internal Server Error**: Unexpected server errors
- **503 Service Unavailable**: External service unavailable

### Error Response Format
```typescript
interface CustomerErrorResponse {
  success: false;
  message: string;
  code?: string;
  statusCode: number;
  details?: {
    field?: string;
    value?: any;
    constraint?: string;
    customerId?: string;
    providerId?: number;
  };
  timestamp: Date;
  path: string;
}
```

### Common Error Scenarios

#### Duplicate Customer Data
```typescript
// Duplicate mobile number error
{
  "success": false,
  "message": "A user with this mobile number already exists (concurrent creation?). Please try again.",
  "statusCode": 409,
  "code": "DUPLICATE_MOBILE_NUMBER",
  "details": {
    "field": "mobileNumber",
    "value": "+************",
    "constraint": "unique"
  }
}
```

#### Access Denied Errors
```typescript
// Customer not in provider's folder
{
  "success": false,
  "message": "Customer not found or you do not have permission to access this customer",
  "statusCode": 404,
  "code": "CUSTOMER_ACCESS_DENIED",
  "details": {
    "customerId": "customer-uuid-123",
    "providerId": 5
  }
}
```

#### Validation Errors
```typescript
// Invalid customer data
{
  "success": false,
  "message": "Customer validation failed",
  "statusCode": 400,
  "code": "VALIDATION_ERROR",
  "details": {
    "errors": [
      {
        "field": "firstName",
        "message": "First name is required",
        "value": ""
      },
      {
        "field": "email",
        "message": "Invalid email format",
        "value": "invalid-email"
      }
    ]
  }
}
```

### Error Handling Patterns

#### Try-Catch with HttpError
```typescript
export const createProviderCustomer = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Validate authentication
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Validate request data
    const customerData = validateAndExtract(createCustomerSchema, req.body);

    // Sanitize input data
    const sanitizedData = sanitizeCustomerInput(customerData);

    // Call operation
    const newCustomerFolder = await createProviderCustomerOp(sanitizedData, context);

    return sendCreated(res, newCustomerFolder, 'Customer created successfully');

  } catch (error: any) {
    console.error('[createProviderCustomer] Error:', error);

    if (error instanceof HttpError) {
      return sendError(res, {
        statusCode: error.statusCode,
        message: error.message,
        code: error.code
      });
    }

    return sendError(res, error, 'Failed to create customer');
  }
});
```

---

## API Reference Summary

### Complete Endpoint List

#### Provider Customer Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/auth/providers/customers` | Get all provider customers | Yes |
| GET | `/api/auth/providers/customers/:id` | Get specific customer | Yes |
| POST | `/api/auth/providers/customers` | Create new customer | Yes |
| PUT | `/api/auth/providers/customers/:id` | Update customer | Yes |

#### Customer Search and Filtering
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/auth/providers/customers?search=query` | Search customers | Yes |
| GET | `/api/auth/providers/customers?page=1&limit=20` | Paginated results | Yes |
| GET | `/api/auth/providers/customers?sortBy=name&sortOrder=asc` | Sorted results | Yes |

### Request/Response Examples

#### Create Customer Request
```json
POST /api/auth/providers/customers
{
  "firstName": "John",
  "lastName": "Doe",
  "mobileNumber": "+************",
  "email": "<EMAIL>",
  "nationalId": "1234567890123",
  "notes": "Regular customer, prefers morning appointments"
}
```

#### Create Customer Response
```json
{
  "success": true,
  "data": {
    "id": "customer-uuid-123",
    "firstName": "John",
    "lastName": "Doe",
    "mobileNumber": "+************",
    "email": "<EMAIL>",
    "nationalId": "1234567890123",
    "notes": "Regular customer, prefers morning appointments",
    "appointmentCount": 0,
    "createdAt": "2024-01-15T10:00:00Z"
  },
  "message": "Customer created successfully"
}
```

#### Update Customer Request
```json
PUT /api/auth/providers/customers/customer-uuid-123
{
  "email": "<EMAIL>",
  "notes": "Updated notes: Customer prefers afternoon appointments now"
}
```

#### Get Provider Customers Response
```json
{
  "success": true,
  "data": [
    {
      "id": "customer-uuid-123",
      "firstName": "John",
      "lastName": "Doe",
      "mobileNumber": "+************",
      "email": "<EMAIL>",
      "nationalId": "1234567890123",
      "notes": "Updated notes: Customer prefers afternoon appointments now",
      "appointmentCount": 5,
      "createdAt": "2024-01-15T10:00:00Z"
    },
    {
      "id": "customer-uuid-456",
      "firstName": "Jane",
      "lastName": "Smith",
      "mobileNumber": "+************",
      "email": "<EMAIL>",
      "nationalId": null,
      "notes": null,
      "appointmentCount": 2,
      "createdAt": "2024-01-20T14:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 2,
    "totalPages": 1,
    "hasNext": false,
    "hasPrev": false
  }
}
```

#### Search Customers Response
```json
{
  "success": true,
  "data": [
    {
      "id": "customer-uuid-123",
      "firstName": "John",
      "lastName": "Doe",
      "mobileNumber": "+************",
      "email": "<EMAIL>",
      "nationalId": "1234567890123",
      "notes": "Updated notes: Customer prefers afternoon appointments now",
      "appointmentCount": 5,
      "createdAt": "2024-01-15T10:00:00Z"
    }
  ],
  "searchQuery": "john",
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "totalPages": 1,
    "hasNext": false,
    "hasPrev": false
  }
}
```

---

## Implementation Notes

### Performance Considerations
- **Database Indexing**: Index on `sProviderId`, `userId`, and search fields
- **Search Optimization**: Implement full-text search for large customer bases
- **Pagination**: Always implement pagination for customer lists
- **Caching**: Cache frequently accessed customer data

### Security Best Practices
- **Input Validation**: Comprehensive validation using Zod schemas
- **Data Sanitization**: Sanitize all user inputs to prevent XSS
- **Access Control**: Strict provider-customer relationship verification
- **Privacy Compliance**: GDPR-compliant data handling and retention

### Monitoring & Observability
- **Customer Analytics**: Track customer acquisition and retention metrics
- **Search Performance**: Monitor search query performance and optimization
- **Data Quality**: Monitor data completeness and accuracy
- **Privacy Compliance**: Audit data access and retention policies

### Testing Strategy
- **Unit Tests**: Test customer CRUD operations and validation logic
- **Integration Tests**: Test complete customer management workflows
- **Privacy Tests**: Test data isolation and access control
- **Performance Tests**: Test search and filtering performance with large datasets

This documentation provides a comprehensive overview of the Provider Customer Management system, covering all aspects from database schema to API implementation, business rules, privacy compliance, and technical considerations. The system ensures strict data isolation between providers while maintaining comprehensive customer relationship management capabilities.
```
