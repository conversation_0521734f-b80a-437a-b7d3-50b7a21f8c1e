apiVersion: v1
kind: Service
metadata:
  name: dalti-frontend-service
  namespace: dalti
  labels:
    app: dalti-frontend
spec:
  selector:
    app: dalti-frontend # Selects pods from the dalti-frontend Deployment
  ports:
  - name: http
    port: 80 # Service listens on port 80
    protocol: TCP
    targetPort: 80 # Forwards traffic to Nginx container port 80
  type: ClusterIP # Internal service, exposed via Ingress 