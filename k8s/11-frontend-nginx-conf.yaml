apiVersion: v1
kind: ConfigMap
metadata:
  name: dalti-frontend-nginx-conf
  namespace: dalti
data:
  nginx.conf: |
    server {
        listen 80;
        server_name dalti.adscloud.org; # Optional: Define server name

        root /usr/share/nginx/html;
        index index.html index.htm;

        location / {
            # Try to serve file directly, then directory, then fallback to index.html
            try_files $uri $uri/ /index.html;
        }

        # Optional: Add caching headers for static assets
        location ~* \.(?:css|js|jpg|jpeg|gif|png|ico|svg|webp|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public";
            access_log off;
        }

        # Optional: Disable access logging for health checks if needed
        # location = /nginx-health {
        #     access_log off;
        #     return 200 "healthy\n";
        # }

        # Deny access to hidden files
        location ~ /\. {
            deny all;
        }
    } 