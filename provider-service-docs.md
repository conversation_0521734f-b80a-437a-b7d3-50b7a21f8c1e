# Provider Services Documentation

## Table of Contents
1. [Service Management Overview](#service-management-overview)
2. [Database Schema](#database-schema)
3. [API Endpoints](#api-endpoints)
4. [Service Categories](#service-categories)
5. [Business Rules](#business-rules)
6. [Mobile API Integration](#mobile-api-integration)
7. [Code Examples](#code-examples)
8. [Error Handling](#error-handling)

## Service Management Overview

### What are Provider Services?
Provider Services represent the specific offerings that service providers make available to customers through the platform. Each service defines what a provider can deliver, including duration, pricing, delivery methods, and booking requirements.

### Purpose and Functionality
- **Service Catalog**: Define what services a provider offers
- **Pricing Management**: Set prices and credit requirements for each service
- **Delivery Options**: Specify where services can be delivered (location, customer site, or both)
- **Booking Control**: Manage online booking availability and new customer acceptance
- **Scheduling Integration**: Connect services to queues for appointment scheduling
- **Regional Coverage**: Define geographic areas served for customer-location services

### Relationships to Other Entities

#### Provider → Services (1:N)
- Each provider can offer multiple services
- Services belong to exactly one provider
- Provider ID is required for all service operations

#### Service → ServiceCategory (N:1)
- Services can be organized into categories
- Categories are provider-specific
- Optional relationship (services can exist without categories)

#### Service → Queue (N:N)
- Services can be offered through multiple queues
- Queues can handle multiple services
- Many-to-many relationship via `ServiceQueues` relation

#### Service → Appointments (1:N)
- Services are booked through appointments
- Each appointment references one service
- Service duration affects appointment scheduling

## Database Schema

### Service Model
```sql
model Service {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  -- Basic Information
  title       String   -- Service name/title
  color       String?  -- UI color for display
  description String?  @db.Text -- Detailed service description

  -- Provider Relationship
  provider    SProvider @relation(fields: [sProviderId], references: [id])
  sProviderId Int

  -- Category Relationship (Optional)
  category          ServiceCategory? @relation(fields: [serviceCategoryId], references: [id])
  serviceCategoryId Int?

  -- Duration and Scheduling
  duration    Int      -- Service duration in minutes
  minDuration Int?     -- Minimum duration (optional)
  maxDuration Int?     -- Maximum duration (optional)
  queue       Int?     -- Legacy queue reference

  -- Booking Settings
  acceptOnline       Boolean @default(true)  -- Allow online booking
  acceptNew          Boolean @default(true)  -- Accept new customers
  notificationOn     Boolean @default(true)  -- Enable notifications
  pointsRequirements Int     @default(1)     -- Credits required to book

  -- Pricing and Delivery
  price         Float?   -- Service price (optional)
  isPublic      Boolean  @default(true)      -- Public visibility
  deliveryType  String?  -- "at_location", "at_customer", "both"
  servedRegions String?  @db.Text            -- JSON array of region IDs

  -- Relationships
  appointments Appointment[]
  queues       Queue[]      @relation("ServiceQueues")
}
```

### ServiceCategory Model
```sql
model ServiceCategory {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  title String -- Category name

  -- Provider Relationship
  provider    SProvider @relation(fields: [sProviderId], references: [id])
  sProviderId Int

  -- Services in this category
  services Service[]

  -- Unique constraint: category title per provider
  @@unique([title, sProviderId])
}
```

### Key Fields and Constraints

#### Required Fields
- `title`: Service name (must be non-empty)
- `duration`: Duration in minutes (must be positive)
- `sProviderId`: Provider ownership (foreign key)
- `pointsRequirements`: Credit requirements (default: 1)

#### Optional Fields
- `price`: Service pricing (can be null for free services)
- `description`: Detailed service information
- `serviceCategoryId`: Category assignment
- `servedRegions`: Geographic coverage (JSON array)

#### Default Values
- `acceptOnline`: true (online booking enabled)
- `acceptNew`: true (new customers accepted)
- `notificationOn`: true (notifications enabled)
- `pointsRequirements`: 1 (minimum credit requirement)
- `isPublic`: true (publicly visible)

#### Constraints
- `duration` must be positive integer
- `price` must be non-negative if provided
- `pointsRequirements` must be non-negative
- `deliveryType` must be one of: "at_location", "at_customer", "both"
- `color` must be valid hex color format (#RRGGBB)

## API Endpoints

### Base URL
- **Development**: `https://dapi-test.adscloud.org:8443`
- **Production**: `https://dapi.adscloud.org`

### Authentication
All service endpoints require authentication:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### Service CRUD Operations

#### 1. Get All Provider Services
**Endpoint:** `GET /api/auth/providers/services`

**Description:** Retrieve all services for the authenticated provider.

**Request:**
```http
GET /api/auth/providers/services
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "General Consultation",
      "duration": 30,
      "price": 2500.0,
      "pointsRequirements": 1,
      "isPublic": true,
      "deliveryType": "at_location",
      "servedRegions": [],
      "description": "Standard medical consultation",
      "color": "#4CAF50",
      "acceptOnline": true,
      "acceptNew": true,
      "notificationOn": true,
      "createdAt": "2024-01-15T10:00:00Z",
      "updatedAt": "2024-01-15T10:00:00Z"
    }
  ],
  "message": "Provider services retrieved successfully"
}
```

#### 2. Get Single Service
**Endpoint:** `GET /api/auth/providers/services/:id`

**Description:** Retrieve a specific service by ID.

**Request:**
```http
GET /api/auth/providers/services/1
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "General Consultation",
    "duration": 30,
    "price": 2500.0,
    "pointsRequirements": 1,
    "isPublic": true,
    "deliveryType": "at_location",
    "servedRegions": [],
    "description": "Standard medical consultation",
    "color": "#4CAF50",
    "acceptOnline": true,
    "acceptNew": true,
    "notificationOn": true
  },
  "message": "Provider service retrieved successfully"
}
```

#### 3. Create New Service
**Endpoint:** `POST /api/auth/providers/services`

**Description:** Create a new service for the authenticated provider.

**Request:**
```http
POST /api/auth/providers/services
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "title": "Home Visit Consultation",
  "duration": 60,
  "price": 5000.0,
  "pointsRequirements": 2,
  "isPublic": true,
  "deliveryType": "at_customer",
  "servedRegions": ["16", "31", "42"],
  "description": "Medical consultation at patient's home",
  "color": "#2196F3",
  "acceptOnline": true,
  "acceptNew": true,
  "notificationOn": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "title": "Home Visit Consultation",
    "duration": 60,
    "price": 5000.0,
    "pointsRequirements": 2,
    "isPublic": true,
    "deliveryType": "at_customer",
    "servedRegions": ["16", "31", "42"],
    "description": "Medical consultation at patient's home",
    "color": "#2196F3",
    "acceptOnline": true,
    "acceptNew": true,
    "notificationOn": true,
    "createdAt": "2024-01-15T11:00:00Z",
    "updatedAt": "2024-01-15T11:00:00Z"
  },
  "message": "Provider service created successfully"
}
```

#### 4. Update Service
**Endpoint:** `PUT /api/auth/providers/services/:id`

**Description:** Update an existing service.

**Request:**
```http
PUT /api/auth/providers/services/1
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "title": "Extended Consultation",
  "duration": 45,
  "price": 3000.0,
  "description": "Extended medical consultation with detailed examination"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "Extended Consultation",
    "duration": 45,
    "price": 3000.0,
    "pointsRequirements": 1,
    "isPublic": true,
    "deliveryType": "at_location",
    "servedRegions": [],
    "description": "Extended medical consultation with detailed examination",
    "color": "#4CAF50",
    "acceptOnline": true,
    "acceptNew": true,
    "notificationOn": true,
    "updatedAt": "2024-01-15T12:00:00Z"
  },
  "message": "Provider service updated successfully"
}
```

#### 5. Delete Service
**Endpoint:** `DELETE /api/auth/providers/services/:id`

**Description:** Delete a service (if no active appointments exist).

**Request:**
```http
DELETE /api/auth/providers/services/1
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "message": "Provider service deleted successfully"
}
```

### Service Categories

#### Service Category Endpoints

**Get Categories:** `GET /api/auth/providers/service-categories`
**Create Category:** `POST /api/auth/providers/service-categories`
**Update Category:** `PUT /api/auth/providers/service-categories/:id`
**Delete Category:** `DELETE /api/auth/providers/service-categories/:id`

#### Create Service Category Example
**Request:**
```http
POST /api/auth/providers/service-categories
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "title": "Medical Consultations"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "Medical Consultations",
    "sProviderId": 123
  },
  "message": "Service category created successfully"
}
```

### Queue Service Assignment

#### Assign Service to Queue
**Endpoint:** `POST /api/auth/providers/queues/:queueId/services`

**Request:**
```http
POST /api/auth/providers/queues/1/services
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "serviceId": 1
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "queueId": 1,
    "serviceId": 1,
    "service": {
      "id": 1,
      "title": "General Consultation",
      "duration": 30,
      "color": "#4CAF50"
    }
  },
  "message": "Service assigned to queue successfully"
}
```

#### Remove Service from Queue
**Endpoint:** `DELETE /api/auth/providers/queues/:queueId/services/:serviceId`

**Response:**
```json
{
  "success": true,
  "message": "Service removed from queue successfully"
}
```

## Service Categories

### Purpose and Structure
Service categories help providers organize their service offerings into logical groups for better management and customer navigation.

### Category Features
- **Provider-Specific**: Each provider has their own categories
- **Unique Names**: Category titles must be unique per provider
- **Optional Assignment**: Services can exist without categories
- **Hierarchical Display**: Categories can be used for UI organization

### Category Management
```typescript
// Category structure
interface ServiceCategory {
  id: number;
  title: string;
  sProviderId: number;
  services: Service[];
}

// Common category examples
const commonCategories = [
  "Medical Consultations",
  "Diagnostic Services", 
  "Therapeutic Treatments",
  "Emergency Services",
  "Preventive Care"
];
```

### Business Rules for Categories
- Category names must be unique per provider
- Categories can be deleted only if no services are assigned
- Category assignment is optional for services
- Categories are automatically created during service creation if specified

## Business Rules

### Service Validation Rules

#### Required Fields
```typescript
const serviceValidation = {
  title: {
    required: true,
    minLength: 1,
    maxLength: 255
  },
  duration: {
    required: true,
    type: 'integer',
    minimum: 1,
    maximum: 1440 // 24 hours max
  },
  pointsRequirements: {
    required: true,
    type: 'integer',
    minimum: 0,
    default: 1
  }
};
```

#### Optional Fields with Validation
```typescript
const optionalValidation = {
  price: {
    type: 'number',
    minimum: 0,
    maximum: 999999.99
  },
  color: {
    pattern: /^#[0-9A-Fa-f]{6}$/,
    default: '#000000'
  },
  deliveryType: {
    enum: ['at_location', 'at_customer', 'both'],
    default: 'at_location'
  },
  servedRegions: {
    type: 'array',
    items: { type: 'string' },
    nullable: true
  }
};
```

### Delivery Type Rules

#### "at_location" Services
- Service provided at provider's location
- No geographic restrictions
- `servedRegions` should be empty or null

#### "at_customer" Services  
- Service provided at customer's location
- Requires `servedRegions` specification
- Geographic validation for booking

#### "both" Services
- Service available at either location
- `servedRegions` applies only to customer-location bookings
- Flexible booking options

### Credit System Rules
- `pointsRequirements` determines booking cost
- Customers must have sufficient credits
- Credits are deducted upon booking confirmation
- Minimum requirement is 0 (free services allowed)

### Booking Control Rules
- `acceptOnline`: Controls online booking availability
- `acceptNew`: Controls new customer acceptance
- `isPublic`: Controls service visibility in public listings
- `notificationOn`: Controls notification preferences

## Mobile API Integration

### Service Management in Mobile Apps

#### Flutter/React Native Service Class
```typescript
class ProviderServiceAPI {
  private baseUrl: string;
  private authToken: string;

  constructor(baseUrl: string, authToken: string) {
    this.baseUrl = baseUrl;
    this.authToken = authToken;
  }

  async getServices(): Promise<ServiceResponse[]> {
    const response = await fetch(`${this.baseUrl}/api/auth/providers/services`, {
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.message);
    }

    return result.data;
  }

  async createService(serviceData: CreateServiceRequest): Promise<ServiceResponse> {
    const response = await fetch(`${this.baseUrl}/api/auth/providers/services`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(serviceData)
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.message);
    }

    return result.data;
  }

  async updateService(serviceId: number, updateData: Partial<CreateServiceRequest>): Promise<ServiceResponse> {
    const response = await fetch(`${this.baseUrl}/api/auth/providers/services/${serviceId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateData)
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.message);
    }

    return result.data;
  }

  async deleteService(serviceId: number): Promise<void> {
    const response = await fetch(`${this.baseUrl}/api/auth/providers/services/${serviceId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.message);
    }
  }
}
```

#### State Management Integration
```typescript
// Redux/Context state for services
interface ServiceState {
  services: ServiceResponse[];
  categories: ServiceCategoryResponse[];
  loading: boolean;
  error: string | null;
}

// Service actions
const serviceActions = {
  fetchServices: async (dispatch: Dispatch) => {
    dispatch({ type: 'SERVICES_LOADING' });
    try {
      const services = await serviceAPI.getServices();
      dispatch({ type: 'SERVICES_SUCCESS', payload: services });
    } catch (error) {
      dispatch({ type: 'SERVICES_ERROR', payload: error.message });
    }
  },

  createService: async (dispatch: Dispatch, serviceData: CreateServiceRequest) => {
    try {
      const newService = await serviceAPI.createService(serviceData);
      dispatch({ type: 'SERVICE_CREATED', payload: newService });
      return newService;
    } catch (error) {
      dispatch({ type: 'SERVICES_ERROR', payload: error.message });
      throw error;
    }
  }
};
```

## Code Examples

### TypeScript Interfaces

#### Core Service Types
```typescript
// Main service interface
export interface ServiceResponse {
  id: number;
  title: string;
  duration: number;
  price: number;
  pointsRequirements: number;
  isPublic: boolean;
  deliveryType: string;
  servedRegions: string[];
  description?: string;
  color: string;
  acceptOnline: boolean;
  acceptNew: boolean;
  notificationOn: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// Service creation request
export interface CreateServiceRequest {
  title: string;
  duration: number;
  price: number;
  pointsRequirements?: number;
  isPublic?: boolean;
  deliveryType: 'at_location' | 'at_customer' | 'both';
  servedRegions?: string[] | null;
  description?: string;
  color: string;
  acceptOnline?: boolean;
  acceptNew?: boolean;
  notificationOn?: boolean;
}

// Service update request
export interface UpdateServiceRequest extends Partial<CreateServiceRequest> {}

// Service category interfaces
export interface ServiceCategoryResponse {
  id: number;
  title: string;
  sProviderId: number;
}

export interface CreateServiceCategoryRequest {
  title: string;
}
```

#### Validation Schemas
```typescript
import { z } from 'zod';

// Service validation schema
export const createServiceSchema = z.object({
  title: z.string().min(1, 'Service title is required').max(255, 'Title too long'),
  duration: z.number().int().min(1, 'Duration must be at least 1 minute').max(1440, 'Duration cannot exceed 24 hours'),
  price: z.number().min(0, 'Price cannot be negative'),
  pointsRequirements: z.number().int().min(0, 'Points requirements cannot be negative').default(1),
  isPublic: z.boolean().default(true),
  deliveryType: z.enum(['at_location', 'at_customer', 'both']),
  servedRegions: z.array(z.string()).optional().nullable(),
  description: z.string().max(1000, 'Description too long').optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Color must be a valid hex color'),
  acceptOnline: z.boolean().default(true),
  acceptNew: z.boolean().default(true),
  notificationOn: z.boolean().default(true)
});

// Service category validation schema
export const createServiceCategorySchema = z.object({
  title: z.string().min(1, 'Category title is required').max(255, 'Title too long')
});
```

### React Component Examples

#### Service List Component
```tsx
import React, { useState, useEffect } from 'react';
import { ServiceResponse } from '../types';

interface ServiceListProps {
  onEditService: (service: ServiceResponse) => void;
  onDeleteService: (serviceId: number) => void;
}

const ServiceList: React.FC<ServiceListProps> = ({ onEditService, onDeleteService }) => {
  const [services, setServices] = useState<ServiceResponse[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchServices();
  }, []);

  const fetchServices = async () => {
    try {
      const response = await fetch('/api/auth/providers/services', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });
      
      const result = await response.json();
      if (result.success) {
        setServices(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch services:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading services...</div>;

  return (
    <div className="service-list">
      {services.map(service => (
        <div key={service.id} className="service-card">
          <div className="service-header">
            <h3>{service.title}</h3>
            <span 
              className="service-color" 
              style={{ backgroundColor: service.color }}
            />
          </div>
          
          <div className="service-details">
            <p>Duration: {service.duration} minutes</p>
            <p>Price: ${service.price}</p>
            <p>Credits: {service.pointsRequirements}</p>
            <p>Delivery: {service.deliveryType}</p>
          </div>
          
          <div className="service-actions">
            <button onClick={() => onEditService(service)}>
              Edit
            </button>
            <button 
              onClick={() => onDeleteService(service.id)}
              className="delete-btn"
            >
              Delete
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};
```

#### Service Form Component
```tsx
import React, { useState } from 'react';
import { CreateServiceRequest } from '../types';

interface ServiceFormProps {
  initialData?: Partial<CreateServiceRequest>;
  onSubmit: (data: CreateServiceRequest) => Promise<void>;
  onCancel: () => void;
}

const ServiceForm: React.FC<ServiceFormProps> = ({ initialData, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState<CreateServiceRequest>({
    title: '',
    duration: 30,
    price: 0,
    pointsRequirements: 1,
    isPublic: true,
    deliveryType: 'at_location',
    servedRegions: [],
    description: '',
    color: '#4CAF50',
    acceptOnline: true,
    acceptNew: true,
    notificationOn: true,
    ...initialData
  });

  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Failed to save service:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="service-form">
      <div className="form-group">
        <label htmlFor="title">Service Title *</label>
        <input
          id="title"
          type="text"
          value={formData.title}
          onChange={(e) => setFormData({ ...formData, title: e.target.value })}
          required
        />
      </div>

      <div className="form-group">
        <label htmlFor="duration">Duration (minutes) *</label>
        <input
          id="duration"
          type="number"
          min="1"
          max="1440"
          value={formData.duration}
          onChange={(e) => setFormData({ ...formData, duration: parseInt(e.target.value) })}
          required
        />
      </div>

      <div className="form-group">
        <label htmlFor="price">Price</label>
        <input
          id="price"
          type="number"
          min="0"
          step="0.01"
          value={formData.price}
          onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) })}
        />
      </div>

      <div className="form-group">
        <label htmlFor="deliveryType">Delivery Type *</label>
        <select
          id="deliveryType"
          value={formData.deliveryType}
          onChange={(e) => setFormData({ ...formData, deliveryType: e.target.value as any })}
          required
        >
          <option value="at_location">At Provider Location</option>
          <option value="at_customer">At Customer Location</option>
          <option value="both">Both Locations</option>
        </select>
      </div>

      <div className="form-group">
        <label htmlFor="description">Description</label>
        <textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          rows={3}
        />
      </div>

      <div className="form-actions">
        <button type="button" onClick={onCancel} disabled={loading}>
          Cancel
        </button>
        <button type="submit" disabled={loading}>
          {loading ? 'Saving...' : 'Save Service'}
        </button>
      </div>
    </form>
  );
};
```

## Error Handling

### Common Error Scenarios

#### 1. Authentication Errors
**Status Code:** 401
```json
{
  "success": false,
  "message": "Authentication required"
}
```

**Resolution:** Ensure valid JWT token is provided in Authorization header.

#### 2. Service Not Found
**Status Code:** 404
```json
{
  "success": false,
  "message": "Service not found or you do not have permission to access it"
}
```

**Resolution:** Verify service ID exists and belongs to authenticated provider.

#### 3. Validation Errors
**Status Code:** 400
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "title",
      "message": "Service title is required"
    },
    {
      "field": "duration",
      "message": "Duration must be at least 1 minute"
    }
  ]
}
```

**Resolution:** Fix validation errors in request data according to business rules.

#### 4. Deletion Conflicts
**Status Code:** 409
```json
{
  "success": false,
  "message": "Cannot delete service with active appointments"
}
```

**Resolution:** Cancel or complete all appointments before deleting service.

#### 5. Category Conflicts
**Status Code:** 409
```json
{
  "success": false,
  "message": "Service category with this title already exists"
}
```

**Resolution:** Use unique category names per provider.

### Error Handling Best Practices

#### Client-Side Error Handling
```typescript
const handleServiceOperation = async (operation: () => Promise<any>) => {
  try {
    const result = await operation();
    return result;
  } catch (error: any) {
    if (error.status === 401) {
      // Redirect to login
      redirectToLogin();
    } else if (error.status === 400) {
      // Show validation errors
      showValidationErrors(error.errors);
    } else if (error.status === 409) {
      // Show conflict message
      showConflictMessage(error.message);
    } else {
      // Show generic error
      showGenericError('An unexpected error occurred');
    }
    throw error;
  }
};
```

#### Retry Logic for Network Errors
```typescript
const retryServiceRequest = async (
  request: () => Promise<any>,
  maxRetries: number = 3
): Promise<any> => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await request();
    } catch (error: any) {
      if (attempt === maxRetries || error.status < 500) {
        throw error;
      }
      
      // Exponential backoff
      await new Promise(resolve => 
        setTimeout(resolve, Math.pow(2, attempt) * 1000)
      );
    }
  }
};
```

---

*This comprehensive documentation covers all aspects of the Provider Services feature, providing developers with complete information for implementing service management functionality in mobile applications.*
