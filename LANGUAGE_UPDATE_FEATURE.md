# Language Update Feature Implementation

## Overview
This document describes the implementation of the preferred language update feature that synchronizes user language preferences with the backend when users switch languages using the language switcher.

## Backend API Integration

### Endpoint Details
- **Endpoint**: `POST /api/auth/user/update-prefered-language`
- **Authentication**: Required (Bearer token)
- **Supported Languages**: EN, AR, FR (uppercase format)
- **Request Body**: 
  ```json
  {
    "preferedLanguage": "EN" | "AR" | "FR"
  }
  ```

### Response Format
```json
{
  "success": true,
  "message": "Language preference updated successfully",
  "data": {
    "preferedLanguage": "EN"
  }
}
```

## Implementation Details

### 1. Configuration Update
**File**: `src/lib/config.ts`
- Added new endpoint: `updatePreferredLanguage: '/api/auth/user/update-prefered-language'`

### 2. Service Layer
**File**: `src/services/user.service.ts`
- Added `updatePreferredLanguage()` method
- Handles language code conversion (lowercase to uppercase)
- Proper error handling and response validation
- Type definitions for request/response

**Key Features**:
- Converts frontend language codes ('en', 'fr', 'ar') to backend format ('EN', 'FR', 'AR')
- Comprehensive error handling
- TypeScript type safety

### 3. React Query Hook
**File**: `src/hooks/useUser.ts`
- Added `useUpdatePreferredLanguage()` hook
- Uses React Query mutation for optimal caching and error handling
- Silent success (no toast) to avoid interrupting language switch UX
- Error logging and user-friendly error messages

### 4. Language Context Integration
**File**: `src/context/LanguageContext.tsx`
- Modified `changeLanguage()` function to call backend API
- Fire-and-forget approach - doesn't block UI updates
- Graceful error handling - logs errors but doesn't interrupt UX
- Maintains existing functionality while adding backend sync

**Implementation Strategy**:
1. Update UI immediately for responsive UX
2. Sync with backend asynchronously
3. Log errors without showing to user (to avoid interrupting language switch)

### 5. Test Implementation
**File**: `src/pages/TestLanguageUpdate.tsx`
- Comprehensive test page for all functionality
- Tests three approaches:
  1. Full language context change (recommended)
  2. Direct API service call
  3. React Query mutation hook
- Real-time status monitoring
- Error handling verification

**Route**: `/test-language-update`

## User Experience Flow

1. **User clicks language switcher**
2. **Immediate UI update** - Language changes instantly
3. **Background sync** - API call made to backend
4. **Error handling** - If sync fails, error is logged but user isn't interrupted
5. **Local storage** - Language preference saved locally as fallback

## Error Handling Strategy

### Silent Failures
- Backend sync failures don't interrupt language switching
- Errors are logged for debugging but not shown to users
- Local storage serves as fallback for language preference

### Logged Errors
- Network failures
- Authentication issues
- Invalid language codes
- Server errors

## Testing

### Manual Testing
1. Navigate to `/test-language-update`
2. Test all three implementation approaches
3. Verify API calls in browser network tab
4. Test error scenarios (network offline, invalid auth)

### Unit Tests
**File**: `src/services/__tests__/user.service.test.ts`
- Tests language code conversion
- Tests API success scenarios
- Tests error handling
- Tests all supported languages (EN, AR, FR)

## Integration Points

### Existing Components
- **LanguageSwitcher**: Automatically uses new backend sync
- **LanguageContext**: Enhanced with backend integration
- **All language-dependent components**: Benefit from persistent preferences

### Authentication
- Requires valid JWT token
- Uses existing authentication infrastructure
- Respects session management

## Performance Considerations

### Optimizations
- **Fire-and-forget**: Backend sync doesn't block UI
- **Local storage fallback**: Immediate language switching
- **React Query caching**: Efficient API call management
- **Silent errors**: No interruption to user experience

### Network Efficiency
- Single API call per language change
- No polling or continuous sync
- Minimal payload size

## Future Enhancements

### Potential Improvements
1. **Retry mechanism**: Retry failed sync attempts
2. **Offline support**: Queue language changes when offline
3. **Bulk sync**: Sync multiple preference changes together
4. **User notification**: Optional success confirmation

### Analytics Integration
- Track language switching patterns
- Monitor sync success rates
- User preference analytics

## Deployment Notes

### Environment Variables
- No new environment variables required
- Uses existing API base URL and authentication

### Database Considerations
- Backend should handle user preference storage
- Consider indexing on user language preferences for analytics

### Monitoring
- Monitor API endpoint success rates
- Track language switching frequency
- Log sync failures for debugging

## Security Considerations

### Authentication
- All API calls require valid JWT token
- Respects existing session management
- No additional authentication required

### Data Validation
- Language codes validated on both frontend and backend
- Type safety with TypeScript
- Input sanitization in service layer

## Conclusion

The language update feature provides seamless synchronization between frontend language preferences and backend storage while maintaining excellent user experience. The implementation prioritizes UI responsiveness over perfect sync, ensuring users can switch languages instantly while the system handles backend synchronization gracefully in the background.
