# Dalti Provider Dashboard - Healthcare Provider Management System

Dalti Provider Dashboard is a comprehensive healthcare provider management system built on **React and Tailwind CSS**, providing healthcare providers with everything they need to manage their practice, appointments, and patient interactions.

With Dalti Provider Dashboard, you get access to all the necessary dashboard UI components, elements, and pages required to build a feature-rich and complete healthcare provider management system. Whether you're managing a small clinic or a large healthcare facility, Dalti Provider Dashboard is the perfect solution to help you streamline your operations.

![Dalti Provider Dashboard Preview](./banner.png)

## Overview

Dalti Provider Dashboard provides essential UI components and layouts for building feature-rich, data-driven healthcare provider management systems. It's built on:

- React 19
- TypeScript
- Tailwind CSS

### Features

- 🏥 Healthcare provider management
- 📅 Appointment scheduling system
- 👥 Patient management
- 📊 Analytics and reporting
- 🔐 Secure authentication
- 🌙 Dark mode support

## Installation

### Prerequisites

To get started with Dalti Provider Dashboard, ensure you have the following prerequisites installed and set up:

- Node.js 18.x or later (recommended to use Node.js 20.x or later)

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Start the development server:

```bash
npm run dev
```

1. Install dependencies:

   ```bash
   npm install
   # or
   yarn install
   ```

   > Use the `--legacy-peer-deps` flag, if you face issues while installing.

2. Start the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

## Components

Dalti Provider Dashboard is a pre-designed starting point for building a healthcare provider management system using React.js and Tailwind CSS. The dashboard includes:

- Sophisticated and accessible sidebar
- Healthcare-specific data visualization components
- Provider profile management and appointment scheduling
- Patient management tables and charts
- Authentication forms and input elements
- Alerts, Dropdowns, Modals, Buttons and more
- Dark Mode support 🕶️

All components are built with React and styled using Tailwind CSS for easy customization.

## Key Features

- **Provider Management**: Complete profile management for healthcare providers
- **Appointment System**: Advanced scheduling and calendar management
- **Patient Dashboard**: Comprehensive patient information management
- **Analytics**: Real-time reporting and data visualization
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Modern UI**: Clean, professional interface designed for healthcare workflows

## Changelog

### Version 2.0.2 - [March 25, 2025]

- Upgraded to React 19
- Included overrides for packages to prevent peer dependency errors.
- Migrated from react-flatpickr to flatpickr package for React 19 support

### Version 2.0.1 - [February 27, 2025]

#### Update Overview

- Upgraded to Tailwind CSS v4 for better performance and efficiency.
- Updated class usage to match the latest syntax and features.
- Replaced deprecated class and optimized styles.

#### Next Steps

- Run npm install or yarn install to update dependencies.
- Check for any style changes or compatibility issues.
- Refer to the Tailwind CSS v4 [Migration Guide](https://tailwindcss.com/docs/upgrade-guide) on this release. if needed.
- This update keeps the project up to date with the latest Tailwind improvements. 🚀

### Version 2.0.0 - [February 2025]

A major update with comprehensive redesign and modern React patterns implementation.

#### Major Improvements

- Complete UI redesign with modern React patterns
- New features: collapsible sidebar, chat, and calendar
- Improved performance and accessibility
- Updated data visualization using ApexCharts

#### Key Features

- Redesigned dashboards (Ecommerce, Analytics, Marketing, CRM)
- Enhanced navigation with React Router integration
- Advanced tables with sorting and filtering
- Calendar with drag-and-drop support
- New UI components and improved existing ones

#### Breaking Changes

- Updated sidebar component API
- Migrated charts to ApexCharts
- Revised authentication system

[Read more](https://tailadmin.com/docs/update-logs/react) on this release.

### Version 1.3.7 - [June 20, 2024]

#### Enhancements

1. Remove Repetition of DefaultLayout in every Pages
2. Add ClickOutside Component for reduce repeated functionality in Header Message, Notification and User Dropdowns.

### Version 1.3.6 - [Jan 31, 2024]

#### Enhancements

1. Integrate flatpickr in [Date Picker/Form Elements]
2. Change color after select an option [Select Element/Form Elements].
3. Make it functional [Multiselect Dropdown/Form Elements].
4. Make best value editable [Pricing Table One/Pricing Table].
5. Rearrange Folder structure.

### Version 1.2.0 - [Apr 28, 2023]

- Add Typescript in TailAdmin React.

### Version 1.0.0 - Initial Release - [Mar 13, 2023]

- Initial release of TailAdmin React.

## License

Dalti Provider Dashboard is released under the MIT License.

## Support

If you find this project helpful, please consider giving it a star on GitHub. Your support helps us continue developing and maintaining this healthcare provider management system.
