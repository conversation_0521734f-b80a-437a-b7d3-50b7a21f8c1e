# Global Search Functionality Documentation

## Overview

The Dalti Provider Dashboard now includes a comprehensive global search functionality that allows users to quickly find and navigate to different pages, features, and actions within the application.

## Features

### 🔍 **Smart Search Algorithm**
- **Exact Match Priority**: Exact title matches get the highest ranking
- **Prefix Matching**: Results starting with the search term are prioritized
- **Fuzzy Matching**: Partial matches in titles, descriptions, and keywords
- **Keyword Search**: Comprehensive keyword matching for better discoverability
- **Score-based Ranking**: Results are ranked by relevance score

### 📱 **Multi-Platform Support**
- **Desktop Search**: Full-featured search input in the top navigation bar
- **Mobile Search**: Modal-based search interface optimized for mobile devices
- **Responsive Design**: Seamless experience across all device sizes

### ⌨️ **Keyboard Shortcuts**
- **Cmd/Ctrl + K**: Open search (works globally)
- **Cmd/Ctrl + Shift + P**: Alternative command palette shortcut
- **Arrow Keys**: Navigate through search results
- **Enter**: Select highlighted result
- **Escape**: Close search interface

### 🕒 **Recent Searches**
- **Persistent Storage**: Recent searches are saved in localStorage
- **Quick Access**: Shows recent searches when no query is entered
- **Smart Limit**: Maintains up to 5 recent searches
- **Clear Option**: Users can clear their search history

### 🎯 **Searchable Content**

#### **Pages**
- Dashboard - Overview of business metrics and activities
- Calendar - View and manage appointments in calendar format
- Appointments - Manage all appointments and bookings
- Customers - Manage customer database and relationships
- Services - Configure services, pricing, and offerings
- Locations - Manage business locations and addresses
- Queues - Manage waiting queues and walk-in customers
- Profile - Manage provider profile and settings
- Advanced Features - Access advanced tools and features

#### **Quick Actions**
- New Appointment - Create a new appointment booking
- Add Customer - Add a new customer to database
- Add Service - Create a new service offering
- Add Location - Add a new business location

#### **Features**
- Settings - Configure account and application settings
- Reports & Analytics - View business reports and analytics
- Help & Support - Get help and support documentation

## Technical Implementation

### **Components Structure**

```
src/components/search/
├── GlobalSearchInput.tsx       # Desktop search input component
├── GlobalSearchDropdown.tsx    # Search results dropdown
├── MobileSearchModal.tsx       # Mobile search modal
└── SearchCommandPalette.tsx    # Global keyboard shortcuts handler
```

### **Hooks**

```
src/hooks/
└── useGlobalSearch.ts          # Main search logic and state management
```

### **Key Features of useGlobalSearch Hook**

- **State Management**: Handles search query, results, and UI state
- **Recent Searches**: Manages persistent recent search history
- **Smart Filtering**: Implements intelligent search algorithm
- **Navigation Integration**: Seamless routing to selected results
- **Performance Optimized**: Uses useMemo for efficient re-renders

## Usage Examples

### **Basic Search**
1. Click on the search input or press `Cmd/Ctrl + K`
2. Type your search query (e.g., "appointments", "customers", "new")
3. Use arrow keys to navigate results or click on desired result
4. Press Enter or click to navigate to the selected page

### **Recent Searches**
1. Open search without typing anything
2. View your recent searches
3. Click on any recent search to navigate quickly
4. Use "Clear" button to remove search history

### **Mobile Search**
1. Tap the search icon in the mobile header
2. Use the full-screen search modal
3. Same functionality as desktop with touch-optimized interface

## Customization

### **Adding New Searchable Items**

To add new searchable content, update the `searchableItems` array in `src/hooks/useGlobalSearch.ts`:

```typescript
{
  id: 'unique-id',
  title: 'Display Title',
  description: 'Helpful description for users',
  path: '/route-path',
  category: 'page' | 'feature' | 'action',
  icon: '📊', // Emoji or icon
  keywords: ['keyword1', 'keyword2', 'synonym']
}
```

### **Modifying Search Algorithm**

The search scoring can be adjusted in the `searchResults` useMemo hook:

- **Exact match**: 100 points
- **Prefix match**: 80 points
- **Title contains**: 60 points
- **Description contains**: 30 points
- **Keyword match**: 20 points
- **Exact keyword match**: 40 points

### **Styling Customization**

All components use Tailwind CSS classes and support dark mode. Key styling areas:

- **Search Input**: Matches existing design system
- **Dropdown**: Consistent with other dropdowns in the app
- **Mobile Modal**: Full-screen overlay with backdrop
- **Results**: Categorized with color-coded badges

## Performance Considerations

- **Debounced Search**: Search is triggered on every keystroke but optimized
- **Limited Results**: Maximum 8 results to prevent overwhelming UI
- **Memoized Calculations**: Search algorithm uses React.useMemo for efficiency
- **Lazy Loading**: Components are only rendered when needed

## Accessibility

- **Keyboard Navigation**: Full keyboard support for all interactions
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Focus Management**: Logical focus flow and visual indicators
- **High Contrast**: Works well with system accessibility settings

## Browser Support

- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile Browsers**: iOS Safari, Chrome Mobile, Samsung Internet
- **Keyboard Shortcuts**: Cross-platform support for Cmd/Ctrl modifiers

## Future Enhancements

Potential improvements for future versions:

1. **Search Analytics**: Track popular searches and optimize results
2. **AI-Powered Suggestions**: Smart suggestions based on user behavior
3. **Global Content Search**: Search within appointments, customers, etc.
4. **Voice Search**: Voice-activated search functionality
5. **Search Filters**: Filter results by category or date
6. **Bookmarks**: Save frequently accessed items
7. **Search History Export**: Allow users to export their search history

## Troubleshooting

### **Common Issues**

1. **Search not working**: Check if JavaScript is enabled
2. **Keyboard shortcuts not working**: Ensure no browser extensions are interfering
3. **Recent searches not persisting**: Check localStorage permissions
4. **Mobile search not opening**: Verify touch events are working

### **Development Issues**

1. **TypeScript errors**: Ensure all interfaces are properly imported
2. **Styling issues**: Check Tailwind CSS classes and dark mode support
3. **Navigation issues**: Verify React Router setup and route definitions

## Testing

The search functionality includes:

- **Unit Tests**: For search algorithm and utility functions
- **Integration Tests**: For component interactions
- **E2E Tests**: For complete user workflows
- **Accessibility Tests**: For keyboard navigation and screen readers

Run tests with:
```bash
npm run test
npm run test:coverage
```

## Contributing

When contributing to the search functionality:

1. **Follow TypeScript**: Maintain strict type safety
2. **Test Coverage**: Add tests for new features
3. **Accessibility**: Ensure new features are accessible
4. **Performance**: Consider performance impact of changes
5. **Documentation**: Update this documentation for significant changes
