/**
 * Demo component to showcase the TwoStepCategorySelector
 */

import React, { useState } from 'react';
import { useProviderCategories } from '../../hooks/useAuthMutations';
import TwoStepCategorySelector from './TwoStepCategorySelector';
import { getCategoryPath, getCategoryIconForComponent, getCategoryColor, hasCustomMetadata } from '../../utils/category-utils';

const CategorySelectorDemo: React.FC = () => {
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>();
  const { data: categories, isLoading } = useProviderCategories();

  if (isLoading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-10 bg-gray-200 rounded mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Category Selection Demo
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Test the two-step category selection process
        </p>
      </div>

      <TwoStepCategorySelector
        categories={categories || []}
        value={selectedCategoryId}
        onChange={setSelectedCategoryId}
        required={true}
      />

      {/* Debug Information */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
          API Data Verification
        </h4>
        <div className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
          <div>Selected Category ID: {selectedCategoryId || 'None'}</div>
          {selectedCategoryId && categories && (
            <>
              <div>Category Path: {getCategoryPath(categories, selectedCategoryId)}</div>
              {(() => {
                const selectedCategory = categories.find(c => c.id === selectedCategoryId);
                if (selectedCategory) {
                  return (
                    <>
                      <div>Icon: {getCategoryIconForComponent(selectedCategory)}</div>
                      <div>Color: {getCategoryColor(selectedCategory)}</div>
                      <div>Has Custom Metadata: {hasCustomMetadata(selectedCategory) ? 'Yes' : 'No'}</div>
                      {selectedCategory.metadata && (
                        <div>Metadata: {JSON.stringify(selectedCategory.metadata)}</div>
                      )}
                    </>
                  );
                }
                return null;
              })()}
            </>
          )}
          <div>Total Categories: {categories?.length || 0}</div>
          <div>
            Parent Categories: {categories?.filter(c => !c.parentId).length || 0}
          </div>
          <div>
            Child Categories: {categories?.filter(c => c.parentId).length || 0}
          </div>
          <div>
            Categories with Metadata: {categories?.filter(c => hasCustomMetadata(c)).length || 0}
          </div>
        </div>
      </div>

      {/* Raw API Data Preview */}
      {categories && categories.length > 0 && (
        <details className="mt-4">
          <summary className="text-sm font-medium text-gray-900 dark:text-white cursor-pointer">
            View Raw API Data (First 3 Categories)
          </summary>
          <div className="mt-2 p-3 bg-gray-50 rounded-lg dark:bg-gray-700 max-h-40 overflow-y-auto">
            <pre className="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
              {JSON.stringify(categories.slice(0, 3), null, 2)}
            </pre>
          </div>
        </details>
      )}

      {/* Category Structure Preview */}
      {categories && categories.length > 0 && (
        <details className="mt-4">
          <summary className="text-sm font-medium text-gray-900 dark:text-white cursor-pointer">
            View Category Structure
          </summary>
          <div className="mt-2 p-3 bg-gray-50 rounded-lg dark:bg-gray-700 max-h-60 overflow-y-auto">
            <div className="space-y-2 text-xs">
              {categories
                .filter(c => !c.parentId)
                .map(parent => (
                  <div key={parent.id}>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {parent.title} (ID: {parent.id})
                    </div>
                    <div className="ml-4 space-y-1">
                      {categories
                        .filter(c => c.parentId === parent.id)
                        .map(child => (
                          <div 
                            key={child.id} 
                            className={`text-gray-600 dark:text-gray-400 ${
                              selectedCategoryId === child.id ? 'font-medium text-blue-600 dark:text-blue-400' : ''
                            }`}
                          >
                            → {child.title} (ID: {child.id})
                            {child.description && (
                              <span className="text-gray-500"> - {child.description}</span>
                            )}
                          </div>
                        ))}
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </details>
      )}
    </div>
  );
};

export default CategorySelectorDemo;
