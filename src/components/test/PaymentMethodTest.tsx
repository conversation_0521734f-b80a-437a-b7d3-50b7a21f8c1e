import React, { useState } from 'react';
import { usePaymentMethods } from '../../hooks/usePaymentMethods';
import { useUserLocation } from '../../hooks/useUserLocation';
import { PaymentProcessor, PaymentMethodType } from '../../types';
import Button from '../ui/button/Button';

interface PaymentMethodTestProps {
  onTestResult?: (result: TestResult) => void;
  className?: string;
}

interface TestResult {
  testName: string;
  passed: boolean;
  message: string;
  details?: any;
}

/**
 * Quick test component for payment method functionality
 */
export const PaymentMethodTest: React.FC<PaymentMethodTestProps> = ({
  onTestResult,
  className = '',
}) => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const { data: paymentMethodsData, isLoading: methodsLoading } = usePaymentMethods();
  const { location, isLoading: locationLoading } = useUserLocation();

  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result]);
    onTestResult?.(result);
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    // Test 1: Location Detection
    addTestResult({
      testName: 'Location Detection',
      passed: !!location,
      message: location 
        ? `Detected: ${location.country} (${location.countryCode})`
        : 'Failed to detect location',
      details: location,
    });

    // Test 2: Payment Methods API
    addTestResult({
      testName: 'Payment Methods API',
      passed: !!paymentMethodsData?.data?.methods,
      message: paymentMethodsData?.data?.methods
        ? `Loaded ${paymentMethodsData.data.methods.length} payment methods`
        : 'Failed to load payment methods',
      details: paymentMethodsData,
    });

    // Test 3: Chargily Availability
    const chargilyAvailable = paymentMethodsData?.data?.methods?.some(m => m.id === 'chargily');
    addTestResult({
      testName: 'Chargily Availability',
      passed: !!chargilyAvailable,
      message: chargilyAvailable
        ? 'Chargily Pay is available'
        : 'Chargily Pay is not available',
      details: paymentMethodsData?.data?.methods?.find(m => m.id === 'chargily'),
    });

    // Test 4: Algerian User Detection
    if (location) {
      addTestResult({
        testName: 'Algerian User Detection',
        passed: location.isAlgeria === (location.countryCode === 'DZ'),
        message: location.isAlgeria
          ? 'Correctly identified as Algerian user'
          : 'Correctly identified as non-Algerian user',
        details: { isAlgeria: location.isAlgeria, countryCode: location.countryCode },
      });
    }

    // Test 5: Payment Method Filtering
    if (paymentMethodsData?.data?.methods && location) {
      const expectedMethods = location.isAlgeria 
        ? paymentMethodsData.data.methods.filter(m => m.id === 'chargily' || m.id === 'lemonsqueezy')
        : paymentMethodsData.data.methods.filter(m => m.id === 'lemonsqueezy');
      
      addTestResult({
        testName: 'Payment Method Filtering',
        passed: expectedMethods.length > 0,
        message: `${expectedMethods.length} methods available for ${location.isAlgeria ? 'Algerian' : 'international'} user`,
        details: expectedMethods.map(m => m.id),
      });
    }

    setIsRunning(false);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Payment Method Tests
        </h3>
        <div className="space-x-2">
          <Button
            onClick={runAllTests}
            disabled={isRunning || methodsLoading || locationLoading}
            variant="primary"
            size="sm"
          >
            {isRunning ? 'Running...' : 'Run Tests'}
          </Button>
          <Button
            onClick={clearResults}
            variant="outline"
            size="sm"
          >
            Clear
          </Button>
        </div>
      </div>

      {/* Test Results */}
      <div className="space-y-3">
        {testResults.length === 0 ? (
          <p className="text-gray-500 dark:text-gray-400 text-center py-4">
            Click "Run Tests" to start testing
          </p>
        ) : (
          testResults.map((result, index) => (
            <div
              key={index}
              className={`p-3 rounded border ${
                result.passed
                  ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                  : 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
              }`}
            >
              <div className="flex items-center justify-between">
                <span className="font-medium text-gray-900 dark:text-white">
                  {result.testName}
                </span>
                <span className={`text-sm font-medium ${
                  result.passed ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                }`}>
                  {result.passed ? '✓ PASS' : '✗ FAIL'}
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {result.message}
              </p>
              {result.details && (
                <details className="mt-2">
                  <summary className="text-xs text-gray-500 dark:text-gray-500 cursor-pointer">
                    Show details
                  </summary>
                  <pre className="text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded mt-1 overflow-auto">
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))
        )}
      </div>

      {/* Test Summary */}
      {testResults.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Test Summary:
            </span>
            <span className="text-sm font-medium">
              {testResults.filter(r => r.passed).length} / {testResults.length} passed
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
            <div
              className="bg-green-600 h-2 rounded-full transition-all duration-300"
              style={{
                width: `${(testResults.filter(r => r.passed).length / testResults.length) * 100}%`
              }}
            ></div>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Scenario-based test component
 */
export const ScenarioTest: React.FC<{ scenario: 'algerian' | 'international' }> = ({ scenario }) => {
  const [mockLocation, setMockLocation] = useState({
    country: scenario === 'algerian' ? 'Algeria' : 'United States',
    countryCode: scenario === 'algerian' ? 'DZ' : 'US',
    isAlgeria: scenario === 'algerian',
    detectionMethod: 'mock' as const,
    confidence: 'high' as const,
  });

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        {scenario === 'algerian' ? 'Algerian User' : 'International User'} Scenario
      </h3>
      
      <div className="space-y-4">
        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded">
          <p className="text-sm font-medium text-gray-900 dark:text-white">
            Mock Location: {mockLocation.country} ({mockLocation.countryCode})
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Expected behavior: {scenario === 'algerian' 
              ? 'Should recommend Chargily Pay with EDAHABIA/CIB options'
              : 'Should recommend LemonSqueezy with card/PayPal options'
            }
          </p>
        </div>
        
        {/* Add scenario-specific tests here */}
        <PaymentMethodTest />
      </div>
    </div>
  );
};

export default PaymentMethodTest;
