import React, { useState } from 'react';
import { HardcodedPaymentMethodsService } from '../../services/hardcoded-payment-methods.service';
import Button from '../ui/button/Button';

/**
 * Test component for hardcoded payment methods
 */
export const HardcodedPaymentMethodsTest: React.FC = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    const results = [];

    // Test 1: Get all payment methods
    try {
      const allMethods = await HardcodedPaymentMethodsService.getAllPaymentMethods();
      results.push({
        test: 'Get All Payment Methods',
        success: true,
        data: allMethods,
      });
    } catch (error) {
      results.push({
        test: 'Get All Payment Methods',
        success: false,
        error: error,
      });
    }

    // Test 2: Get methods for Algerian user
    try {
      const algerianMethods = await HardcodedPaymentMethodsService.getAvailablePaymentMethods('DZ');
      results.push({
        test: 'Algerian User Methods',
        success: true,
        data: algerianMethods,
      });
    } catch (error) {
      results.push({
        test: 'Algerian User Methods',
        success: false,
        error: error,
      });
    }

    // Test 3: Get methods for US user
    try {
      const usMethods = await HardcodedPaymentMethodsService.getAvailablePaymentMethods('US');
      results.push({
        test: 'US User Methods',
        success: true,
        data: usMethods,
      });
    } catch (error) {
      results.push({
        test: 'US User Methods',
        success: false,
        error: error,
      });
    }

    // Test 4: Check Chargily availability for Algerian user
    const chargilyForAlgerian = HardcodedPaymentMethodsService.isPaymentMethodAvailable('chargily', 'DZ');
    results.push({
      test: 'Chargily Available for Algerian',
      success: chargilyForAlgerian,
      data: { available: chargilyForAlgerian },
    });

    // Test 5: Check Chargily availability for US user
    const chargilyForUS = HardcodedPaymentMethodsService.isPaymentMethodAvailable('chargily', 'US');
    results.push({
      test: 'Chargily Available for US User',
      success: !chargilyForUS, // Should be false
      data: { available: chargilyForUS },
    });

    // Test 6: Get recommended method for Algerian user
    const recommendedAlgerian = HardcodedPaymentMethodsService.getRecommendedPaymentMethod('DZ');
    results.push({
      test: 'Recommended for Algerian',
      success: recommendedAlgerian?.id === 'chargily',
      data: recommendedAlgerian,
    });

    // Test 7: Get recommended method for US user
    const recommendedUS = HardcodedPaymentMethodsService.getRecommendedPaymentMethod('US');
    results.push({
      test: 'Recommended for US User',
      success: recommendedUS?.id === 'lemonsqueezy',
      data: recommendedUS,
    });

    // Test 8: Validate payment method selection
    const validationChargily = HardcodedPaymentMethodsService.validatePaymentMethodSelection('chargily', 'edahabia', 'DZ');
    results.push({
      test: 'Validate Chargily + EDAHABIA for Algerian',
      success: validationChargily.isValid,
      data: validationChargily,
    });

    // Test 9: Validate invalid payment method selection
    const invalidValidation = HardcodedPaymentMethodsService.validatePaymentMethodSelection('chargily', 'card', 'DZ');
    results.push({
      test: 'Validate Invalid Chargily + Card',
      success: !invalidValidation.isValid, // Should be false
      data: invalidValidation,
    });

    setTestResults(results);
    setIsRunning(false);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Hardcoded Payment Methods Test
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Test the hardcoded payment methods service
          </p>
        </div>
        <Button
          onClick={runTests}
          disabled={isRunning}
          variant="primary"
          size="sm"
        >
          {isRunning ? 'Running...' : 'Run Tests'}
        </Button>
      </div>

      {/* Test Results */}
      <div className="space-y-3">
        {testResults.length === 0 ? (
          <p className="text-gray-500 dark:text-gray-400 text-center py-4">
            Click "Run Tests" to start testing
          </p>
        ) : (
          testResults.map((result, index) => (
            <div
              key={index}
              className={`p-3 rounded border ${
                result.success
                  ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                  : 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
              }`}
            >
              <div className="flex items-center justify-between">
                <span className="font-medium text-gray-900 dark:text-white">
                  {result.test}
                </span>
                <span className={`text-sm font-medium ${
                  result.success ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                }`}>
                  {result.success ? '✓ PASS' : '✗ FAIL'}
                </span>
              </div>
              {result.data && (
                <details className="mt-2">
                  <summary className="text-xs text-gray-500 dark:text-gray-500 cursor-pointer">
                    Show data
                  </summary>
                  <pre className="text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded mt-1 overflow-auto max-h-32">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </details>
              )}
              {result.error && (
                <div className="mt-2">
                  <p className="text-xs text-red-600 dark:text-red-400">
                    Error: {result.error.message || 'Unknown error'}
                  </p>
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Summary */}
      {testResults.length > 0 && (
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Test Summary:
            </span>
            <span className="text-sm font-medium">
              {testResults.filter(r => r.success).length} / {testResults.length} passed
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
            <div
              className="bg-green-600 h-2 rounded-full transition-all duration-300"
              style={{
                width: `${(testResults.filter(r => r.success).length / testResults.length) * 100}%`
              }}
            ></div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HardcodedPaymentMethodsTest;
