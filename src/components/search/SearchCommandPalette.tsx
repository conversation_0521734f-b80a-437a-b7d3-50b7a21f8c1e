import React, { useEffect, useState } from 'react';
import MobileSearchModal from './MobileSearchModal';

const SearchCommandPalette: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Cmd/Ctrl + K to open search
      if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
        event.preventDefault();
        setIsOpen(true);
      }
      
      // Cmd/Ctrl + Shift + P for command palette (alternative)
      if ((event.metaKey || event.ctrlKey) && event.shiftKey && event.key === 'P') {
        event.preventDefault();
        setIsOpen(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <MobileSearchModal 
      isOpen={isOpen}
      onClose={() => setIsOpen(false)}
    />
  );
};

export default SearchCommandPalette;
