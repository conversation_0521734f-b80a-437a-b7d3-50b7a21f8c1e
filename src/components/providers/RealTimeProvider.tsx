import React from 'react';
import { useRealTimeFeatures } from '../../hooks/useRealTimeUpdates';

interface RealTimeProviderProps {
  children: React.ReactNode;
}

/**
 * Provider component that enables real-time features for the application
 * This component should wrap the main app content to enable:
 * - Real-time appointment updates
 * - Push notifications
 * - Appointment reminders
 * - Live status tracking
 */
export default function RealTimeProvider({ children }: RealTimeProviderProps) {
  // Initialize real-time features
  useRealTimeFeatures();

  return <>{children}</>;
}
