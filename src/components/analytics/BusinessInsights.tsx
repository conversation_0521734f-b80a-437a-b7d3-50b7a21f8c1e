import React from 'react';
import { useAppointments } from '../../hooks/useAppointments';
import { useServices } from '../../hooks/useServices';

export default function BusinessInsights() {
  const { data: appointments } = useAppointments();
  const { data: services } = useServices();

  // Calculate business insights
  const insights = React.useMemo(() => {
    if (!appointments || !services) return null;

    const now = new Date();
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Appointment statistics
    const totalAppointments = appointments.length;
    const completedAppointments = appointments.filter(a => a.status === 'completed').length;
    const pendingAppointments = appointments.filter(a => a.status === 'pending').length;
    const thisWeekAppointments = appointments.filter(a => 
      new Date(a.expectedAppointmentStartTime) >= startOfWeek
    ).length;

    // Revenue calculations
    const totalRevenue = appointments
      .filter(a => a.status === 'completed')
      .reduce((sum, a) => sum + (a.service?.price || 0), 0);

    const thisMonthRevenue = appointments
      .filter(a => 
        a.status === 'completed' && 
        new Date(a.expectedAppointmentStartTime) >= startOfMonth
      )
      .reduce((sum, a) => sum + (a.service?.price || 0), 0);

    // Service performance
    const serviceStats = services.map(service => {
      const serviceAppointments = appointments.filter(a => a.service?.id === service.id);
      const completedCount = serviceAppointments.filter(a => a.status === 'completed').length;
      const revenue = serviceAppointments
        .filter(a => a.status === 'completed')
        .reduce((sum, a) => sum + (a.service?.price || 0), 0);

      return {
        ...service,
        appointmentCount: serviceAppointments.length,
        completedCount,
        revenue,
        completionRate: serviceAppointments.length > 0 
          ? Math.round((completedCount / serviceAppointments.length) * 100) 
          : 0
      };
    }).sort((a, b) => b.revenue - a.revenue);

    // Customer insights
    const uniqueCustomers = new Set(
      appointments.map(a => a.customer?.id).filter(Boolean)
    ).size;

    const repeatCustomers = appointments.reduce((acc, appointment) => {
      const customerId = appointment.customer?.id;
      if (customerId) {
        acc[customerId] = (acc[customerId] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const repeatCustomerCount = Object.values(repeatCustomers).filter(count => count > 1).length;
    const repeatCustomerRate = uniqueCustomers > 0 
      ? Math.round((repeatCustomerCount / uniqueCustomers) * 100) 
      : 0;

    return {
      appointments: {
        total: totalAppointments,
        completed: completedAppointments,
        pending: pendingAppointments,
        thisWeek: thisWeekAppointments,
        completionRate: totalAppointments > 0 
          ? Math.round((completedAppointments / totalAppointments) * 100) 
          : 0
      },
      revenue: {
        total: totalRevenue,
        thisMonth: thisMonthRevenue,
        averagePerAppointment: completedAppointments > 0 
          ? Math.round(totalRevenue / completedAppointments) 
          : 0
      },
      customers: {
        unique: uniqueCustomers,
        repeat: repeatCustomerCount,
        repeatRate: repeatCustomerRate
      },
      services: serviceStats
    };
  }, [appointments, services]);

  if (!insights) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Business Overview
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {insights.appointments.total}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Total Appointments
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              ${insights.revenue.total}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Total Revenue
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {insights.customers.unique}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Unique Customers
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
              {insights.appointments.completionRate}%
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Completion Rate
            </div>
          </div>
        </div>
      </div>

      {/* Service Performance */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Service Performance
        </h3>
        
        <div className="space-y-4">
          {insights.services.map((service) => (
            <div key={service.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex-1">
                <h4 className="font-medium text-gray-900 dark:text-white">
                  {service.title}
                </h4>
                <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-1">
                  <span>{service.appointmentCount} appointments</span>
                  <span>{service.completionRate}% completion</span>
                </div>
              </div>
              
              <div className="text-right">
                <div className="text-lg font-semibold text-gray-900 dark:text-white">
                  ${service.revenue}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Revenue
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Customer Insights */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Customer Insights
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="text-xl font-bold text-blue-600 dark:text-blue-400">
              {insights.customers.unique}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Total Customers
            </div>
          </div>
          
          <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="text-xl font-bold text-green-600 dark:text-green-400">
              {insights.customers.repeat}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Repeat Customers
            </div>
          </div>
          
          <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div className="text-xl font-bold text-purple-600 dark:text-purple-400">
              {insights.customers.repeatRate}%
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Repeat Rate
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Quick Actions
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="p-4 text-center bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
            <div className="text-blue-600 dark:text-blue-400 font-medium">
              {insights.appointments.pending}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Pending Appointments
            </div>
          </button>
          
          <button className="p-4 text-center bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
            <div className="text-green-600 dark:text-green-400 font-medium">
              ${insights.revenue.thisMonth}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              This Month Revenue
            </div>
          </button>
          
          <button className="p-4 text-center bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors">
            <div className="text-purple-600 dark:text-purple-400 font-medium">
              {insights.appointments.thisWeek}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              This Week Appointments
            </div>
          </button>
          
          <button className="p-4 text-center bg-orange-50 dark:bg-orange-900/20 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors">
            <div className="text-orange-600 dark:text-orange-400 font-medium">
              ${insights.revenue.averagePerAppointment}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Avg per Appointment
            </div>
          </button>
        </div>
      </div>
    </div>
  );
}
