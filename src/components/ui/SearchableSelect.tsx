import React, { useState, useRef, useEffect } from 'react';
import { ChevronDownIcon, XMarkIcon, MagnifyingGlassIcon } from '../../icons';

export interface SearchableSelectOption {
  value: string;
  label: string;
  subtitle?: string;
  disabled?: boolean;
}

interface SearchableSelectProps {
  options: SearchableSelectOption[];
  value?: string;
  placeholder?: string;
  searchPlaceholder?: string;
  onSelect: (value: string, option: SearchableSelectOption) => void;
  onClear?: () => void;
  disabled?: boolean;
  loading?: boolean;
  error?: string;
  allowClear?: boolean;
  className?: string;
  dropdownClassName?: string;
  noOptionsMessage?: string;
  renderOption?: (option: SearchableSelectOption) => React.ReactNode;
  renderSelected?: (option: SearchableSelectOption) => React.ReactNode;
}

export default function SearchableSelect({
  options = [],
  value,
  placeholder = "Select an option...",
  searchPlaceholder = "Search...",
  onSelect,
  onClear,
  disabled = false,
  loading = false,
  error,
  allowClear = true,
  className = "",
  dropdownClassName = "",
  noOptionsMessage = "No options found",
  renderOption,
  renderSelected,
}: SearchableSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Find selected option
  const selectedOption = options.find(option => option.value === value);

  // Filter options based on search query
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (option.subtitle && option.subtitle.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchQuery('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      setSearchQuery('');
    }
  };

  const handleSelect = (option: SearchableSelectOption) => {
    if (!option.disabled) {
      onSelect(option.value, option);
      setIsOpen(false);
      setSearchQuery('');
    }
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onClear) {
      onClear();
    }
    setSearchQuery('');
  };

  const defaultRenderOption = (option: SearchableSelectOption) => (
    <div className="flex flex-col">
      <span className="font-medium text-gray-900 dark:text-white">
        {option.label}
      </span>
      {option.subtitle && (
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {option.subtitle}
        </span>
      )}
    </div>
  );

  const defaultRenderSelected = (option: SearchableSelectOption) => (
    <span className="truncate">{option.label}</span>
  );

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Main Select Button */}
      <button
        type="button"
        onClick={handleToggle}
        disabled={disabled}
        className={`
          relative w-full h-11 px-4 py-2.5 text-left bg-transparent border border-gray-300 rounded-lg shadow-theme-xs
          focus:outline-none focus:ring-3 focus:ring-brand-500/10 focus:border-brand-300
          dark:border-gray-700 dark:bg-gray-900 dark:focus:border-brand-800
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:border-gray-400 dark:hover:border-gray-600'}
          ${error ? 'border-red-300 focus:border-red-300 focus:ring-red-500/10' : ''}
        `}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            {selectedOption ? (
              renderSelected ? renderSelected(selectedOption) : defaultRenderSelected(selectedOption)
            ) : (
              <span className="text-gray-400 dark:text-gray-500">{placeholder}</span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {loading && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-brand-500"></div>
            )}
            
            {allowClear && selectedOption && !disabled && (
              <button
                type="button"
                onClick={handleClear}
                className="p-0.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
              >
                <XMarkIcon className="h-4 w-4 text-gray-400" />
              </button>
            )}
            
            <ChevronDownIcon 
              className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
            />
          </div>
        </div>
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className={`
          absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 
          rounded-lg shadow-lg max-h-60 overflow-hidden
          ${dropdownClassName}
        `}>
          {/* Search Input */}
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={searchPlaceholder}
                className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md 
                         bg-transparent focus:outline-none focus:ring-2 focus:ring-brand-500/20 focus:border-brand-300
                         dark:focus:border-brand-800 text-gray-900 dark:text-white"
              />
            </div>
          </div>

          {/* Options List */}
          <div className="max-h-48 overflow-y-auto">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => handleSelect(option)}
                  disabled={option.disabled}
                  className={`
                    w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700
                    focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700
                    ${option.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                    ${selectedOption?.value === option.value ? 'bg-brand-50 dark:bg-brand-900/20' : ''}
                  `}
                >
                  {renderOption ? renderOption(option) : defaultRenderOption(option)}
                </button>
              ))
            ) : (
              <div className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400 text-center">
                {noOptionsMessage}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}
    </div>
  );
}
