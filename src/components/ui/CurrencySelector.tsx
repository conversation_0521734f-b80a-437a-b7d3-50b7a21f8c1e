import React, { useState, useRef, useEffect } from 'react';
import { useCurrency } from '../../context/CurrencyContext';
import { SupportedCurrency, getCurrencySymbol, getCurrencyName } from '../../utils/currency.utils';
import { useCommonTranslation } from '../../hooks/useTranslation';
import { ChevronDownIcon } from '../../icons';

interface CurrencySelectorProps {
  variant?: 'header' | 'sidebar' | 'standalone';
  showLabel?: boolean;
  className?: string;
  disabled?: boolean;
}

const CurrencySelector: React.FC<CurrencySelectorProps> = ({
  variant = 'header',
  showLabel = true,
  className = '',
  disabled = false,
}) => {
  const { defaultCurrency, setDefaultCurrency, isLoading } = useCurrency();
  const { t } = useCommonTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const currencies: SupportedCurrency[] = ['DZD', 'EUR'];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleCurrencyChange = async (currency: SupportedCurrency) => {
    if (currency !== defaultCurrency && !disabled) {
      setIsOpen(false);
      await setDefaultCurrency(currency);
    }
  };

  const getCurrencyFlag = (currency: SupportedCurrency): string => {
    switch (currency) {
      case 'DZD':
        return 'DZD'; // Algeria flag for DZD
      case 'EUR':
        return 'EUR'; // EU flag for EUR
      default:
        return '💰';
    }
  };

  const getCurrencyDisplayName = (currency: SupportedCurrency): string => {
    const name = getCurrencyName(currency);
    const symbol = getCurrencySymbol(currency);
    return `${name} (${symbol})`;
  };

  // Variant-specific styling (matching LanguageSwitcher exactly)
  const getVariantStyles = () => {
    switch (variant) {
      case 'header':
        return {
          button: 'relative flex items-center justify-center text-gray-500 transition-colors bg-white border border-gray-200 rounded-full hover:text-gray-700 h-11 w-11 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-white',
          dropdown: 'absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50',
        };
      case 'sidebar':
        return {
          button: 'flex items-center gap-3 w-full px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors',
          dropdown: 'absolute left-full top-0 ml-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50',
        };
      case 'standalone':
        return {
          button: 'flex items-center gap-3 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors border border-gray-200 dark:border-gray-700',
          dropdown: 'absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50',
        };
      default:
        return {
          button: 'relative flex items-center justify-center text-gray-500 transition-colors bg-white border border-gray-200 rounded-full hover:text-gray-700 h-11 w-11 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-white',
          dropdown: 'absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50',
        };
    }
  };

  const styles = getVariantStyles();



  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Currency Selector Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled || isLoading}
        className={`${styles.button} ${disabled || isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
        aria-label="Change currency"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {variant === 'header' ? (
          /* Header variant - only flag */
          <span className="text-lg" role="img" aria-label={getCurrencyDisplayName(defaultCurrency)}>
            {getCurrencyFlag(defaultCurrency)}
          </span>
        ) : (
          /* Other variants - flag + label + chevron */
          <>
            {/* Flag Icon */}
            <span className="text-lg" role="img" aria-label={getCurrencyDisplayName(defaultCurrency)}>
              {getCurrencyFlag(defaultCurrency)}
            </span>

            {/* Currency Label */}
            {showLabel && (
              <span className="hidden sm:inline">
                {variant === 'sidebar' ? getCurrencyDisplayName(defaultCurrency) : defaultCurrency}
              </span>
            )}

            {/* Loading indicator or chevron */}
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
            ) : (
              <ChevronDownIcon
                className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
              />
            )}
          </>
        )}
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className={styles.dropdown}>
          <div className="py-2">
            {currencies.map((currency) => (
              <button
                key={currency}
                onClick={() => handleCurrencyChange(currency)}
                disabled={isLoading}
                className={`
                  w-full flex items-center gap-3 px-4 py-2 text-sm text-left
                  ${currency === defaultCurrency
                    ? 'bg-brand-50 dark:bg-brand-900/20 text-brand-600 dark:text-brand-400'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }
                  ${isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50 dark:hover:bg-gray-700'}
                  transition-colors
                `}
              >
                {/* Flag */}
                <span className="text-lg" role="img" aria-label={getCurrencyName(currency)}>
                  {getCurrencyFlag(currency)}
                </span>

                {/* Currency Info */}
                <div className="flex-1">
                  <div className="font-medium">{getCurrencyName(currency)}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {getCurrencySymbol(currency)} • {currency}
                  </div>
                </div>

                {/* Selected Indicator */}
                {currency === defaultCurrency && (
                  <svg className="w-4 h-4 text-brand-600 dark:text-brand-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
            ))}
          </div>

          {/* Footer note (like in LanguageSwitcher) */}
          <div className="border-t border-gray-200 dark:border-gray-700 px-4 py-2">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Currency preference is saved automatically
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Compact version for mobile or tight spaces (matches CompactLanguageSwitcher)
export const CompactCurrencySelector: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <CurrencySelector
      variant="header"
      showLabel={false}
      className={className}
    />
  );
};

// Sidebar version with full labels (matches SidebarLanguageSwitcher)
export const SidebarCurrencySelector: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <CurrencySelector
      variant="sidebar"
      showLabel={true}
      className={className}
    />
  );
};

export default CurrencySelector;
