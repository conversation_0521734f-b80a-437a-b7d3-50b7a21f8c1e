import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Modal } from '../ui/modal';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import { useRestoreCustomer } from '../../hooks/useProviderCustomers';
import { CustomerRestoreRequest } from '../../types/provider-customer';
import { XMarkIcon } from '../../icons';
import { useCommonTranslation, useFormTranslation, useManagementTranslation } from '../../hooks/useTranslation';

// Validation schema
const restoreCustomerSchema = z.object({
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  mobileNumber: z.string().min(1, 'Mobile number is required').optional().or(z.literal('')),
  nationalId: z.string().min(1, 'National ID is required').optional().or(z.literal('')),
}).refine((data) => {
  // At least one field must be provided
  return data.email || data.mobileNumber || data.nationalId;
}, {
  message: 'At least one field (email, mobile number, or national ID) must be provided',
  path: ['root'],
});

type RestoreCustomerFormData = z.infer<typeof restoreCustomerSchema>;

interface RestoreCustomerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export default function RestoreCustomerModal({ isOpen, onClose, onSuccess }: RestoreCustomerModalProps) {
  const restoreCustomerMutation = useRestoreCustomer();
  const { t } = useCommonTranslation();
  const { t: tForm } = useFormTranslation();
  const { t: tManagement } = useManagementTranslation();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setError,
  } = useForm<RestoreCustomerFormData>({
    resolver: zodResolver(restoreCustomerSchema),
  });

  const onSubmit = async (data: RestoreCustomerFormData) => {
    try {
      // Clean up empty strings to undefined
      const cleanData: CustomerRestoreRequest = {};
      if (data.email?.trim()) cleanData.email = data.email.trim();
      if (data.mobileNumber?.trim()) cleanData.mobileNumber = data.mobileNumber.trim();
      if (data.nationalId?.trim()) cleanData.nationalId = data.nationalId.trim();

      await restoreCustomerMutation.mutateAsync(cleanData);
      reset();
      onSuccess?.();
      onClose();
    } catch (error: any) {
      // Handle specific errors
      const errorMessage = error?.response?.data?.message || error?.message || '';
      if (errorMessage.includes('not found')) {
        setError('root', {
          type: 'manual',
          message: tManagement('customers.restore.noCustomerFound'),
        });
      }
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  const isLoading = restoreCustomerMutation.isPending;

  return (
    <Modal isOpen={isOpen} onClose={handleClose} className="max-w-md p-0" showCloseButton={false}>
      <div className="bg-white dark:bg-gray-800 rounded-3xl w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {tManagement('customers.restore.title')}
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {tManagement('customers.restore.description')}
            </p>
          </div>
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
          {/* Instructions */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              {tManagement('customers.restore.instructions')}
            </p>
          </div>

          {/* Root Error */}
          {errors.root && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <p className="text-sm text-red-700 dark:text-red-300">
                {errors.root.message}
              </p>
            </div>
          )}

          {/* Email Field */}
          <div>
            <Label htmlFor="email">
              Email Address
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter email address"
              error={errors.email?.message}
              disabled={isLoading}
              {...register('email')}
            />
          </div>

          {/* Mobile Number Field */}
          <div>
            <Label htmlFor="mobileNumber">
              Mobile Number
            </Label>
            <Input
              id="mobileNumber"
              type="tel"
              placeholder="Enter mobile number (e.g., +213555123456)"
              error={errors.mobileNumber?.message}
              disabled={isLoading}
              {...register('mobileNumber')}
            />
          </div>

          {/* National ID Field */}
          <div>
            <Label htmlFor="nationalId">
              National ID
            </Label>
            <Input
              id="nationalId"
              type="text"
              placeholder="Enter national ID"
              error={errors.nationalId?.message}
              disabled={isLoading}
              {...register('nationalId')}
            />
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              {t('actions.cancel')}
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              loading={isLoading}
            >
              {tManagement('customers.restore.restoreButton')}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
