import React, { useState } from 'react';
import Button from '../ui/button/Button';
import { ProviderCustomer, ProviderCustomerFilters } from '../../types/provider-customer';
import {
  PlusIcon,
  DownloadIcon,
  MagnifyingGlassIcon,
  UserGroupIcon,
  CalendarIcon,
  EnvelopeIcon,
  ChartBarIcon
} from '../../icons';
import { useManagementTranslation } from '../../hooks/useTranslation';

interface CustomerQuickActionsProps {
  customers: ProviderCustomer[];
  onCreateCustomer: () => void;
  onExportCustomers: () => void;
  onFilterChange: (filters: Partial<ProviderCustomerFilters>) => void;
  currentFilters: ProviderCustomerFilters;
  className?: string;
}

interface QuickFilter {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  filter: Partial<ProviderCustomerFilters>;
  count?: number;
}

export default function CustomerQuickActions({
  customers,
  onCreateCustomer,
  onExportCustomers,
  onFilterChange,
  currentFilters,
  className = ''
}: CustomerQuickActionsProps) {
  const [searchQuery, setSearchQuery] = useState(currentFilters.search || '');
  const { t, currentLanguage } = useManagementTranslation();

  // Custom translations for customers page
  const customTranslations = {
    ar: {
      export: "تصدير",
      addCustomer: "إضافة عميل",
      searchPlaceholder: "البحث عن العملاء...",
      quickFilters: "المرشحات السريعة",
      allCustomers: "جميع العملاء",
      active: "نشط",
      inactive: "غير نشط",
      newThisMonth: "جديد هذا الشهر",
      withEmail: "مع بريد إلكتروني",
      highEngagement: "تفاعل عالي"
    },
    en: {
      export: "Export",
      addCustomer: "Add Customer",
      searchPlaceholder: "Search customers...",
      quickFilters: "Quick Filters",
      allCustomers: "All Customers",
      active: "Active",
      inactive: "Inactive",
      newThisMonth: "New This Month",
      withEmail: "With Email",
      highEngagement: "High Engagement"
    },
    fr: {
      export: "Exporter",
      addCustomer: "Ajouter un client",
      searchPlaceholder: "Rechercher des clients...",
      quickFilters: "Filtres rapides",
      allCustomers: "Tous les clients",
      active: "Actif",
      inactive: "Inactif",
      newThisMonth: "Nouveau ce mois",
      withEmail: "Avec e-mail",
      highEngagement: "Engagement élevé"
    }
  };

  // Get current language from the language context
  const currentLang = currentLanguage as keyof typeof customTranslations;
  const ct = (key: keyof typeof customTranslations.ar) => customTranslations[currentLang]?.[key] || customTranslations.en[key] || key;

  // Ensure customers is always an array for safe filtering
  const safeCustomers = Array.isArray(customers) ? customers : [];

  // Calculate counts for quick filters
  const activeCustomers = safeCustomers.filter(c => c.isActive !== false).length;
  const inactiveCustomers = safeCustomers.filter(c => c.isActive === false).length;
  const newThisMonth = safeCustomers.filter(c => {
    const createdDate = new Date(c.createdAt);
    const now = new Date();
    return createdDate.getMonth() === now.getMonth() &&
           createdDate.getFullYear() === now.getFullYear();
  }).length;
  const withEmail = safeCustomers.filter(c => c.email).length;
  const highEngagement = safeCustomers.filter(c => c.appointmentCount >= 5).length;

  const quickFilters: QuickFilter[] = [
    {
      id: 'all',
      label: ct('allCustomers'),
      icon: UserGroupIcon,
      filter: { isActive: undefined },
      count: safeCustomers.length
    },
    {
      id: 'active',
      label: ct('active'),
      icon: ChartBarIcon,
      filter: { isActive: true },
      count: activeCustomers
    },
    {
      id: 'inactive',
      label: ct('inactive'),
      icon: UserGroupIcon,
      filter: { isActive: false },
      count: inactiveCustomers
    },
    {
      id: 'new',
      label: ct('newThisMonth'),
      icon: CalendarIcon,
      filter: {
        isActive: undefined,
        // Note: In a real implementation, you'd handle date filtering in the backend
      },
      count: newThisMonth
    },
    {
      id: 'email',
      label: ct('withEmail'),
      icon: EnvelopeIcon,
      filter: { hasEmail: true },
      count: withEmail
    },
    {
      id: 'engaged',
      label: ct('highEngagement'),
      icon: ChartBarIcon,
      filter: {
        appointmentCount: { min: 5 }
      },
      count: highEngagement
    }
  ];

  const handleQuickFilter = (filter: Partial<ProviderCustomerFilters>) => {
    onFilterChange(filter);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onFilterChange({ search: searchQuery || undefined });
  };

  const handleSearchClear = () => {
    setSearchQuery('');
    onFilterChange({ search: undefined });
  };

  const isFilterActive = (filter: Partial<ProviderCustomerFilters>) => {
    // Simple check for active filter - in a real app this would be more sophisticated
    if (filter.isActive !== undefined) {
      return currentFilters.isActive === filter.isActive;
    }
    return false;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Actions Row */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        {/* Search */}
        <div className="flex-1 max-w-md">
          <form onSubmit={handleSearchSubmit} className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder={ct('searchPlaceholder')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full h-10 rounded-lg border border-gray-300 bg-transparent pl-10 pr-10 py-2 text-sm text-gray-800 placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-500"
            />
            {searchQuery && (
              <button
                type="button"
                onClick={handleSearchClear}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                ×
              </button>
            )}
          </form>
        </div>

        {/* Primary Actions */}
        <div className="flex items-center space-x-3">
          <Button
            onClick={onExportCustomers}
            variant="outline"
            size="sm"
          >
            <DownloadIcon className="w-4 h-4 mr-2" />
            {ct('export')}
          </Button>
          <Button
            onClick={onCreateCustomer}
            size="sm"
          >
            <PlusIcon className="w-4 h-4 mr-2" />
            {ct('addCustomer')}
          </Button>
        </div>
      </div>

      {/* Quick Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            {ct('quickFilters')}
          </h3>
          {Object.keys(currentFilters).length > 0 && (
            <button
              onClick={() => onFilterChange({})}
              className="text-sm text-brand-600 dark:text-brand-400 hover:text-brand-700 dark:hover:text-brand-300"
            >
              Clear All
            </button>
          )}
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
          {quickFilters.map((filter) => {
            const Icon = filter.icon;
            const isActive = isFilterActive(filter.filter);
            
            return (
              <button
                key={filter.id}
                onClick={() => handleQuickFilter(filter.filter)}
                className={`p-3 rounded-lg border text-left transition-colors ${
                  isActive
                    ? 'border-brand-500 bg-brand-50 dark:bg-brand-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <div className="flex items-center space-x-2 mb-1">
                  <Icon className={`w-4 h-4 ${
                    isActive 
                      ? 'text-brand-600 dark:text-brand-400' 
                      : 'text-gray-400'
                  }`} />
                  <span className={`text-xs font-medium ${
                    isActive 
                      ? 'text-brand-600 dark:text-brand-400' 
                      : 'text-gray-900 dark:text-white'
                  }`}>
                    {filter.label}
                  </span>
                </div>
                <div className={`text-lg font-bold ${
                  isActive 
                    ? 'text-brand-600 dark:text-brand-400' 
                    : 'text-gray-900 dark:text-white'
                }`}>
                  {filter.count}
                </div>
              </button>
            );
          })}
        </div>
      </div>

      {/* Active Filters Display */}
      {Object.keys(currentFilters).length > 0 && (
        <div className="flex items-center space-x-2 text-sm">
          <span className="text-gray-500 dark:text-gray-400">Active filters:</span>
          <div className="flex flex-wrap gap-2">
            {currentFilters.search && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                Search: "{currentFilters.search}"
                <button
                  onClick={() => onFilterChange({ search: undefined })}
                  className="ml-1 hover:text-blue-600 dark:hover:text-blue-300"
                >
                  ×
                </button>
              </span>
            )}
            {currentFilters.isActive !== undefined && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                Status: {currentFilters.isActive ? 'Active' : 'Inactive'}
                <button
                  onClick={() => onFilterChange({ isActive: undefined })}
                  className="ml-1 hover:text-green-600 dark:hover:text-green-300"
                >
                  ×
                </button>
              </span>
            )}
            {currentFilters.sortBy && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400">
                Sort: {currentFilters.sortBy} ({currentFilters.sortOrder || 'desc'})
                <button
                  onClick={() => onFilterChange({ sortBy: undefined, sortOrder: undefined })}
                  className="ml-1 hover:text-purple-600 dark:hover:text-purple-300"
                >
                  ×
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
