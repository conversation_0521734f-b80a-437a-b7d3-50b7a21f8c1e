import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import { Modal } from '../ui/modal';
import { CustomerFormData } from '../../types/provider-customer';
import { 
  XMarkIcon, 
  CheckCircleIcon, 
  UserCircleIcon,
  PhoneIcon,
  EnvelopeIcon,
  DocumentTextIcon,
  ArrowRightIcon
} from '../../icons';

interface CustomerOnboardingFlowProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (customerData: CustomerFormData) => void;
}

type OnboardingStep = 'welcome' | 'basic-info' | 'contact-info' | 'preferences' | 'review' | 'complete';

const customerSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  mobileNumber: z.string().min(1, 'Mobile number is required'),
  email: z.string().email('Invalid email format').optional().or(z.literal('')),
  nationalId: z.string().optional().or(z.literal('')),
  notes: z.string().max(1000, 'Notes cannot exceed 1000 characters').optional().or(z.literal('')),
});

export default function CustomerOnboardingFlow({ 
  isOpen, 
  onClose, 
  onComplete 
}: CustomerOnboardingFlowProps) {
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('welcome');
  const [preferences, setPreferences] = useState({
    appointmentReminders: true,
    promotionalMessages: false,
    preferredContactMethod: 'sms' as 'sms' | 'email' | 'phone'
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    trigger,
  } = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      mobileNumber: '',
      email: '',
      nationalId: '',
      notes: '',
    },
  });

  const watchedValues = watch();

  const steps: { id: OnboardingStep; title: string; description: string }[] = [
    { id: 'welcome', title: 'Welcome', description: 'Getting started' },
    { id: 'basic-info', title: 'Basic Info', description: 'Name and identification' },
    { id: 'contact-info', title: 'Contact', description: 'Phone and email' },
    { id: 'preferences', title: 'Preferences', description: 'Communication settings' },
    { id: 'review', title: 'Review', description: 'Confirm details' },
    { id: 'complete', title: 'Complete', description: 'All done!' },
  ];

  const getCurrentStepIndex = () => steps.findIndex(step => step.id === currentStep);
  const getProgress = () => ((getCurrentStepIndex() + 1) / steps.length) * 100;

  const handleNext = async () => {
    const stepIndex = getCurrentStepIndex();
    
    // Validate current step before proceeding
    if (currentStep === 'basic-info') {
      const isValid = await trigger(['firstName', 'lastName']);
      if (!isValid) return;
    } else if (currentStep === 'contact-info') {
      const isValid = await trigger(['mobileNumber', 'email']);
      if (!isValid) return;
    }

    if (stepIndex < steps.length - 1) {
      setCurrentStep(steps[stepIndex + 1].id);
    }
  };

  const handlePrevious = () => {
    const stepIndex = getCurrentStepIndex();
    if (stepIndex > 0) {
      setCurrentStep(steps[stepIndex - 1].id);
    }
  };

  const handleComplete = (data: CustomerFormData) => {
    const finalData = {
      ...data,
      email: data.email?.trim() || undefined,
      nationalId: data.nationalId?.trim() || undefined,
      notes: data.notes?.trim() || undefined,
    };
    
    onComplete(finalData);
    setCurrentStep('complete');
  };

  const handleClose = () => {
    setCurrentStep('welcome');
    onClose();
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'welcome':
        return (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-brand-100 dark:bg-brand-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <UserCircleIcon className="w-8 h-8 text-brand-600 dark:text-brand-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Welcome to Customer Onboarding
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Let's set up a new customer profile. This will only take a few minutes.
            </p>
            <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
              <p>✓ Collect basic customer information</p>
              <p>✓ Set up contact preferences</p>
              <p>✓ Create customer relationship</p>
            </div>
          </div>
        );

      case 'basic-info':
        return (
          <div className="space-y-4">
            <div className="text-center mb-6">
              <UserCircleIcon className="w-12 h-12 text-brand-600 dark:text-brand-400 mx-auto mb-2" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Basic Information
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Enter the customer's basic details
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName" required>First Name</Label>
                <Input
                  id="firstName"
                  type="text"
                  placeholder="Enter first name"
                  error={errors.firstName?.message}
                  {...register('firstName')}
                />
              </div>
              <div>
                <Label htmlFor="lastName" required>Last Name</Label>
                <Input
                  id="lastName"
                  type="text"
                  placeholder="Enter last name"
                  error={errors.lastName?.message}
                  {...register('lastName')}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="nationalId">National ID (Optional)</Label>
              <Input
                id="nationalId"
                type="text"
                placeholder="Enter national ID"
                error={errors.nationalId?.message}
                {...register('nationalId')}
              />
            </div>
          </div>
        );

      case 'contact-info':
        return (
          <div className="space-y-4">
            <div className="text-center mb-6">
              <PhoneIcon className="w-12 h-12 text-brand-600 dark:text-brand-400 mx-auto mb-2" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Contact Information
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                How can we reach this customer?
              </p>
            </div>

            <div>
              <Label htmlFor="mobileNumber" required>Mobile Number</Label>
              <Input
                id="mobileNumber"
                type="tel"
                placeholder="Enter mobile number"
                error={errors.mobileNumber?.message}
                {...register('mobileNumber')}
              />
            </div>

            <div>
              <Label htmlFor="email">Email Address (Optional)</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter email address"
                error={errors.email?.message}
                {...register('email')}
              />
            </div>
          </div>
        );

      case 'preferences':
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <EnvelopeIcon className="w-12 h-12 text-brand-600 dark:text-brand-400 mx-auto mb-2" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Communication Preferences
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Set up how you'd like to communicate with this customer
              </p>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    Appointment Reminders
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Send reminders before appointments
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.appointmentReminders}
                    onChange={(e) => setPreferences(prev => ({
                      ...prev,
                      appointmentReminders: e.target.checked
                    }))}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 dark:peer-focus:ring-brand-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-brand-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    Promotional Messages
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Receive offers and updates
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.promotionalMessages}
                    onChange={(e) => setPreferences(prev => ({
                      ...prev,
                      promotionalMessages: e.target.checked
                    }))}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 dark:peer-focus:ring-brand-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-brand-600"></div>
                </label>
              </div>

              <div>
                <Label>Preferred Contact Method</Label>
                <div className="grid grid-cols-3 gap-3 mt-2">
                  {[
                    { value: 'sms', label: 'SMS', icon: PhoneIcon },
                    { value: 'email', label: 'Email', icon: EnvelopeIcon },
                    { value: 'phone', label: 'Phone', icon: PhoneIcon },
                  ].map((method) => {
                    const Icon = method.icon;
                    return (
                      <button
                        key={method.value}
                        type="button"
                        onClick={() => setPreferences(prev => ({
                          ...prev,
                          preferredContactMethod: method.value as any
                        }))}
                        className={`p-3 rounded-lg border text-center transition-colors ${
                          preferences.preferredContactMethod === method.value
                            ? 'border-brand-500 bg-brand-50 dark:bg-brand-900/20'
                            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                        }`}
                      >
                        <Icon className="w-5 h-5 mx-auto mb-1" />
                        <div className="text-sm font-medium">{method.label}</div>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        );

      case 'review':
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <DocumentTextIcon className="w-12 h-12 text-brand-600 dark:text-brand-400 mx-auto mb-2" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Review Information
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Please review the customer information before creating the profile
              </p>
            </div>

            <div className="space-y-4">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                  Basic Information
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Name:</span>
                    <span className="text-gray-900 dark:text-white">
                      {watchedValues.firstName} {watchedValues.lastName}
                    </span>
                  </div>
                  {watchedValues.nationalId && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">National ID:</span>
                      <span className="text-gray-900 dark:text-white">{watchedValues.nationalId}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                  Contact Information
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Mobile:</span>
                    <span className="text-gray-900 dark:text-white">{watchedValues.mobileNumber}</span>
                  </div>
                  {watchedValues.email && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Email:</span>
                      <span className="text-gray-900 dark:text-white">{watchedValues.email}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                  Preferences
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Appointment Reminders:</span>
                    <span className="text-gray-900 dark:text-white">
                      {preferences.appointmentReminders ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Promotional Messages:</span>
                    <span className="text-gray-900 dark:text-white">
                      {preferences.promotionalMessages ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Preferred Contact:</span>
                    <span className="text-gray-900 dark:text-white capitalize">
                      {preferences.preferredContactMethod}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <Label htmlFor="notes">Additional Notes (Optional)</Label>
              <textarea
                id="notes"
                rows={3}
                placeholder="Add any additional notes about this customer..."
                className="w-full rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-500"
                {...register('notes')}
              />
            </div>
          </div>
        );

      case 'complete':
        return (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircleIcon className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Customer Profile Created!
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {watchedValues.firstName} {watchedValues.lastName} has been successfully added to your customer list.
            </p>
            <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
              <p>✓ Customer profile created</p>
              <p>✓ Contact information saved</p>
              <p>✓ Preferences configured</p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} className="max-w-2xl p-0">
      <div className="bg-white dark:bg-gray-800 rounded-3xl shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Customer Onboarding
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Step {getCurrentStepIndex() + 1} of {steps.length}
            </p>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Progress Bar */}
        <div className="px-6 py-4">
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-brand-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${getProgress()}%` }}
            ></div>
          </div>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit(handleComplete)} className="p-6">
          {renderStepContent()}

          {/* Navigation */}
          {currentStep !== 'complete' && (
            <div className="flex items-center justify-between pt-6 mt-6 border-t border-gray-200 dark:border-gray-700">
              <Button
                type="button"
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 'welcome'}
              >
                Previous
              </Button>
              
              {currentStep === 'review' ? (
                <Button type="submit">
                  Create Customer
                </Button>
              ) : (
                <Button
                  type="button"
                  onClick={handleNext}
                >
                  Next
                  <ArrowRightIcon className="w-4 h-4 ml-2" />
                </Button>
              )}
            </div>
          )}

          {currentStep === 'complete' && (
            <div className="flex justify-center pt-6 mt-6 border-t border-gray-200 dark:border-gray-700">
              <Button onClick={handleClose}>
                Done
              </Button>
            </div>
          )}
        </form>
      </div>
    </Modal>
  );
}
