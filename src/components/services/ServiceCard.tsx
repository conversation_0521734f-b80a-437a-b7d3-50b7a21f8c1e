import React from 'react';
import Button from '../ui/button/Button';
import { Service } from '../../types';
import { useCommonTranslation, useTranslation } from '../../hooks/useTranslation';
import { useCurrency } from '../../context/CurrencyContext';

interface ServiceCardProps {
  service: Service;
  onEdit: () => void;
  onDelete: () => void;
  isDeleting?: boolean;
}

export default function ServiceCard({ service, onEdit, onDelete, isDeleting }: ServiceCardProps) {
  const { t: tCommon } = useCommonTranslation();
  const { t: tForms } = useTranslation('forms');
  const { formatPrice } = useCurrency();

  const formatDuration = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };



  const getDeliveryTypeLabel = (type: string): string => {
    switch (type) {
      case 'at_location':
        return tForms('labels.atLocation');
      case 'at_customer':
        return tForms('labels.atCustomer');
      case 'both':
        return tForms('labels.both');
      default:
        return type;
    }
  };

  const getStatusColor = (isPublic: boolean, acceptNew: boolean): string => {
    if (!isPublic) return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    if (!acceptNew) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
  };

  const getStatusLabel = (isPublic: boolean, acceptNew: boolean): string => {
    if (!isPublic) return tForms('labels.private');
    if (!acceptNew) return tForms('labels.notAccepting');
    return tForms('labels.active');
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: service.color }}
            />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {service.title}
            </h3>
          </div>
          {service.category && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
              {service.category.title}
            </span>
          )}
        </div>
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
            service.isPublic,
            service.acceptNew
          )}`}
        >
          {getStatusLabel(service.isPublic, service.acceptNew)}
        </span>
      </div>

      {/* Description */}
      {service.description && (
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
          {service.description}
        </p>
      )}

      {/* Service Details */}
      <div className="space-y-3 mb-6">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500 dark:text-gray-400">{tCommon('common.price')}</span>
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {formatPrice(service.price)}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500 dark:text-gray-400">{tCommon('common.duration')}</span>
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {formatDuration(service.duration)}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500 dark:text-gray-400">{tForms('labels.delivery')}</span>
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {getDeliveryTypeLabel(service.deliveryType)}
          </span>
        </div>

        {service.pointsRequirements > 0 && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-500 dark:text-gray-400">{tForms('labels.pointsRequired')}</span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {service.pointsRequirements}
            </span>
          </div>
        )}
      </div>

      {/* Service Features */}
      <div className="flex flex-wrap gap-2 mb-6">
        {service.acceptOnline && (
          <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400">
            {tForms('labels.onlineBooking')}
          </span>
        )}
        {service.notificationOn && (
          <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400">
            {tForms('labels.notifications')}
          </span>
        )}
        {service.servedRegions && service.servedRegions.length > 0 && (
          <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400">
            {service.servedRegions.length} {tForms('labels.regions')}{service.servedRegions.length > 1 ? 's' : ''}
          </span>
        )}
      </div>

      {/* Actions */}
      <div className="flex gap-2">
        <Button
          onClick={onEdit}
          variant="outline"
          size="sm"
          className="flex-1"
        >
          {tCommon('actions.edit')}
        </Button>
        <Button
          onClick={onDelete}
          variant="outline"
          size="sm"
          className="flex-1 text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
          disabled={isDeleting}
        >
          {isDeleting ? tCommon('messages.deleting') : tCommon('actions.delete')}
        </Button>
      </div>
    </div>
  );
}
