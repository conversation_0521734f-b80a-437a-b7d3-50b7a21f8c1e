import React, { useState, useEffect } from 'react';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import Checkbox from '../form/input/Checkbox';
import { ErrorDisplay } from '../error';
import { useLocationHours, useUpdateLocationHours } from '../../hooks/useLocations';
import { Location, Opening } from '../../types';

interface LocationHoursManagerProps {
  location: Location;
  onClose: () => void;
}

interface DayHours {
  dayOfWeek: string;
  isActive: boolean;
  timeFrom: string;
  timeTo: string;
}

const daysOfWeek = [
  'Monday',
  'Tuesday', 
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday'
];

export default function LocationHoursManager({ location, onClose }: LocationHoursManagerProps) {
  const [hours, setHours] = useState<DayHours[]>([]);
  
  const { data: locationHours, isLoading } = useLocationHours(location.id);
  const updateHoursMutation = useUpdateLocationHours();

  // Initialize hours state
  useEffect(() => {
    const initialHours = daysOfWeek.map(day => {
      const existingHour = locationHours?.find(h => h.dayOfWeek === day);
      return {
        dayOfWeek: day,
        isActive: existingHour?.isActive || false,
        timeFrom: existingHour?.timeFrom || '09:00',
        timeTo: existingHour?.timeTo || '17:00',
      };
    });
    setHours(initialHours);
  }, [locationHours]);

  const handleDayToggle = (dayIndex: number, isActive: boolean) => {
    setHours(prev => prev.map((day, index) => 
      index === dayIndex ? { ...day, isActive } : day
    ));
  };

  const handleTimeChange = (dayIndex: number, field: 'timeFrom' | 'timeTo', value: string) => {
    setHours(prev => prev.map((day, index) => 
      index === dayIndex ? { ...day, [field]: value } : day
    ));
  };

  const handleCopyToAll = (dayIndex: number) => {
    const sourceDay = hours[dayIndex];
    if (!sourceDay.isActive) return;

    setHours(prev => prev.map(day => ({
      ...day,
      isActive: true,
      timeFrom: sourceDay.timeFrom,
      timeTo: sourceDay.timeTo,
    })));
  };

  const handleSave = async () => {
    try {
      const openings: Opening[] = hours
        .filter(day => day.isActive)
        .map(day => ({
          id: 0, // Will be set by backend
          sProvidingPlaceId: location.id,
          dayOfWeek: day.dayOfWeek,
          timeFrom: day.timeFrom,
          timeTo: day.timeTo,
          isActive: day.isActive,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }));

      await updateHoursMutation.mutateAsync({
        locationId: location.id,
        hours: openings,
      });
      
      onClose();
    } catch (error) {
      // Error handled by mutation
    }
  };

  const isFormLoading = updateHoursMutation.isPending;

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden p-6">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden">
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Operating Hours
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {location.name}
          </p>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div className="p-6 space-y-6">
            {/* Hours Configuration */}
            <div className="space-y-4">
              {hours.map((day, index) => (
                <div key={day.dayOfWeek} className="flex items-center space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div className="w-24">
                    <Checkbox
                      checked={day.isActive}
                      onChange={(checked) => handleDayToggle(index, checked)}
                      disabled={isFormLoading}
                    />
                    <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                      {day.dayOfWeek}
                    </span>
                  </div>

                  <div className="flex-1 flex items-center space-x-3">
                    <div className="flex-1">
                      <Label>From</Label>
                      <Input
                        type="time"
                        value={day.timeFrom}
                        onChange={(e) => handleTimeChange(index, 'timeFrom', e.target.value)}
                        disabled={!day.isActive || isFormLoading}
                        className="w-full"
                      />
                    </div>

                    <div className="flex-1">
                      <Label>To</Label>
                      <Input
                        type="time"
                        value={day.timeTo}
                        onChange={(e) => handleTimeChange(index, 'timeTo', e.target.value)}
                        disabled={!day.isActive || isFormLoading}
                        className="w-full"
                      />
                    </div>

                    <div className="pt-6">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => handleCopyToAll(index)}
                        disabled={!day.isActive || isFormLoading}
                        className="text-xs"
                      >
                        Copy to All
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Quick Actions */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Quick Actions
              </h3>
              <div className="flex flex-wrap gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setHours(prev => prev.map(day => ({
                      ...day,
                      isActive: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'].includes(day.dayOfWeek),
                      timeFrom: '09:00',
                      timeTo: '17:00',
                    })));
                  }}
                  disabled={isFormLoading}
                >
                  Standard Business Hours
                </Button>
                
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setHours(prev => prev.map(day => ({
                      ...day,
                      isActive: true,
                      timeFrom: '09:00',
                      timeTo: '17:00',
                    })));
                  }}
                  disabled={isFormLoading}
                >
                  Open All Days
                </Button>
                
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setHours(prev => prev.map(day => ({
                      ...day,
                      isActive: false,
                    })));
                  }}
                  disabled={isFormLoading}
                >
                  Close All Days
                </Button>
              </div>
            </div>

            {updateHoursMutation.error && (
              <ErrorDisplay
                error={updateHoursMutation.error}
                variant="banner"
                size="sm"
              />
            )}

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isFormLoading}
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleSave}
                disabled={isFormLoading}
              >
                {isFormLoading ? 'Saving...' : 'Save Hours'}
              </Button>
            </div>
          </div>
    </div>
  );
}
