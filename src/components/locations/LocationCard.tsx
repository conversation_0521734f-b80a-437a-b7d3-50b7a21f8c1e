import React from 'react';
import Button from '../ui/button/Button';
import { Location } from '../../types';
import { useManagementTranslation } from '../../hooks/useTranslation';

interface LocationCardProps {
  location: Location;
  onEdit: () => void;
  onDelete: () => void;
  onManageHours: () => void;
  isDeleting?: boolean;
}

export default function LocationCard({
  location,
  onEdit,
  onDelete,
  onManageHours,
  isDeleting
}: LocationCardProps) {
  const { t, currentLanguage } = useManagementTranslation();

  // Custom translations for location cards
  const cardTranslations = {
    ar: {
      active: "نشط",
      noAddress: "لم يتم تحديد عنوان",
      floor: "الطابق: {floor}",
      fax: "فاكس: {fax}",
      amenities: "المرافق",
      parking: "موقف سيارات",
      elevator: "مصعد",
      handicapAccess: "وصول ذوي الاحتياجات الخاصة",
      edit: "تعديل",
      hours: "الساعات",
      deleteLocation: "حذف الموقع",
      deleting: "جاري الحذف..."
    },
    en: {
      active: "Active",
      noAddress: "No address specified",
      floor: "Floor: {floor}",
      fax: "Fax: {fax}",
      amenities: "Amenities",
      parking: "Parking",
      elevator: "Elevator",
      handicapAccess: "Handicap Access",
      edit: "Edit",
      hours: "Hours",
      deleteLocation: "Delete Location",
      deleting: "Deleting..."
    },
    fr: {
      active: "Actif",
      noAddress: "Aucune adresse spécifiée",
      floor: "Étage: {floor}",
      fax: "Fax: {fax}",
      amenities: "Commodités",
      parking: "Parking",
      elevator: "Ascenseur",
      handicapAccess: "Accès handicapés",
      edit: "Modifier",
      hours: "Heures",
      deleteLocation: "Supprimer l'emplacement",
      deleting: "Suppression..."
    }
  };

  const currentLang = currentLanguage as keyof typeof cardTranslations;
  const ct = (key: keyof typeof cardTranslations.ar, params?: { floor?: string; fax?: string }) => {
    let text = cardTranslations[currentLang]?.[key] || cardTranslations.en[key] || key;
    if (params) {
      Object.entries(params).forEach(([param, value]) => {
        text = text.replace(`{${param}}`, value?.toString() || '');
      });
    }
    return text;
  };
  const formatAddress = (): string => {
    const parts = [];
    if (location.address) parts.push(location.address);
    if (location.city) parts.push(location.city);
    return parts.join(', ') || ct('noAddress');
  };

  const getAmenities = () => {
    const amenities = [];
    if (location.parking) amenities.push(ct('parking'));
    if (location.elevator) amenities.push(ct('elevator'));
    if (location.handicapAccess) amenities.push(ct('handicapAccess'));
    return amenities;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
            {location.name}
          </h3>
          {location.shortName && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
              {location.shortName}
            </span>
          )}
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 rounded-full bg-green-400"></div>
          <span className="text-xs text-gray-500 dark:text-gray-400">{ct('active')}</span>
        </div>
      </div>

      {/* Address */}
      <div className="mb-4">
        <div className="flex items-start space-x-2">
          <svg className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <div className="flex-1">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {formatAddress()}
            </p>
            {location.floor && (
              <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                {ct('floor', { floor: location.floor })}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div className="space-y-2 mb-4">
        {location.mobile && !location.isMobileHidden && (
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {location.mobile}
            </span>
          </div>
        )}
        
        {location.fax && (
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
            </svg>
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {ct('fax', { fax: location.fax })}
            </span>
          </div>
        )}
      </div>

      {/* Amenities */}
      {getAmenities().length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {ct('amenities')}
          </h4>
          <div className="flex flex-wrap gap-2">
            {getAmenities().map((amenity) => (
              <span
                key={amenity}
                className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400"
              >
                {amenity}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Timezone */}
      {location.timezone && (
        <div className="mb-4">
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {location.timezone}
            </span>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex flex-col gap-2">
        <div className="flex gap-2">
          <Button
            onClick={onEdit}
            variant="outline"
            size="sm"
            className="flex-1"
          >
            {ct('edit')}
          </Button>
         <Button
          onClick={onDelete}
          variant="outline"
          size="sm"
          className="flex-1 text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
          disabled={isDeleting}
        >
          {isDeleting ? ct('deleting') : ct('deleteLocation')}
        </Button>
        </div>
        
      </div>
    </div>
  );
}
