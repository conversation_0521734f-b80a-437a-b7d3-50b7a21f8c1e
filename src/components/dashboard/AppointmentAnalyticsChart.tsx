import React, { useState, useMemo } from 'react';
import { useAppointments } from '../../hooks/useAppointments';
import { useDashboardTranslation } from '../../hooks/useTranslation';
import { Appointment } from '../../types/appointment';

const AppointmentAnalyticsChart: React.FC = () => {
  const [period, setPeriod] = useState<'week' | 'month'>('week');
  const { data: appointments, isLoading, error } = useAppointments();
  const { t } = useDashboardTranslation();

  const appointmentsArray: Appointment[] = (appointments as Appointment[]) || [];

  // Consolidated appointment statistics - single source of truth
  const appointmentStats = useMemo(() => {
    const total = appointmentsArray.length;
    const completed = appointmentsArray.filter(apt => apt.status === 'completed').length;
    const cancelled = appointmentsArray.filter(apt =>
      (apt.status as string) === 'canceled' || (apt.status as string) === 'cancelled'
    ).length;
    const noShow = appointmentsArray.filter(apt => apt.status === 'noshow').length;
    const cancelledAndNoShow = cancelled + noShow;
    const pending = total - completed - cancelledAndNoShow;
    const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;

    console.log('Appointment Statistics:', {
      total,
      completed,
      cancelled,
      noShow,
      cancelledAndNoShow,
      pending,
      completionRate,
      statuses: appointmentsArray.map(apt => apt.status)
    });

    return { total, completed, cancelled, noShow, cancelledAndNoShow, pending, completionRate };
  }, [appointmentsArray]);

  // Helper function to distribute appointments by time periods
  const distributeAppointmentsByPeriod = useMemo(() => {
    if (!appointmentsArray.length) {
      return {
        week: {
          labels: ['6 days ago', '5 days ago', '4 days ago', '3 days ago', '2 days ago', 'Yesterday', 'Today'],
          total: [0, 0, 0, 0, 0, 0, 0],
          completed: [0, 0, 0, 0, 0, 0, 0],
          cancelledAndNoShow: [0, 0, 0, 0, 0, 0, 0],
          pending: [0, 0, 0, 0, 0, 0, 0]
        },
        month: {
          labels: ['4 weeks ago', '3 weeks ago', '2 weeks ago', 'This week'],
          total: [0, 0, 0, 0],
          completed: [0, 0, 0, 0],
          cancelledAndNoShow: [0, 0, 0, 0],
          pending: [0, 0, 0, 0]
        }
      };
    }

    const now = new Date();

    // Week view: Last 7 days
    const weekData = {
      labels: ['6 days ago', '5 days ago', '4 days ago', '3 days ago', '2 days ago', 'Yesterday', 'Today'],
      total: [0, 0, 0, 0, 0, 0, 0],
      completed: [0, 0, 0, 0, 0, 0, 0],
      cancelledAndNoShow: [0, 0, 0, 0, 0, 0, 0],
      pending: [0, 0, 0, 0, 0, 0, 0]
    };

    // Month view: Last 4 weeks
    const monthData = {
      labels: ['4 weeks ago', '3 weeks ago', '2 weeks ago', 'This week'],
      total: [0, 0, 0, 0],
      completed: [0, 0, 0, 0],
      cancelledAndNoShow: [0, 0, 0, 0],
      pending: [0, 0, 0, 0]
    };

    appointmentsArray.forEach(appointment => {
      const appointmentDate = new Date(appointment.expectedAppointmentStartTime);
      const daysDiff = Math.floor((now.getTime() - appointmentDate.getTime()) / (1000 * 60 * 60 * 24));

      // Distribute for week view (last 7 days)
      if (daysDiff >= 0 && daysDiff < 7) {
        const dayIndex = 6 - daysDiff; // Reverse index (today = 6, yesterday = 5, etc.)
        weekData.total[dayIndex]++;

        if (appointment.status === 'completed') {
          weekData.completed[dayIndex]++;
        } else if ((appointment.status as string) === 'canceled' || (appointment.status as string) === 'cancelled' || appointment.status === 'noshow') {
          weekData.cancelledAndNoShow[dayIndex]++;
        } else {
          weekData.pending[dayIndex]++;
        }
      }

      // Distribute for month view (last 4 weeks)
      if (daysDiff >= 0 && daysDiff < 28) {
        const weekIndex = Math.floor(daysDiff / 7);
        const monthIndex = 3 - weekIndex; // Reverse index (this week = 3, last week = 2, etc.)

        if (monthIndex >= 0 && monthIndex < 4) {
          monthData.total[monthIndex]++;

          if (appointment.status === 'completed') {
            monthData.completed[monthIndex]++;
          } else if ((appointment.status as string) === 'canceled' || (appointment.status as string) === 'cancelled' || appointment.status === 'noshow') {
            monthData.cancelledAndNoShow[monthIndex]++;
          } else {
            monthData.pending[monthIndex]++;
          }
        }
      }
    });

    console.log('Week distribution:', weekData);
    console.log('Month distribution:', monthData);

    return { week: weekData, month: monthData };
  }, [appointmentsArray]);

  const currentChartData = distributeAppointmentsByPeriod[period];
  const maxValue = Math.max(...currentChartData.total, 1); // Ensure at least 1 to avoid division by zero

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 animate-pulse">
        <div className="flex items-center justify-between mb-6">
          <div>
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-48 mb-2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
          </div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
        </div>
        <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {t('appointmentChart.errorTitle', 'Unable to load appointment analytics')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {t('appointmentChart.errorMessage', 'Please try again later.')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('appointmentChart.title', 'Appointment Analytics')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {appointmentStats.total} {t('appointmentChart.totalAppointments', 'total appointments')} • {appointmentStats.completionRate}% {t('appointmentChart.completionRate', 'completion rate')}
          </p>
        </div>
        <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button
            onClick={() => setPeriod('week')}
            className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
              period === 'week'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            {t('appointmentChart.week', 'Week')}
          </button>
          <button
            onClick={() => setPeriod('month')}
            className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
              period === 'month'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            {t('appointmentChart.month', 'Month')}
          </button>
        </div>
      </div>

      {/* Legend */}
      <div className="flex items-center gap-6 mb-4">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {t('appointmentChart.completed', 'Completed')}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {t('appointmentChart.cancelledAndNoShow', 'Cancelled/No Show')}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
          <span className="text-sm text-gray-600 dark:text-gray-400">
            Pending
          </span>
        </div>
      </div>

      {/* Chart */}
      <div className="space-y-4">
        <div className="flex items-end justify-between h-48 gap-2">
          {currentChartData.total.map((totalValue, index) => {
            const completedValue = currentChartData.completed[index];
            const cancelledValue = currentChartData.cancelledAndNoShow[index];
            const pendingValue = currentChartData.pending[index];
            
            // Calculate heights as percentages of the max value
            const totalHeight = totalValue > 0 ? (totalValue / maxValue) * 100 : 0;

            // Calculate segment heights as percentages of the total bar height
            const completedPercentage = totalValue > 0 ? (completedValue / totalValue) * 100 : 0;
            const cancelledPercentage = totalValue > 0 ? (cancelledValue / totalValue) * 100 : 0;
            const pendingPercentage = totalValue > 0 ? (pendingValue / totalValue) * 100 : 0;

            return (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div className="w-full flex flex-col items-center relative group">
                  {/* Stacked bars - properly stacked from bottom to top */}
                  <div className="w-full relative" style={{ height: `${Math.max(totalHeight, 4)}px`, minHeight: '4px' }}>
                    {totalValue > 0 ? (
                      <>
                        {/* Pending appointments (bottom layer - gray) */}
                        <div
                          className="w-full bg-gray-300 dark:bg-gray-600 absolute bottom-0 rounded-t-sm"
                          style={{ height: `${pendingPercentage}%` }}
                        />
                        {/* Completed appointments (middle layer - green) */}
                        <div
                          className="w-full bg-green-500 absolute bottom-0 rounded-t-sm"
                          style={{
                            height: `${completedPercentage + pendingPercentage}%`,
                            clipPath: `inset(${pendingPercentage}% 0 0 0)`
                          }}
                        />
                        {/* Cancelled/NoShow appointments (top layer - red) */}
                        <div
                          className="w-full bg-red-500 absolute bottom-0 rounded-t-sm"
                          style={{
                            height: `${cancelledPercentage + completedPercentage + pendingPercentage}%`,
                            clipPath: `inset(${completedPercentage + pendingPercentage}% 0 0 0)`
                          }}
                        />
                      </>
                    ) : (
                      /* Empty state */
                      <div className="w-full bg-gray-100 dark:bg-gray-700 rounded-sm h-full" />
                    )}

                    {/* Tooltip */}
                    <div className="absolute -top-20 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs rounded py-2 px-3 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                      <div>{t('appointmentChart.total', 'Total')}: {totalValue}</div>
                      <div className="text-green-400">{t('appointmentChart.completed', 'Completed')}: {completedValue}</div>
                      <div className="text-red-400">{t('appointmentChart.cancelledAndNoShow', 'Cancelled/No Show')}: {cancelledValue}</div>
                      <div className="text-gray-400">Pending: {pendingValue}</div>
                    </div>
                  </div>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400 mt-2 font-medium">
                  {currentChartData.labels[index]}
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-4 gap-4 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="text-center">
          <div className="text-2xl font-bold text-brand-600 dark:text-brand-400">
            {appointmentStats.total}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {t('appointmentChart.totalAppointments', 'Total Appointments')}
          </div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {appointmentStats.completed}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {t('appointmentChart.completed', 'Completed')}
          </div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-red-600 dark:text-red-400">
            {appointmentStats.cancelledAndNoShow}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {t('appointmentChart.cancelledAndNoShow', 'Cancelled/No Show')}
          </div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
            {appointmentStats.completionRate}%
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {t('appointmentChart.completionRate', 'Completion Rate')}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppointmentAnalyticsChart;
