import React, { useEffect } from 'react';
import { useNavigate } from 'react-router';
import Button from '../ui/button/Button';
import { useQueues, useQueueStats } from '../../hooks/useQueues';
import { useDashboardTranslation } from '../../hooks/useTranslation';
import { LastUpdatedIndicator } from '../../hooks/useLastUpdated';

const QueueStatsWidget: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useDashboardTranslation();
  const { data: queuesData, isLoading: queuesLoading, refetch: refetchQueues, isFetching: queuesFetching } = useQueues();
  const { data: queueStats, isLoading: statsLoading, error: statsError, refetch: refetchStats, isFetching: statsFetching, dataUpdatedAt } = useQueueStats();

  // Force refresh when component mounts
  useEffect(() => {
    console.log('🔄 QueueStatsWidget component mounted - forcing refresh');
    refetchQueues();
    refetchStats();
  }, [refetchQueues, refetchStats]);

  const isLoading = queuesLoading || statsLoading;
  const isFetching = queuesFetching || statsFetching;
  const error = statsError;

  // Fallback stats calculation if API doesn't provide stats
  const fallbackStats = React.useMemo(() => {
    if (!queuesData) return null;

    const activeQueues = queuesData.filter(q => q.isActive);
    return {
      totalQueues: queuesData.length,
      activeQueues: activeQueues.length,
      totalCustomersToday: 0, // Would need separate API call
      averageWaitTime: 15, // Default fallback
      busyQueues: [],
      queueEfficiency: 85 // Default fallback
    };
  }, [queuesData]);

  const displayStats = queueStats || fallbackStats;

  const refetch = () => {
    refetchQueues();
    refetchStats();
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !displayStats) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {t('queueStats.errorTitle', 'Unable to load queue statistics')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {t('queueStats.errorMessage', 'Please try again later.')}
          </p>
        </div>
      </div>
    );
  }

  const formatWaitTime = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('queueStats.title', 'Queue Overview')}
          </h3>
          <LastUpdatedIndicator
            dataUpdatedAt={dataUpdatedAt}
            isLoading={isLoading}
            isFetching={isFetching}
          />
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => refetch()}
            disabled={isFetching}
            className="p-2"
            title={t('queueStats.refresh', 'Refresh queue statistics')}
          >
            <svg
              className={`w-4 h-4 ${isFetching ? 'animate-spin' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/queues')}
            className="text-brand-600 border-brand-300 hover:bg-brand-50 dark:text-brand-400 dark:border-brand-700 dark:hover:bg-brand-900/20"
          >
            {t('queueStats.viewAll', 'View All')}
          </Button>
        </div>
      </div>

      {/* Quick Stats Grid */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {displayStats.activeQueues}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {t('queueStats.activeQueues', 'Active Queues')}
          </div>
        </div>

        <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
            {displayStats.totalCustomersToday || 0}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {t('queueStats.customersWaiting', 'Customers Waiting')}
          </div>
        </div>

        <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {formatWaitTime(displayStats.averageWaitTime)}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {t('queueStats.avgWaitTime', 'Avg Wait Time')}
          </div>
        </div>

        <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
            {displayStats.totalQueues}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {t('queueStats.totalQueues', 'Total Queues')}
          </div>
        </div>
      </div>

      {/* Busy Queues Alert */}
      {displayStats.busyQueues && displayStats.busyQueues.length > 0 && (
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">
              {t('queueStats.busyQueues', 'Queues Needing Attention')}
            </h4>
          </div>
          <div className="space-y-2">
            {displayStats.busyQueues.map((queue) => (
              <div
                key={queue.id}
                className="flex items-center justify-between p-2 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800"
              >
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {queue.title}
                  </span>
                </div>
                <span className="text-xs text-red-600 dark:text-red-400">
                  {t('queueStats.highTraffic', 'High Traffic')}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* No active queues state */}
      {displayStats.activeQueues === 0 && (
        <div className="text-center py-4 border-t border-gray-200 dark:border-gray-700">
          <div className="w-12 h-12 mx-auto mb-3 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {t('queueStats.noActiveQueues', 'No active queues at the moment')}
          </p>
        </div>
      )}
    </div>
  );
};

export default QueueStatsWidget;
