import React from 'react';
import { CustomerStats } from '../../types/customer';

interface CustomerStatsOverviewProps {
  stats: CustomerStats;
}

export default function CustomerStatsOverview({ stats }: CustomerStatsOverviewProps) {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
        Customer Overview
      </h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {stats.totalCustomers}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Total Customers
          </div>
        </div>

        <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {stats.activeCustomers}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Active Customers
          </div>
        </div>

        <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
            {stats.newCustomersThisMonth}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            New This Month
          </div>
        </div>

        <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
            {Math.round(stats.customerRetentionRate)}%
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Retention Rate
          </div>
        </div>

        <div className="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
            {stats.averageVisitsPerCustomer.toFixed(1)}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Avg Visits
          </div>
        </div>

        <div className="text-center p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
          <div className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">
            ${Math.round(stats.averageSpendPerCustomer)}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Avg Spend
          </div>
        </div>
      </div>
    </div>
  );
}
