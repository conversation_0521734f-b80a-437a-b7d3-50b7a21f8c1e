import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Appointment } from '../../types';
import CalendarEventCard from './CalendarEventCard';
import { useCalendarTranslation } from '../../hooks/useTranslation';

interface CalendarSlotEventsProps {
  appointments: Appointment[];
  onEventClick?: (appointment: Appointment) => void;
  onModalStateChange?: (isOpen: boolean) => void;
  maxVisible?: number;
}

interface EventsListModalProps {
  appointments: Appointment[];
  onEventClick?: (appointment: Appointment) => void;
  onClose: () => void;
  position: { x: number; y: number };
}

const EventsListModal: React.FC<EventsListModalProps> = ({
  appointments,
  onEventClick,
  onClose,
  position
}) => {
  const handleModalMouseEnter = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const handleModalMouseLeave = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const handleModalClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return createPortal(
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 z-[9998]"
        onClick={onClose}
        onMouseEnter={handleModalMouseEnter}
        onMouseLeave={handleModalMouseLeave}
      />

      {/* Modal */}
      <div
        className="fixed z-[9999] bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl max-w-sm w-80"
        style={{
          left: Math.min(position.x, window.innerWidth - 320),
          top: Math.min(position.y, window.innerHeight - 400),
        }}
        onMouseEnter={handleModalMouseEnter}
        onMouseLeave={handleModalMouseLeave}
        onClick={handleModalClick}
      >
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
              {appointments.length} Appointments
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div className="space-y-2 max-h-80 overflow-y-auto">
            {appointments.map((appointment) => (
              <div
                key={appointment.id}
                className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  if (onEventClick) {
                    onEventClick(appointment);
                  }
                  onClose();
                }}
                onMouseEnter={(e) => e.stopPropagation()}
                onMouseLeave={(e) => e.stopPropagation()}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {appointment.service?.title || 'Service'}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 truncate">
                      {appointment.customer?.firstName} {appointment.customer?.lastName}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                      {appointment.service?.duration} min
                    </div>
                  </div>
                  <div className="flex-shrink-0 ml-2">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      appointment.status === 'confirmed' 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : appointment.status === 'pending'
                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        : appointment.status === 'completed'
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                        : appointment.status === 'InProgress'
                        ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                    }`}>
                      {appointment.status}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>,
    document.body
  );
};

const CalendarSlotEvents: React.FC<CalendarSlotEventsProps> = ({
  appointments,
  onEventClick,
  onModalStateChange,
  maxVisible = 3
}) => {
  const { t } = useCalendarTranslation();
  const [showAllEvents, setShowAllEvents] = useState(false);
  const [modalPosition, setModalPosition] = useState({ x: 0, y: 0 });

  const visibleAppointments = appointments.slice(0, maxVisible);
  const hiddenCount = Math.max(0, appointments.length - maxVisible);

  // Cleanup modal state when component unmounts
  useEffect(() => {
    return () => {
      if (showAllEvents && onModalStateChange) {
        onModalStateChange(false);
      }
    };
  }, [showAllEvents, onModalStateChange]);

  const handleMoreClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    const rect = e.currentTarget.getBoundingClientRect();
    setModalPosition({
      x: rect.left,
      y: rect.bottom + 5
    });
    setShowAllEvents(true);
    if (onModalStateChange) {
      onModalStateChange(true);
    }
  };

  const handleEventClick = (appointment: Appointment) => {
    if (onEventClick) {
      onEventClick(appointment);
    }
  };

  if (appointments.length === 0) {
    return null;
  }

  if (appointments.length === 1) {
    return (
      <CalendarEventCard
        appointment={appointments[0]}
        onClick={() => handleEventClick(appointments[0])}
      />
    );
  }

  return (
    <>
      <div className="h-full flex gap-0.5">
        {visibleAppointments.map((appointment, index) => (
          <div
            key={appointment.id}
            className="flex-1 min-w-0 rounded-md overflow-hidden"
            style={{
              backgroundColor: getAppointmentColor(appointment.status),
            }}
          >
            <CalendarEventCard
              appointment={appointment}
              isCompact={appointments.length > 1}
              onClick={() => handleEventClick(appointment)}
            />
          </div>
        ))}

        {hiddenCount > 0 && (
          <div
            className="flex-shrink-0 w-8 h-full cursor-pointer flex items-center justify-center bg-gray-600 hover:bg-gray-700 text-white text-xs font-medium rounded-md transition-colors"
            onClick={handleMoreClick}
            title={`${hiddenCount} ${hiddenCount > 1 ? t('tooltip.moreAppointmentsPlural') : t('tooltip.moreAppointments')}`}
          >
            +{hiddenCount}
          </div>
        )}
      </div>

      {showAllEvents && (
        <EventsListModal
          appointments={appointments}
          onEventClick={onEventClick}
          onClose={() => {
            setShowAllEvents(false);
            if (onModalStateChange) {
              onModalStateChange(false);
            }
          }}
          position={modalPosition}
        />
      )}
    </>
  );
};

// Helper function to get appointment color based on status
function getAppointmentColor(status: string): string {
  switch (status) {
    case 'pending':
      return '#F59E0B'; // yellow-500
    case 'confirmed':
      return '#10B981'; // green-500
    case 'completed':
      return '#3B82F6'; // blue-500
    case 'cancelled':
    case 'canceled':
      return '#EF4444'; // red-500
    case 'InProgress':
      return '#8B5CF6'; // purple
    case 'noshow':
    case 'no-show':
      return '#6B7280'; // gray
    default:
      return '#8B5CF6'; // purple
  }
}

export default CalendarSlotEvents;
