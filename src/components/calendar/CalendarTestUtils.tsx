import React from 'react';
import { Appointment } from '../../types/appointment';

/**
 * Utility component for testing calendar functionality with sample data
 * This can be used during development to test multiple events in the same time slot
 */

export const generateSampleAppointments = (): Appointment[] => {
  const baseDate = new Date();
  baseDate.setHours(10, 0, 0, 0); // Start at 10:00 AM today

  return [
    {
      id: 1,
      status: 'confirmed',
      expectedAppointmentStartTime: new Date(baseDate.getTime()).toISOString(),
      expectedAppointmentEndTime: new Date(baseDate.getTime() + 30 * 60000).toISOString(),
      serviceDuration: 30,
      customer: {
        id: 'customer-1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890'
      },
      service: {
        id: 1,
        title: 'Hair Cut',
        duration: 30,
        price: 50,
        color: '#10B981'
      }
    },
    {
      id: 2,
      status: 'pending',
      expectedAppointmentStartTime: new Date(baseDate.getTime() + 15 * 60000).toISOString(), // Overlapping: 10:15 AM
      expectedAppointmentEndTime: new Date(baseDate.getTime() + 45 * 60000).toISOString(),
      serviceDuration: 30,
      customer: {
        id: 'customer-2',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+1234567891'
      },
      service: {
        id: 2,
        title: 'Manicure',
        duration: 30,
        price: 35,
        color: '#F59E0B'
      }
    },
    {
      id: 3,
      status: 'InProgress',
      expectedAppointmentStartTime: new Date(baseDate.getTime() + 30 * 60000).toISOString(), // 10:30 AM
      expectedAppointmentEndTime: new Date(baseDate.getTime() + 90 * 60000).toISOString(),
      serviceDuration: 60,
      customer: {
        id: 'customer-3',
        firstName: 'Bob',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '+1234567892'
      },
      service: {
        id: 3,
        title: 'Hair Color',
        duration: 60,
        price: 120,
        color: '#8B5CF6'
      }
    },
    {
      id: 4,
      status: 'confirmed',
      expectedAppointmentStartTime: new Date(baseDate.getTime() + 120 * 60000).toISOString(), // 12:00 PM
      expectedAppointmentEndTime: new Date(baseDate.getTime() + 150 * 60000).toISOString(),
      serviceDuration: 30,
      customer: {
        id: 'customer-4',
        firstName: 'Alice',
        lastName: 'Brown',
        email: '<EMAIL>',
        phone: '+1234567893'
      },
      service: {
        id: 4,
        title: 'Facial',
        duration: 30,
        price: 80,
        color: '#3B82F6'
      }
    },
    {
      id: 5,
      status: 'confirmed',
      expectedAppointmentStartTime: new Date(baseDate.getTime() + 135 * 60000).toISOString(), // Overlapping: 12:15 PM
      expectedAppointmentEndTime: new Date(baseDate.getTime() + 165 * 60000).toISOString(),
      serviceDuration: 30,
      customer: {
        id: 'customer-5',
        firstName: 'Charlie',
        lastName: 'Wilson',
        email: '<EMAIL>',
        phone: '+1234567894'
      },
      service: {
        id: 5,
        title: 'Massage',
        duration: 30,
        price: 90,
        color: '#10B981'
      }
    },
    {
      id: 6,
      status: 'pending',
      expectedAppointmentStartTime: new Date(baseDate.getTime() + 150 * 60000).toISOString(), // Overlapping: 12:30 PM
      expectedAppointmentEndTime: new Date(baseDate.getTime() + 180 * 60000).toISOString(),
      serviceDuration: 30,
      customer: {
        id: 'customer-6',
        firstName: 'Diana',
        lastName: 'Davis',
        email: '<EMAIL>',
        phone: '+1234567895'
      },
      service: {
        id: 6,
        title: 'Pedicure',
        duration: 30,
        price: 45,
        color: '#EF4444'
      }
    }
  ];
};

/**
 * Component to display sample appointments for testing
 */
interface CalendarTestPanelProps {
  onLoadSampleData: (appointments: Appointment[]) => void;
  onClearData: () => void;
}

export const CalendarTestPanel: React.FC<CalendarTestPanelProps> = ({
  onLoadSampleData,
  onClearData
}) => {
  const handleLoadSampleData = () => {
    const sampleAppointments = generateSampleAppointments();
    onLoadSampleData(sampleAppointments);
  };

  return (
    <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
            Calendar Testing
          </h3>
          <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-1">
            Load sample appointments to test the calendar functionality, including overlapping events.
          </p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={handleLoadSampleData}
            className="px-3 py-1.5 text-xs font-medium text-yellow-800 dark:text-yellow-200 bg-yellow-100 dark:bg-yellow-800 hover:bg-yellow-200 dark:hover:bg-yellow-700 rounded-md transition-colors"
          >
            Load Sample Data
          </button>
          <button
            onClick={onClearData}
            className="px-3 py-1.5 text-xs font-medium text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors"
          >
            Clear
          </button>
        </div>
      </div>
    </div>
  );
};

export default CalendarTestPanel;
