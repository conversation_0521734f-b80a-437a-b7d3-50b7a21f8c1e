import React, { useState } from 'react';

// Mock data - in real app, this would come from API
const mockData = {
  daily: [
    { date: 'Mon', appointments: 8, revenue: 320 },
    { date: 'Tue', appointments: 12, revenue: 480 },
    { date: 'Wed', appointments: 6, revenue: 240 },
    { date: 'Thu', appointments: 15, revenue: 600 },
    { date: 'Fri', appointments: 18, revenue: 720 },
    { date: 'Sat', appointments: 22, revenue: 880 },
    { date: 'Sun', appointments: 10, revenue: 400 },
  ],
  weekly: [
    { date: 'Week 1', appointments: 85, revenue: 3400 },
    { date: 'Week 2', appointments: 92, revenue: 3680 },
    { date: 'Week 3', appointments: 78, revenue: 3120 },
    { date: 'Week 4', appointments: 105, revenue: 4200 },
  ],
  monthly: [
    { date: 'Jan', appointments: 320, revenue: 12800 },
    { date: 'Feb', appointments: 285, revenue: 11400 },
    { date: 'Mar', appointments: 410, revenue: 16400 },
    { date: 'Apr', appointments: 380, revenue: 15200 },
    { date: 'May', appointments: 445, revenue: 17800 },
    { date: 'Jun', appointments: 390, revenue: 15600 },
  ],
};

export default function AppointmentChart() {
  const [activeTab, setActiveTab] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const [activeMetric, setActiveMetric] = useState<'appointments' | 'revenue'>('appointments');

  const data = mockData[activeTab];
  const maxValue = Math.max(...data.map(item => item[activeMetric]));

  const tabs = [
    { key: 'daily' as const, label: 'Daily' },
    { key: 'weekly' as const, label: 'Weekly' },
    { key: 'monthly' as const, label: 'Monthly' },
  ];

  const metrics = [
    { key: 'appointments' as const, label: 'Appointments', color: 'bg-blue-500' },
    { key: 'revenue' as const, label: 'Revenue', color: 'bg-green-500' },
  ];

  return (
    <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
              Appointments & Revenue
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Track your business performance over time
            </p>
          </div>
          
          {/* Metric Toggle */}
          <div className="flex rounded-lg border border-gray-200 dark:border-gray-700 p-1">
            {metrics.map((metric) => (
              <button
                key={metric.key}
                onClick={() => setActiveMetric(metric.key)}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  activeMetric === metric.key
                    ? 'bg-brand-500 text-white'
                    : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                {metric.label}
              </button>
            ))}
          </div>
        </div>

        {/* Time Period Tabs */}
        <div className="flex space-x-1 rounded-lg border border-gray-200 dark:border-gray-700 p-1">
          {tabs.map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === tab.key
                  ? 'bg-brand-500 text-white'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Chart */}
      <div className="relative">
        <div className="flex items-end justify-between h-64 space-x-2">
          {data.map((item, index) => {
            const height = (item[activeMetric] / maxValue) * 100;
            const colorClass = activeMetric === 'appointments' ? 'bg-blue-500' : 'bg-green-500';
            
            return (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div className="relative w-full group">
                  <div
                    className={`w-full ${colorClass} rounded-t-md transition-all duration-300 hover:opacity-80 cursor-pointer`}
                    style={{ height: `${height}%` }}
                  >
                    {/* Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                      {activeMetric === 'appointments' 
                        ? `${item.appointments} appointments`
                        : `$${item.revenue.toLocaleString()}`
                      }
                    </div>
                  </div>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400 mt-2 font-medium">
                  {item.date}
                </span>
              </div>
            );
          })}
        </div>

        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 h-64 flex flex-col justify-between text-xs text-gray-500 dark:text-gray-400 -ml-8">
          <span>{maxValue}</span>
          <span>{Math.round(maxValue * 0.75)}</span>
          <span>{Math.round(maxValue * 0.5)}</span>
          <span>{Math.round(maxValue * 0.25)}</span>
          <span>0</span>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="mt-6 grid grid-cols-2 gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="text-center">
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {data.reduce((sum, item) => sum + item.appointments, 0)}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Total Appointments
          </p>
        </div>
        <div className="text-center">
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            ${data.reduce((sum, item) => sum + item.revenue, 0).toLocaleString()}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Total Revenue
          </p>
        </div>
      </div>
    </div>
  );
}
