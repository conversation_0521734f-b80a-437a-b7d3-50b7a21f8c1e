import React from 'react';

// Mock data - in real app, this would come from API
const mockServices = [
  {
    id: 1,
    name: 'Hair Cut & Style',
    bookings: 45,
    revenue: 1800,
    rating: 4.8,
    color: '#3B82F6',
    trend: 12.5,
  },
  {
    id: 2,
    name: 'Color Treatment',
    bookings: 32,
    revenue: 2400,
    rating: 4.9,
    color: '#10B981',
    trend: 8.3,
  },
  {
    id: 3,
    name: 'Consultation',
    bookings: 28,
    revenue: 840,
    rating: 4.7,
    color: '#F59E0B',
    trend: -2.1,
  },
  {
    id: 4,
    name: 'Deep Conditioning',
    bookings: 18,
    revenue: 720,
    rating: 4.6,
    color: '#8B5CF6',
    trend: 15.7,
  },
  {
    id: 5,
    name: 'Styling Session',
    bookings: 15,
    revenue: 600,
    rating: 4.5,
    color: '#EF4444',
    trend: 5.2,
  },
];

export default function ServicePerformance() {
  const totalBookings = mockServices.reduce((sum, service) => sum + service.bookings, 0);
  const totalRevenue = mockServices.reduce((sum, service) => sum + service.revenue, 0);

  return (
    <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
              Service Performance
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Top performing services this month
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500 dark:text-gray-400">Total</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {totalBookings} bookings
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        {mockServices.map((service, index) => {
          const bookingPercentage = (service.bookings / totalBookings) * 100;
          
          return (
            <div key={service.id} className="relative">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-3">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: service.color }}
                  />
                  <span className="font-medium text-gray-900 dark:text-white">
                    {service.name}
                  </span>
                </div>
                <div className="flex items-center space-x-4 text-sm">
                  <span className="text-gray-500 dark:text-gray-400">
                    {service.bookings} bookings
                  </span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    ${service.revenue.toLocaleString()}
                  </span>
                  <div className="flex items-center space-x-1">
                    <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                    </svg>
                    <span className="text-gray-600 dark:text-gray-400">
                      {service.rating}
                    </span>
                  </div>
                  <span
                    className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                      service.trend >= 0
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                        : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                    }`}
                  >
                    {service.trend >= 0 ? '+' : ''}{service.trend}%
                  </span>
                </div>
              </div>
              
              {/* Progress bar */}
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${bookingPercentage}%`,
                    backgroundColor: service.color,
                  }}
                />
              </div>
              
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                <span>{bookingPercentage.toFixed(1)}% of total bookings</span>
                <span>Rank #{index + 1}</span>
              </div>
            </div>
          );
        })}
      </div>

      {/* Summary */}
      <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {mockServices.length}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Active Services
            </p>
          </div>
          <div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {totalBookings}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Total Bookings
            </p>
          </div>
          <div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              ${totalRevenue.toLocaleString()}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Total Revenue
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
