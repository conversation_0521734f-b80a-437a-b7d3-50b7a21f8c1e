import React from 'react';
import { useAuth } from '../../context/AuthContext';

// Mock data - in real app, this would come from API
const mockBusinessData = {
  setupCompletion: 85,
  nextSteps: [
    { task: 'Add business hours', completed: false, priority: 'high' },
    { task: 'Upload business photos', completed: false, priority: 'medium' },
    { task: 'Set up payment methods', completed: true, priority: 'high' },
    { task: 'Configure notifications', completed: false, priority: 'low' },
  ],
  recentActivity: [
    { action: 'New customer registration', time: '2 hours ago', type: 'customer' },
    { action: 'Service "Hair Cut" updated', time: '4 hours ago', type: 'service' },
    { action: 'Location hours modified', time: '1 day ago', type: 'location' },
    { action: 'Profile information updated', time: '2 days ago', type: 'profile' },
  ],
  businessHealth: {
    score: 78,
    factors: [
      { name: 'Profile Completion', score: 85, status: 'good' },
      { name: 'Service Availability', score: 92, status: 'excellent' },
      { name: 'Customer Satisfaction', score: 88, status: 'good' },
      { name: 'Response Time', score: 45, status: 'needs-improvement' },
    ],
  },
};

export default function BusinessOverview() {
  const { provider } = useAuth();

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'customer':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        );
      case 'service':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        );
      case 'location':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'text-green-600 dark:text-green-400';
      case 'good':
        return 'text-blue-600 dark:text-blue-400';
      case 'needs-improvement':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  return (
    <div className="space-y-6">
      {/* Setup Progress */}
      <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
            Business Setup
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Complete your profile to attract more customers
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm mb-2">
            <span className="text-gray-600 dark:text-gray-400">Setup Progress</span>
            <span className="font-medium text-gray-900 dark:text-white">
              {mockBusinessData.setupCompletion}%
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div
              className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-300"
              style={{ width: `${mockBusinessData.setupCompletion}%` }}
            />
          </div>
        </div>

        {/* Next Steps */}
        <div>
          <h4 className="font-medium text-gray-900 dark:text-white mb-3">
            Next Steps
          </h4>
          <div className="space-y-2">
            {mockBusinessData.nextSteps.map((step, index) => (
              <div
                key={index}
                className={`flex items-center justify-between p-3 rounded-lg border ${
                  step.completed
                    ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
                    : 'bg-gray-50 border-gray-200 dark:bg-gray-800 dark:border-gray-700'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div
                    className={`w-5 h-5 rounded-full flex items-center justify-center ${
                      step.completed
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-300 dark:bg-gray-600'
                    }`}
                  >
                    {step.completed && (
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                  <span
                    className={`text-sm ${
                      step.completed
                        ? 'text-green-800 dark:text-green-400 line-through'
                        : 'text-gray-900 dark:text-white'
                    }`}
                  >
                    {step.task}
                  </span>
                </div>
                <span
                  className={`px-2 py-1 text-xs rounded-full font-medium ${getPriorityColor(
                    step.priority
                  )}`}
                >
                  {step.priority}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Business Health Score */}
      <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
                Business Health
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Overall business performance score
              </p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 dark:text-white">
                {mockBusinessData.businessHealth.score}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Health Score
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          {mockBusinessData.businessHealth.factors.map((factor, index) => (
            <div key={index} className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {factor.name}
              </span>
              <div className="flex items-center space-x-3">
                <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full"
                    style={{ width: `${factor.score}%` }}
                  />
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-white w-8">
                  {factor.score}
                </span>
                <span className={`text-xs font-medium ${getStatusColor(factor.status)}`}>
                  {factor.status.replace('-', ' ')}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
