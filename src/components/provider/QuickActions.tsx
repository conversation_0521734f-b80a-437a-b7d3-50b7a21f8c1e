import React, { useState } from 'react';
import { useNavigate } from 'react-router';
import { Modal } from '../ui/modal';
import AppointmentForm from '../appointments/AppointmentForm';
import ProviderCustomerForm from '../customers/ProviderCustomerForm';
import { useDashboardTranslation } from '../../hooks/useTranslation';

export default function QuickActions() {
  const navigate = useNavigate();
  const { t } = useDashboardTranslation();
  const [isAppointmentModalOpen, setIsAppointmentModalOpen] = useState(false);
  const [isCustomerModalOpen, setIsCustomerModalOpen] = useState(false);

  const handleNewAppointment = () => {
    setIsAppointmentModalOpen(true);
  };

  const handleCloseAppointmentModal = () => {
    setIsAppointmentModalOpen(false);
  };

  const handleAppointmentSuccess = () => {
    setIsAppointmentModalOpen(false);
  };

  const handleNewCustomer = () => {
    setIsCustomerModalOpen(true);
  };

  const handleCloseCustomerModal = () => {
    setIsCustomerModalOpen(false);
  };

  const handleCustomerSuccess = () => {
    setIsCustomerModalOpen(false);
  };

  const actions = [
    {
      title: t('quickActions.newAppointment'),
      description: t('quickActions.bookAppointment', 'Book a new appointment'),
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      ),
      action: handleNewAppointment,
      color: 'bg-brand-500 hover:bg-brand-600',
    },
    {
      title: t('quickActions.addCustomer', 'Add Customer'),
      description: t('quickActions.addNewCustomer', 'Add a new customer to your database'),
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
        </svg>
      ),
      action: handleNewCustomer,
      color: 'bg-purple-500 hover:bg-purple-600',
    },
    {
      title: t('quickActions.viewCalendar'),
      description: t('quickActions.checkSchedule', 'Check schedule & appointments'),
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      action: () => navigate('/calendar'),
      color: 'bg-blue-500 hover:bg-blue-600',
    },
    {
      title: t('quickActions.manageServices'),
      description: t('quickActions.addEditServices', 'Add or edit services'),
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      action: () => navigate('/services'),
      color: 'bg-green-500 hover:bg-green-600',
    }
  ];

  return (
    <>
      <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
            {t('quickActions.title')}
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t('quickActions.subtitle', 'Common tasks and shortcuts')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {actions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className="flex flex-col items-center p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-brand-300 dark:hover:border-brand-600 transition-all duration-200 group hover:shadow-md"
            >
              <div className={`p-3 rounded-lg ${action.color} text-white mb-3 group-hover:scale-110 transition-transform`}>
                {action.icon}
              </div>
              <div className="text-center">
                <h4 className="font-medium text-gray-900 dark:text-white group-hover:text-brand-600 dark:group-hover:text-brand-400 mb-1">
                  {action.title}
                </h4>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {action.description}
                </p>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* New Appointment Modal */}
      <Modal
        isOpen={isAppointmentModalOpen}
        onClose={handleCloseAppointmentModal}
        className="max-w-[800px] p-0"
      >
        <AppointmentForm
          onClose={handleCloseAppointmentModal}
          onSuccess={handleAppointmentSuccess}
        />
      </Modal>

      {/* New Customer Modal */}
      <Modal
        isOpen={isCustomerModalOpen}
        onClose={handleCloseCustomerModal}
        className="max-w-[800px] p-0"
        showCloseButton={false}
      >
        <ProviderCustomerForm
          onClose={handleCloseCustomerModal}
          onSuccess={handleCustomerSuccess}
        />
      </Modal>
    </>
  );
}
