import React from 'react';
import Button from '../ui/button/Button';
import { Queue } from '../../types/queue';
import { useToggleQueueStatus } from '../../hooks/useQueues';
import { useAppointments } from '../../hooks/useAppointments';
import { useManagementTranslation } from '../../hooks/useTranslation';

interface QueueCardProps {
  queue: Queue;
  onView: () => void;
  onEdit: () => void;
  onDelete: () => void;
}

export default function QueueCard({ queue, onView, onEdit, onDelete }: QueueCardProps) {
  const { data: allAppointments } = useAppointments();
  const toggleStatusMutation = useToggleQueueStatus();
  const { t, currentLanguage } = useManagementTranslation();

  // Filter appointments for this specific queue
  const queueAppointments = allAppointments?.filter(appointment =>
    appointment.queue?.id === queue.id
  ) || [];

  // Calculate queue statistics
  const confirmedAppointments = queueAppointments.filter(apt => apt.status === 'confirmed');
  const activeSession = queueAppointments.find(apt => apt.status === 'InProgress');

  // Get today's appointments
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const todayAppointments = queueAppointments.filter(apt => {
    const appointmentDate = new Date(apt.expectedAppointmentStartTime);
    return appointmentDate >= today && appointmentDate < tomorrow &&
           (apt.status === 'confirmed' || apt.status === 'pending');
  });

  // Custom translations for queue cards
  const cardTranslations = {
    ar: {
      active: "نشط",
      inactive: "غير نشط",
      highPriority: "أولوية عالية",
      mediumPriority: "أولوية متوسطة",
      lowPriority: "أولوية منخفضة",
      confirmed: "مؤكد",
      todayAppointments: "اليوم",
      activeSession: "جلسة نشطة",
      noActiveSession: "لا توجد جلسة",
      services: "الخدمات ({count})",
      service: "خدمة {index}",
      moreServices: "+{count} أخرى",
      edit: "تعديل",
      delete: "حذف",
      activateQueue: "تفعيل قائمة الانتظار",
      deactivateQueue: "إلغاء تفعيل قائمة الانتظار",
      updated: "تم التحديث {date}",
      location: "الموقع"
    },
    en: {
      active: "Active",
      inactive: "Inactive",
      highPriority: "High Priority",
      mediumPriority: "Medium Priority",
      lowPriority: "Low Priority",
      confirmed: "Confirmed",
      todayAppointments: "Today",
      activeSession: "Active Session",
      noActiveSession: "No Session",
      services: "Services ({count})",
      service: "Service {index}",
      moreServices: "+{count} more",
      edit: "Edit",
      delete: "Delete",
      activateQueue: "Activate Queue",
      deactivateQueue: "Deactivate Queue",
      updated: "Updated {date}",
      location: "Location"
    },
    fr: {
      active: "Actif",
      inactive: "Inactif",
      highPriority: "Priorité élevée",
      mediumPriority: "Priorité moyenne",
      lowPriority: "Priorité faible",
      confirmed: "Confirmé",
      todayAppointments: "Aujourd'hui",
      activeSession: "Session active",
      noActiveSession: "Aucune session",
      services: "Services ({count})",
      service: "Service {index}",
      moreServices: "+{count} de plus",
      edit: "Modifier",
      delete: "Supprimer",
      activateQueue: "Activer la file d'attente",
      deactivateQueue: "Désactiver la file d'attente",
      updated: "Mis à jour {date}",
      location: "Emplacement"
    }
  };

  const currentLang = currentLanguage as keyof typeof cardTranslations;
  const ct = (key: keyof typeof cardTranslations.ar, params?: { count?: number; index?: number; max?: number; date?: string }) => {
    let text = cardTranslations[currentLang]?.[key] || cardTranslations.en[key] || key;
    if (params) {
      Object.entries(params).forEach(([param, value]) => {
        text = text.replace(`{${param}}`, value?.toString() || '');
      });
    }
    return text;
  };

  const handleToggleStatus = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await toggleStatusMutation.mutateAsync({
        id: queue.id,
        isActive: !queue.isActive
      });
    } catch (error) {
      // Error handled by mutation
    }
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive 
      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  const confirmedCount = confirmedAppointments.length;
  const todayCount = todayAppointments.length;

  return (
    <div 
      className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow cursor-pointer"
      onClick={onView}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
            {queue.title}
          </h3>
          <div className="flex items-center space-x-2 mt-1">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(queue.isActive)}`}>
              {queue.isActive ? ct('active') : ct('inactive')}
            </span>
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(queue.priority || 'medium')}`}>
              {queue.priority === 'high' ? ct('highPriority') :
               queue.priority === 'low' ? ct('lowPriority') :
               ct('mediumPriority')}
            </span>
          </div>
        </div>
        
        <div className="flex items-center space-x-2 ml-4">
          <button
            onClick={handleToggleStatus}
            disabled={toggleStatusMutation.isPending}
            className={`p-2 rounded-lg transition-colors ${
              queue.isActive 
                ? 'text-green-600 hover:bg-green-50 dark:text-green-400 dark:hover:bg-green-900/20'
                : 'text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
            }`}
            title={queue.isActive ? ct('deactivateQueue') : ct('activateQueue')}
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>

      {/* Queue Metrics */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="text-xl font-bold text-blue-600 dark:text-blue-400">
            {confirmedCount}
          </div>
          <div className="text-sm text-blue-700 dark:text-blue-300">
            {ct('confirmed')}
          </div>
        </div>

        <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div className="text-xl font-bold text-green-600 dark:text-green-400">
            {todayCount}
          </div>
          <div className="text-sm text-green-700 dark:text-green-300">
            {ct('todayAppointments')}
          </div>
        </div>
      </div>

      {/* Active Session Indicator */}
      <div className="mb-4">
        <div className={`flex items-center justify-center p-3 rounded-lg ${
          activeSession
            ? 'bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800'
            : 'bg-gray-50 dark:bg-gray-700'
        }`}>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${
              activeSession ? 'bg-orange-500 animate-pulse' : 'bg-gray-400'
            }`}></div>
            <span className={`text-sm font-medium ${
              activeSession
                ? 'text-orange-700 dark:text-orange-300'
                : 'text-gray-600 dark:text-gray-400'
            }`}>
              {activeSession ? ct('activeSession') : ct('noActiveSession')}
            </span>
          </div>
        </div>
      </div>

      {/* Location */}
      {(queue.sProvidingPlace?.title || queue.location?.title) && (
        <div className="mb-4">
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span className="truncate">
              {queue.sProvidingPlace?.title || queue.location?.title}
            </span>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex justify-center space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
        <Button
          onClick={(e) => {
            e.stopPropagation();
            onEdit();
          }}
          variant="outline"
          size="sm"
          className="text-xs"
        >
          {ct('edit')}
        </Button>
        <Button
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
          }}
          variant="outline"
          size="sm"
          className="text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20 text-xs"
        >
          {ct('delete')}
        </Button>
      </div>
    </div>
  );
}
