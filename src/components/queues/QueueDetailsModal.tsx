import React, { useState, useEffect } from 'react';
import Button from '../ui/button/Button';
import { Queue } from '../../types/queue';
import { Appointment } from '../../types/appointment';
import { useAppointments } from '../../hooks/useAppointments';
import { useManagementTranslation, useCommonTranslation } from '../../hooks/useTranslation';
import { useRTL } from '../../context/LanguageContext';
import { formatLocalTimeWithLocale, formatLocalDateWithLocale } from '../../utils/timezone';

interface QueueDetailsModalProps {
  queue: Queue;
  onClose: () => void;
  onEdit: () => void;
}

export default function QueueDetailsModal({ queue, onClose, onEdit }: QueueDetailsModalProps) {
  const { t, currentLanguage } = useManagementTranslation();
  const { t: tCommon } = useCommonTranslation();
  const { isRTL, direction } = useRTL();
  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null);

  // Fetch all appointments and filter by queue
  const { data: allAppointments, isLoading: appointmentsLoading } = useAppointments();

  // Filter appointments for this specific queue
  const queueAppointments = allAppointments?.filter(appointment =>
    appointment.queue?.id === queue.id
  ) || [];

  // Get active appointment for this queue
  const queueActiveSession = queueAppointments.find(appointment =>
    appointment.status === 'InProgress'
  ) || null;

  // Initialize timer for active session
  useEffect(() => {
    if (queueActiveSession && queueActiveSession.status === 'InProgress') {
      const duration = queueActiveSession.service?.duration || 30;
      const startTime = queueActiveSession.realAppointmentStartTime
        ? new Date(queueActiveSession.realAppointmentStartTime)
        : new Date();

      setSessionStartTime(startTime);
      const endTime = new Date(startTime.getTime() + duration * 60000);
      const remaining = Math.max(0, endTime.getTime() - Date.now());
      setTimeRemaining(Math.floor(remaining / 1000));
    }
  }, [queueActiveSession]);

  // Timer countdown effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (queueActiveSession && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(prev => Math.max(0, prev - 1));
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [queueActiveSession, timeRemaining]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Calculate statistics from filtered appointments
  const confirmedAppointments = queueAppointments.filter(apt => apt.status === 'confirmed');
  const confirmedCount = confirmedAppointments.length;

  // Get upcoming appointments (confirmed and pending, sorted by expected start time)
  const upcomingAppointments = queueAppointments
    .filter(apt => apt.status === 'confirmed' || apt.status === 'pending')
    .sort((a, b) => new Date(a.expectedAppointmentStartTime).getTime() - new Date(b.expectedAppointmentStartTime).getTime())
    .slice(0, 5);

  // Get today's appointments for "upcoming today" count
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const todayAppointments = queueAppointments.filter(apt => {
    const appointmentDate = new Date(apt.expectedAppointmentStartTime);
    return appointmentDate >= today && appointmentDate < tomorrow &&
           (apt.status === 'confirmed' || apt.status === 'pending');
  });

  const getStatusColor = (isActive: boolean) => {
    return isActive 
      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
      : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return (
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
        );
      case 'pending':
        return (
          <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
        );
      case 'InProgress':
        return (
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
        );
      default:
        return (
          <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
        );
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-hidden" dir={direction}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white truncate">
              {queue.title}
            </h2>
            <div className="flex items-center space-x-2 mt-1">
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(queue.isActive)}`}>
                {queue.isActive ? tCommon('active') : tCommon('inactive')}
              </span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('queues.location')}: {queue.sProvidingPlace?.title || queue.location?.title}
              </span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onEdit}
            >
              {tCommon('actions.edit')}
            </Button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Active Session & Stats */}
          <div className="space-y-6">
            {/* Active Session */}
            {queueActiveSession ? (
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-medium text-green-900 dark:text-green-100">
                    {t('queues.queueDetails.activeSession')}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-sm text-green-700 dark:text-green-300">
                      {t('queues.queueDetails.inProgress')}
                    </span>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-green-700 dark:text-green-300">
                      {t('queues.queueDetails.customer')}:
                    </span>
                    <span className="font-medium text-green-900 dark:text-green-100">
                      {queueActiveSession.customer?.firstName} {queueActiveSession.customer?.lastName}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-green-700 dark:text-green-300">
                      {t('queues.queueDetails.service')}:
                    </span>
                    <span className="font-medium text-green-900 dark:text-green-100">
                      {queueActiveSession.service?.title}
                    </span>
                  </div>

                  {timeRemaining > 0 && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-green-700 dark:text-green-300">
                        {t('queues.queueDetails.timeRemaining')}:
                      </span>
                      <span className="font-mono text-lg font-bold text-green-900 dark:text-green-100">
                        {formatTime(timeRemaining)}
                      </span>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-green-700 dark:text-green-300">
                      {t('queues.queueDetails.started')}:
                    </span>
                    <span className="font-medium text-green-900 dark:text-green-100">
                      {sessionStartTime ? formatLocalTimeWithLocale(sessionStartTime.toISOString(), {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                      }, currentLanguage) : 'Now'}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-3 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">
                    {t('queues.queueDetails.noActiveSession')}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {t('queues.queueDetails.noActiveSessionDesc')}
                  </p>
                </div>
              </div>
            )}

            {/* Queue Stats */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                {t('queues.queueDetails.queueStats')}
              </h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {confirmedCount}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {t('queues.queueDetails.confirmedAppointments')}
                  </div>
                </div>

                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {todayAppointments.length}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {t('queues.queueDetails.upcomingToday')}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Upcoming Appointments */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                {t('queues.queueDetails.upcomingAppointments')}
                <span className="text-sm font-normal text-gray-600 dark:text-gray-400 ml-2">
                  ({t('queues.queueDetails.next5')})
                </span>
              </h3>

              {appointmentsLoading ? (
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                    </div>
                  ))}
                </div>
              ) : upcomingAppointments.length === 0 ? (
                <div className="text-center py-8">
                  <div className="w-12 h-12 mx-auto mb-3 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {t('queues.queueDetails.noUpcomingAppointments')}
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {upcomingAppointments.map((appointment, index) => (
                    <div key={appointment.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-full text-sm font-medium">
                          {index + 1}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {appointment.customer?.firstName} {appointment.customer?.lastName}
                          </p>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            {appointment.service?.title}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(appointment.status)}
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {formatLocalTimeWithLocale(appointment.expectedAppointmentStartTime, {
                              hour: '2-digit',
                              minute: '2-digit',
                              hour12: false
                            }, currentLanguage)}
                          </p>
                        </div>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {formatLocalDateWithLocale(appointment.expectedAppointmentStartTime, {
                            month: 'short',
                            day: 'numeric'
                          }, currentLanguage)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
        <div className="flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={onClose}
          >
            {tCommon('actions.close')}
          </Button>
          <Button onClick={onEdit}>
            {tCommon('actions.edit')}
          </Button>
        </div>
      </div>
    </div>
  );
}
