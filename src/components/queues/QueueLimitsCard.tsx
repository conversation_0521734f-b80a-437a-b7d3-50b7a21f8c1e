import React from 'react';
import { QueueLimits } from '../../types/queue';
import { CheckCircleIcon, AlertIcon, InfoIcon } from '../../icons';
import { useManagementTranslation } from '../../hooks/useTranslation';

interface QueueLimitsCardProps {
  limits: QueueLimits;
}

export default function QueueLimitsCard({ limits }: QueueLimitsCardProps) {
  const { t, currentLanguage } = useManagementTranslation();

  // Custom translations for queue limits widget
  const limitsTranslations = {
    ar: {
      queueLimits: "حدود قوائم الانتظار",
      queuesUsed: "قوائم الانتظار المستخدمة",
      currentPlan: "الخطة الحالية",
      planFeatures: "ميزات الخطة",
      realTimeUpdates: "التحديثات الفورية",
      advancedAnalytics: "التحليلات المتقدمة",
      prioritySupport: "الدعم المتقدم",
      queueLimitReached: "تم الوصول إلى حد قوائم الانتظار",
      upgradeToCreateMore: "قم بترقية خطتك لإنشاء المزيد من قوائم الانتظار وفتح ميزات إضافية.",
      readyToCreate: "جاهز للإنشاء",
      canCreateMore: "يمكنك إنشاء {count} قائمة انتظار إضافية.",
      unlimited: "غير محدود",
      free: "مجاني"
    },
    en: {
      queueLimits: "Queue Limits",
      queuesUsed: "Queues Used",
      currentPlan: "Current Plan",
      planFeatures: "Plan Features",
      realTimeUpdates: "Real-time Updates",
      advancedAnalytics: "Advanced Analytics",
      prioritySupport: "Priority Support",
      queueLimitReached: "Queue Limit Reached",
      upgradeToCreateMore: "Upgrade your plan to create more queues and unlock additional features.",
      readyToCreate: "Ready to Create",
      canCreateMore: "You can create {count} more queue(s).",
      unlimited: "unlimited",
      free: "Free"
    },
    fr: {
      queueLimits: "Limites des files d'attente",
      queuesUsed: "Files d'attente utilisées",
      currentPlan: "Plan actuel",
      planFeatures: "Fonctionnalités du plan",
      realTimeUpdates: "Mises à jour en temps réel",
      advancedAnalytics: "Analyses avancées",
      prioritySupport: "Support prioritaire",
      queueLimitReached: "Limite de file d'attente atteinte",
      upgradeToCreateMore: "Mettez à niveau votre plan pour créer plus de files d'attente et débloquer des fonctionnalités supplémentaires.",
      readyToCreate: "Prêt à créer",
      canCreateMore: "Vous pouvez créer {count} file(s) d'attente supplémentaire(s).",
      unlimited: "illimité",
      free: "Gratuit"
    }
  };

  const currentLang = currentLanguage as keyof typeof limitsTranslations;
  const lt = (key: keyof typeof limitsTranslations.ar, params?: { count?: number }) => {
    let text = limitsTranslations[currentLang]?.[key] || limitsTranslations.en[key] || key;
    if (params?.count !== undefined) {
      text = text.replace('{count}', params.count.toString());
    }
    return text;
  };

  // Add defensive checks for incomplete data
  if (!limits) {
    return null;
  }

  // Provide default values for missing properties
  const safeFeatures = limits.features || {
    realTimeUpdates: false,
    advancedAnalytics: false,
    prioritySupport: false,
  };

  // Provide default values for other properties
  const currentCount = limits.currentCount || 0;
  const maxAllowed = limits.maxAllowed || 0;
  const canCreate = limits.canCreate ?? false;
  const upgradeRequired = limits.upgradeRequired ?? false;
  const tier = limits.tier || 'Free';

  const getStatusColor = () => {
    if (canCreate) {
      return 'text-green-600 dark:text-green-400';
    } else if (upgradeRequired) {
      return 'text-red-600 dark:text-red-400';
    }
    return 'text-yellow-600 dark:text-yellow-400';
  };

  const getStatusIcon = () => {
    if (canCreate) {
      return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
    } else if (upgradeRequired) {
      return <AlertIcon className="w-5 h-5 text-red-500" />;
    }
    return <InfoIcon className="w-5 h-5 text-yellow-500" />;
  };

  const getProgressPercentage = () => {
    if (maxAllowed === -1) return 0; // Unlimited
    return maxAllowed > 0 ? (currentCount / maxAllowed) * 100 : 0;
  };

  const getProgressColor = () => {
    const percentage = getProgressPercentage();
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {lt('queueLimits')}
        </h3>
        {getStatusIcon()}
      </div>

      {/* Current Usage */}
      <div className="space-y-4">
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {lt('queuesUsed')}
            </span>
            <span className={`text-sm font-semibold ${getStatusColor()}`}>
              {currentCount} / {maxAllowed === -1 ? '∞' : maxAllowed}
            </span>
          </div>

          {maxAllowed !== -1 && (
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${getProgressColor()}`}
                style={{ width: `${Math.min(getProgressPercentage(), 100)}%` }}
              />
            </div>
          )}
        </div>

        {/* Subscription Tier */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {lt('currentPlan')}
          </span>
          <span className="text-sm font-medium text-gray-900 dark:text-white capitalize">
            {tier.toLowerCase() === 'free' ? lt('free') : tier}
          </span>
        </div>

        {/* Features */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {lt('planFeatures')}
          </h4>
          <div className="space-y-1">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600 dark:text-gray-400">{lt('realTimeUpdates')}</span>
              <span className={safeFeatures.realTimeUpdates ? 'text-green-600' : 'text-gray-400'}>
                {safeFeatures.realTimeUpdates ? '✓' : '✗'}
              </span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600 dark:text-gray-400">{lt('advancedAnalytics')}</span>
              <span className={safeFeatures.advancedAnalytics ? 'text-green-600' : 'text-gray-400'}>
                {safeFeatures.advancedAnalytics ? '✓' : '✗'}
              </span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600 dark:text-gray-400">{lt('prioritySupport')}</span>
              <span className={safeFeatures.prioritySupport ? 'text-green-600' : 'text-gray-400'}>
                {safeFeatures.prioritySupport ? '✓' : '✗'}
              </span>
            </div>
          </div>
        </div>

        {/* Upgrade Notice */}
        {upgradeRequired && (
          <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertIcon className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-red-800 dark:text-red-200">
                  {lt('queueLimitReached')}
                </p>
                <p className="text-xs text-red-600 dark:text-red-300 mt-1">
                  {lt('upgradeToCreateMore')}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Success Message */}
        {canCreate && currentCount > 0 && (
          <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <div className="flex items-start space-x-2">
              <CheckCircleIcon className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-green-800 dark:text-green-200">
                  {lt('readyToCreate')}
                </p>
                <p className="text-xs text-green-600 dark:text-green-300 mt-1">
                  {maxAllowed === -1
                    ? lt('canCreateMore', { count: 0 }).replace('0', lt('unlimited'))
                    : lt('canCreateMore', { count: maxAllowed - currentCount })
                  }
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
