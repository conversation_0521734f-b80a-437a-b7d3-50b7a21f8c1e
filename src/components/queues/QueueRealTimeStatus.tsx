import React from 'react';
import { useProviderQueueUpdates } from '../../hooks/useQueueWebSocket';
import { Queue } from '../../types/queue';
import { CheckCircleIcon, AlertIcon, TimeIcon, UserIcon } from '../../icons';

interface QueueRealTimeStatusProps {
  queues: Queue[];
  className?: string;
}

export default function QueueRealTimeStatus({ queues, className = '' }: QueueRealTimeStatusProps) {
  const queueIds = queues.map(q => q.id);
  const { queueStates, isConnected, refreshAllQueues } = useProviderQueueUpdates(queueIds);

  const getConnectionStatus = () => {
    if (isConnected) {
      return (
        <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium">Live Updates</span>
        </div>
      );
    }
    return (
      <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
        <span className="text-sm font-medium">Disconnected</span>
      </div>
    );
  };

  const getQueueStatus = (queue: Queue) => {
    const state = queueStates[queue.id];

    if (!state) {
      return {
        status: 'unknown',
        message: 'Status unknown',
        color: 'text-gray-500',
        icon: <AlertIcon className="w-4 h-4" />
      };
    }

    if (state.error) {
      return {
        status: 'error',
        message: state.error,
        color: 'text-red-600 dark:text-red-400',
        icon: <AlertIcon className="w-4 h-4" />
      };
    }

    if (state.position !== null && state.position !== undefined && state.position > 0) {
      return {
        status: 'active',
        message: `${state.position} customers waiting`,
        color: 'text-blue-600 dark:text-blue-400',
        icon: <UserIcon className="w-4 h-4" />
      };
    }

    return {
      status: 'idle',
      message: 'No customers waiting',
      color: 'text-green-600 dark:text-green-400',
      icon: <CheckCircleIcon className="w-4 h-4" />
    };
  };

  const formatWaitTime = (minutes: number | null) => {
    if (!minutes || minutes <= 0) return 'No wait';
    
    if (minutes < 60) {
      return `${minutes}m`;
    }
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Real-time Queue Status
        </h3>
        <div className="flex items-center space-x-4">
          {getConnectionStatus()}
          <button
            onClick={refreshAllQueues}
            disabled={!isConnected}
            className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Refresh
          </button>
        </div>
      </div>

      {queues.length === 0 ? (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <UserIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No queues available</p>
        </div>
      ) : (
        <div className="space-y-4">
          {queues.map((queue) => {
            const status = getQueueStatus(queue);
            const state = queueStates[queue.id];

            return (
              <div
                key={queue.id}
                className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div className={status.color}>
                    {status.icon}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {queue.title}
                    </h4>
                    <p className={`text-sm ${status.color}`}>
                      {status.message}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-6 text-sm">
                  {/* Wait Time */}
                  {state?.estimatedWaitMinutes !== null && state?.estimatedWaitMinutes !== undefined && (
                    <div className="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
                      <TimeIcon className="w-4 h-4" />
                      <span>{formatWaitTime(state.estimatedWaitMinutes)}</span>
                    </div>
                  )}

                  {/* Queue Position */}
                  {state?.position !== null && state?.position !== undefined && state.position > 0 && (
                    <div className="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
                      <UserIcon className="w-4 h-4" />
                      <span>{state.position}</span>
                    </div>
                  )}

                  {/* Active Status */}
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    queue.isActive 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                  }`}>
                    {queue.isActive ? 'Active' : 'Inactive'}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Connection Status Footer */}
      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>
            {isConnected ? 'Connected to real-time updates' : 'Attempting to reconnect...'}
          </span>
          <span>
            Last updated: {new Date().toLocaleTimeString()}
          </span>
        </div>
      </div>
    </div>
  );
}

/**
 * Compact version for dashboard widgets
 */
export function QueueRealTimeWidget({ queues, className = '' }: QueueRealTimeStatusProps) {
  const queueIds = queues.map(q => q.id);
  const { queueStates, isConnected } = useProviderQueueUpdates(queueIds);

  const totalWaiting = Object.values(queueStates).reduce((sum, state) => {
    return sum + (state?.position || 0);
  }, 0);

  const averageWaitTime = Object.values(queueStates).reduce((sum, state, _, arr) => {
    return sum + (state?.estimatedWaitMinutes || 0) / arr.length;
  }, 0);

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h4 className="font-medium text-gray-900 dark:text-white">Live Queue Status</h4>
        <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {totalWaiting}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Total Waiting
          </div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
            {Math.round(averageWaitTime)}m
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Avg. Wait Time
          </div>
        </div>
      </div>
    </div>
  );
}
