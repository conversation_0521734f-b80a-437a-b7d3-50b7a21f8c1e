import React, { useState } from 'react';
import Button from '../ui/button/Button';
import { Queue } from '../../types/queue';
import { useQueueStatus } from '../../hooks/useQueues';
import QueueCapacityCalculator from './QueueCapacityCalculator';
import QueueRealTimeStatus from './QueueRealTimeStatus';

interface QueueDetailsProps {
  queue: Queue;
  onClose: () => void;
  onEdit: () => void;
}

export default function QueueDetails({ queue, onClose, onEdit }: QueueDetailsProps) {
  const { data: queueStatus } = useQueueStatus(queue.id, queue.isActive);
  const [activeTab, setActiveTab] = useState<'overview' | 'capacity' | 'realtime'>('overview');

  const formatTime = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} minutes`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive 
      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Queue Details
        </h2>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview' },
            { id: 'capacity', label: 'Capacity' },
            { id: 'realtime', label: 'Real-time' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      <div className="space-y-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <>
            {/* Basic Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Basic Information
          </h3>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Name:</span>
              <span className="text-sm text-gray-900 dark:text-white">{queue.title}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Status:</span>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(queue.isActive)}`}>
                {queue.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Max Capacity:</span>
              <span className="text-sm text-gray-900 dark:text-white">
                {queue.maxCapacity || 'Unlimited'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Created:</span>
              <span className="text-sm text-gray-900 dark:text-white">
                {new Date(queue.createdAt).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        {/* Current Status */}
        {queueStatus && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Current Status
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {queueStatus.currentCapacity}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Current Customers
                </div>
              </div>
              <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  {formatTime(queueStatus.estimatedWaitTime)}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Estimated Wait
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Current Customers */}
        {queueStatus?.currentCustomers && queueStatus.currentCustomers.length > 0 && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Current Customers ({queueStatus.currentCustomers.length})
            </h3>
            <div className="space-y-3 max-h-60 overflow-y-auto">
              {queueStatus.currentCustomers.map((customer, index) => (
                <div 
                  key={customer.customerId}
                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                      {customer.position}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {customer.customerName}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {customer.serviceRequested || 'General Service'}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatTime(customer.estimatedWaitTime)}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      wait time
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Services */}
        {queue.services && queue.services.length > 0 && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Associated Services ({queue.services.length})
            </h3>
            <div className="grid grid-cols-1 gap-3">
              {queue.services.map((service, index) => (
                <div 
                  key={index}
                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      Service {index + 1}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Estimated Duration: {service.estimatedDuration || 30} minutes
                    </div>
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Priority: {service.priority || 1}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
          </>
        )}

        {/* Capacity Tab */}
        {activeTab === 'capacity' && (
          <QueueCapacityCalculator
            queueId={queue.id}
            queueTitle={queue.title}
          />
        )}

        {/* Real-time Tab */}
        {activeTab === 'realtime' && (
          <QueueRealTimeStatus queues={[queue]} />
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            variant="outline"
            onClick={onClose}
          >
            Close
          </Button>
          <Button onClick={onEdit}>
            Edit Queue
          </Button>
        </div>
      </div>
    </div>
  );
}
