import React from 'react';
import { useNavigate } from 'react-router';
import { useSubscriptionStatus, useCustomerPortal } from '../../hooks/useSubscription';
import { useUsedCredits } from '../../hooks/useUsedCredits';
import Button from '../ui/button/Button';
import { useModal } from '../../hooks/useModal';
import CheckoutModal from './CheckoutModal';
import { useDashboardTranslation, useCommonTranslation } from '../../hooks/useTranslation';

const SubscriptionStatusWidget: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useDashboardTranslation();
  const { t: tCommon } = useCommonTranslation();
  const { data: statusData, isLoading: statusLoading } = useSubscriptionStatus();
  const { isOpen: isCheckoutOpen, openModal: openCheckout, closeModal: closeCheckout } = useModal();
  const {
    used,
    remaining,
    allocated,
    totalAvailable,
    usagePercentage,
    totalQueues,
    usedQueues,
    remainingQueues,
    isLoading: creditsLoading
  } = useUsedCredits();
  const customerPortalMutation = useCustomerPortal();

  const handleManageSubscription = () => {
    if (statusData?.data?.hasCustomerPortal) {
      customerPortalMutation.mutate();
    } else {
      navigate('/subscription');
    }
  };

  const handleUpgrade = () => {
    openCheckout();
  };



  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'past_due':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'cancel_at_period_end':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  const getStatusText = (status?: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'past_due':
        return 'Payment Due';
      case 'cancel_at_period_end':
        return 'Cancelling';
      case 'deleted':
        return 'Cancelled';
      default:
        return 'Inactive';
    }
  };

  const getUsagePercentage = (current: number, limit: number) => {
    if (limit === 0) return 0;
    return Math.min(100, (current / limit) * 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  if (statusLoading || creditsLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 animate-pulse">
        <div className="space-y-4">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  const subscription = statusData?.data?.subscription;
  const user = statusData?.data?.user;

  // Use data from useUsedCredits hook instead of usage statistics
  const creditsPercentage = usagePercentage || 0;
  const queuesPercentage = totalQueues > 0 ? Math.round((usedQueues / totalQueues) * 100) : 0;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {t('subscription.title', 'Subscription')}
        </h3>
        <div className="flex items-center space-x-2">
          <svg className="w-5 h-5 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
        </div>
      </div>

      {/* Current Plan */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
            {t('subscription.currentPlan', 'Current Plan')}
          </span>
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(subscription?.status)}`}>
            {getStatusText(subscription?.status)}
          </span>
        </div>
        <p className="text-xl font-bold text-gray-900 dark:text-white">
          {subscription?.planName || t('subscription.noPlan', 'No Plan')}
        </p>
      </div>

      {/* Usage Statistics */}
      {(used !== undefined && totalAvailable !== undefined) && (
        <div className="space-y-4 mb-6">
          {/* Credits Usage */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('subscription.creditsAvailable', 'Credits Available')}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {totalAvailable}
              </span>
            </div>
            {/* <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${getUsageColor(creditsPercentage)}`}
                style={{ width: `${creditsPercentage}%` }}
              ></div>
            </div> */}
          </div>

          {/* Queues Usage */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {tCommon('navigation.queues')}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {usedQueues} / {totalQueues}
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${getUsageColor(queuesPercentage)}`}
                style={{ width: `${queuesPercentage}%` }}
              ></div>
            </div>
          </div>
        </div>
      )}

      {/* Usage Warnings */}
      {(creditsPercentage >= 80 || queuesPercentage >= 80) && (
        <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <div className="flex items-start space-x-2">
            <svg className="w-4 h-4 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div>
              <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Usage Warning
              </p>
              <p className="text-xs text-yellow-700 dark:text-yellow-300">
                You're approaching your plan limits. Consider upgrading.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-2 flex flex-col sm:flex-row sm:space-y-0 sm:space-x-2">
        {subscription?.planId === 'free' ? (
          <Button
            onClick={handleUpgrade}
            variant="primary"
            size="sm"
            className="w-full"
          >
            {t('subscription.upgradePlan', 'Upgrade Plan')}
          </Button>
        ) : (
          <Button
            onClick={handleManageSubscription}
            variant="outline"
            size="sm"
            className="w-full"
            disabled={customerPortalMutation.isPending}
          >
            {customerPortalMutation.isPending ? tCommon('actions.loading') : t('subscription.manageSubscription', 'Manage Subscription')}
          </Button>
        )}
        
        <Button
          onClick={() => navigate('/subscription')}
          variant="outline"
          size="sm"
          className="w-full"
        >
          {t('subscription.viewAllPlans', 'View All Plans')}
        </Button>
      </div>

      {/* Last Updated */}
      {user?.datePaid && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {t('subscription.lastPayment', 'Last payment')}: {new Date(user.datePaid).toLocaleDateString()}
          </p>
        </div>
      )}

      {/* Checkout Modal */}
      <CheckoutModal
        isOpen={isCheckoutOpen}
        onClose={closeCheckout}
        title={t('subscription.upgradePlan', 'Upgrade Your Plan')}
        showPlanComparison={true}
      />
    </div>
  );
};

export default SubscriptionStatusWidget;
