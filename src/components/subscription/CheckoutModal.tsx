import React, { useState, useEffect } from 'react';
import { Modal } from '../ui/modal';
import Button from '../ui/button/Button';
import { useSubscriptionPlans, useSubscriptionStatus, useCreateCheckoutSession } from '../../hooks/useSubscription';
import SubscriptionPlanCard from './SubscriptionPlanCard';
import PaymentMethodSelector from './PaymentMethodSelector';
import { useUserLocation } from '../../hooks/useUserLocation';
import { useRecommendedPaymentMethod } from '../../hooks/usePaymentMethods';
import { SubscriptionPlan, PaymentProcessor, PaymentMethodType } from '../../types';
import { ErrorDisplay } from '../error';
import { useDashboardTranslation, useManagementTranslation } from '../../hooks/useTranslation';

interface CheckoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  preselectedPlanId?: string;
  title?: string;
  showPlanComparison?: boolean;
}

export default function CheckoutModal({
  isOpen,
  onClose,
  preselectedPlanId,
  title = 'subscription.choosePlan',
  showPlanComparison = true,
}: CheckoutModalProps) {
  const [selectedPlanId, setSelectedPlanId] = useState<string>(preselectedPlanId || '');
  const [step, setStep] = useState<'selection' | 'confirmation'>('selection');
  const [selectedPaymentProcessor, setSelectedPaymentProcessor] = useState<PaymentProcessor | ''>('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethodType | ''>('');
  const { t, currentLanguage } = useManagementTranslation();

  // Custom translations for checkout modal
  const checkoutTranslations = {
    ar: {
      selectPlanDescription: "اختر الخطة التي تناسب احتياجات عملك بشكل أفضل"
    },
    en: {
      selectPlanDescription: "Select the plan that best fits your business needs"
    },
    fr: {
      selectPlanDescription: "Sélectionnez le plan qui correspond le mieux aux besoins de votre entreprise"
    }
  };

  const currentLang = currentLanguage as keyof typeof checkoutTranslations;
  const ct = (key: keyof typeof checkoutTranslations.ar) =>
    checkoutTranslations[currentLang]?.[key] || checkoutTranslations.en[key] || key;

  const { data: plansData, isLoading: plansLoading, error: plansError } = useSubscriptionPlans();
  const { data: statusData } = useSubscriptionStatus();
  const createCheckoutMutation = useCreateCheckoutSession();

  // Get user location and payment method recommendations for metadata
  const { location } = useUserLocation();
  const { recommendedMethod, isAlgeria } = useRecommendedPaymentMethod();

  const { t:tt } = useDashboardTranslation();

  // Function to translate plan names
  const translatePlanName = (planName: string): string => {
    const normalizedName = planName.toLowerCase();
    switch (normalizedName) {
      case 'free':
        return tt('subscription.free');
      case 'starter':
        return tt('subscription.starter');
      case 'business':
        return tt('subscription.business');
      case 'enterprise':
        return tt('subscription.enterprise');
      default:
        return planName; // Return original name if no translation found
    }
  };

  // Auto-proceed to confirmation step if plan is preselected and no comparison needed
  useEffect(() => {
    if (preselectedPlanId && !showPlanComparison && isOpen) {
      console.log('💳 Auto-proceeding to confirmation for preselected plan:', preselectedPlanId);
      setSelectedPlanId(preselectedPlanId);
      setStep('confirmation');
    }
  }, [preselectedPlanId, showPlanComparison, isOpen]);

  const handlePlanSelect = (planId: string) => {
    setSelectedPlanId(planId);
    if (!showPlanComparison) {
      // Skip confirmation step if not showing comparison
      handleProceedToCheckout(planId);
    } else {
      setStep('confirmation');
    }
  };

  const handleProceedToCheckout = (planId?: string) => {
    const targetPlanId = planId || selectedPlanId;
    if (!targetPlanId) return;

    // Validate payment method selection
    if (!selectedPaymentProcessor || !selectedPaymentMethod) {
      console.warn('Payment method not selected');
      return;
    }

    // Prepare metadata for Chargily checkout session
    const metadata = {
      // User location information
      userLocation: location ? {
        country: location.country,
        countryCode: location.countryCode,
        isAlgeria: location.isAlgeria,
        detectionMethod: location.detectionMethod,
        confidence: location.confidence,
      } : null,

      // Payment method selection context
      paymentMethodContext: {
        recommendedMethod: recommendedMethod?.id,
        isAlgerianUser: isAlgeria,
        wasRecommended: recommendedMethod?.id === selectedPaymentProcessor,
        selectionTimestamp: new Date().toISOString(),
      },

      // UI context
      uiContext: {
        modalType: 'checkout',
        preselectedPlan: !!preselectedPlanId,
        showPlanComparison,
        source: 'subscription_management',
      },

      // Session information
      sessionInfo: {
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      },
    };

    createCheckoutMutation.mutate(
      {
        planId: targetPlanId,
        paymentProcessor: selectedPaymentProcessor,
        paymentMethod: selectedPaymentMethod,
        metadata,
      },
      {
        onSuccess: () => {
          // The mutation automatically redirects to checkout
          onClose();
        },
      }
    );
  };

  const handleBack = () => {
    setStep('selection');
  };

  const handleModalClose = () => {
    setStep('selection');
    setSelectedPlanId(preselectedPlanId || '');
    setSelectedPaymentProcessor('');
    setSelectedPaymentMethod('');
    onClose();
  };

  const handlePaymentMethodChange = (processor: PaymentProcessor, method: PaymentMethodType) => {
    setSelectedPaymentProcessor(processor);
    setSelectedPaymentMethod(method);
  };

  const getCurrentPlanId = () => {
    return (statusData as any)?.data?.subscription?.planId;
  };

  const getSelectedPlan = (): SubscriptionPlan | null => {
    if (!(plansData as any)?.data?.plans || !selectedPlanId) return null;
    return (plansData as any).data.plans.find((plan: any) => plan.id === selectedPlanId) || null;
  };

  const sortPlans = (plans: SubscriptionPlan[]) => {
    return [...plans].sort((a, b) => {
      if (a.id === 'free') return -1;
      if (b.id === 'free') return 1;
      if (a.isSubscription && !b.isSubscription) return -1;
      if (!a.isSubscription && b.isSubscription) return 1;
      if (a.isSubscription && b.isSubscription) {
        return a.effect.amount - b.effect.amount;
      }
      return a.effect.amount - b.effect.amount;
    });
  };

  const renderSelectionStep = () => {
    if (plansLoading) {
      return (
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="bg-gray-200 dark:bg-gray-700 rounded-2xl h-64"></div>
              </div>
            ))}
          </div>
        </div>
      );
    }

    if (plansError) {
      return (
        <div className="p-6">
          <ErrorDisplay 
            error={plansError} 
            title="Failed to load plans"
            message="Please try again or contact support."
          />
        </div>
      );
    }

    if (!plansData?.data?.plans || plansData.data.plans.length === 0) {
      return (
        <div className="p-6 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No plans available
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            Please check back later or contact support.
          </p>
        </div>
      );
    }

    const currentPlanId = getCurrentPlanId();
    const sortedPlans = sortPlans(plansData.data.plans);

    return (
      <div className="p-6">
        <div className="text-center mb-6">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            {tt(title)}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            {ct('selectPlanDescription')}
          </p>
        </div>

        <div className="p-8 grid grid-cols-1 md:grid-cols-1 gap-4  overflow-y-auto">
          {sortedPlans.map((plan) => (
            <SubscriptionPlanCard
              type="horizontal"
              key={plan.id}
              plan={plan}
              isCurrentPlan={plan.id === currentPlanId}
              isRecommended={plan.id === 'pro'}
              isLoading={createCheckoutMutation.isPending}
              onSubscribe={handlePlanSelect}
              onUpgrade={handlePlanSelect}
              className="cursor-pointer hover:scale-105 transition-transform"
            />
          ))}
        </div>
      </div>
    );
  };

  const renderConfirmationStep = () => {
    const selectedPlan = getSelectedPlan();
    if (!selectedPlan) return null;

    return (
      <div className="p-6">
        <div className="text-center mb-6">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            {tt('subscription.confirmSelection')}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            {tt('subscription.reviewPlanSelection')}
          </p>
        </div>

        {/* Selected Plan Summary */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 mb-6 border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                {translatePlanName(selectedPlan.name)}
              </h3>
              <p className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                {selectedPlan.isSubscription ? tt('subscription.monthlySubscription') : tt('subscription.onetimePurchase')}
              </p>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-gray-900 dark:text-white">
                {selectedPlan.price}
              </div>
              {selectedPlan.isSubscription && selectedPlan.id !== 'free' && (
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {tt('subscription.perMonth')}
                </div>
              )}
            </div>
          </div>

          <p className="text-gray-700 dark:text-gray-300 mb-4">
            {selectedPlan.description}
          </p>

          {/* Plan Features */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="font-semibold text-gray-900 dark:text-white">{tt('subscription.credits')}</span>
              </div>
              <p className="text-gray-600 dark:text-gray-400">
                {selectedPlan.effect.kind === 'credits'
                  ? `${selectedPlan.effect.amount} Credits`
                  : `${selectedPlan.effect.amount} ${tt('subscription.perMonth')}`
                }
              </p>
            </div>

            {selectedPlan.effect.queues !== null && (
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="font-semibold text-gray-900 dark:text-white">{tt('subscription.queues')}</span>
                </div>
                <p className="text-gray-600 dark:text-gray-400">
                  {selectedPlan.effect.queues === -1 ? tt('subscription.unlimited') : selectedPlan.effect.queues} {selectedPlan.effect.queues === 1 ? tt('subscription.queue') : tt('subscription.queues')}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Payment Methods */}
        <div className="mb-6">
          <PaymentMethodSelector
            selectedProcessor={selectedPaymentProcessor}
            selectedMethod={selectedPaymentMethod}
            onSelectionChange={handlePaymentMethodChange}
            disabled={createCheckoutMutation.isPending}
            enableAutoSelection={true}
            showAutoSelectionInfo={true}
          />

          {/* Payment Method Validation */}
          {(!selectedPaymentProcessor || !selectedPaymentMethod) && (
            <div className="mt-3 p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800">
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-amber-600 dark:text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <p className="text-sm text-amber-800 dark:text-amber-200">
                  {tt('subscription.selectPaymentMethod')}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Features List */}
        {/* <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            {tt('subscription.whatsIncluded')}
          </h4>
          <ul className="space-y-2">
            {selectedPlan.features.map((feature, index) => (
              <li key={index} className="flex items-start space-x-2">
                <svg className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {feature}
                </span>
              </li>
            ))}
          </ul>
        </div> */}

        {/* Billing Summary */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-600 dark:text-gray-400">{tt('subscription.subtotal')}</span>
            <span className="text-gray-900 dark:text-white font-medium">{selectedPlan.price}</span>
          </div>
          {selectedPlan.isSubscription && selectedPlan.id !== 'free' && (
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-600 dark:text-gray-400">{tt('subscription.billingCycle')}</span>
              <span className="text-gray-900 dark:text-white font-medium">{tt('subscription.monthly')}</span>
            </div>
          )}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-gray-900 dark:text-white">{tt('subscription.total')}</span>
              <span className="text-lg font-bold text-gray-900 dark:text-white">{selectedPlan.price}</span>
            </div>
            {selectedPlan.isSubscription && selectedPlan.id !== 'free' && (
              <p className="text-sm text-gray-500 dark:text-gray-400 text-right">
                {tt('subscription.billedMonthly')}
              </p>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button
            onClick={() => handleProceedToCheckout()}
            variant="primary"
            size="lg"
            className="w-full text-white font-semibold py-3"
            disabled={createCheckoutMutation.isPending || !selectedPaymentProcessor || !selectedPaymentMethod}
          >
            {createCheckoutMutation.isPending ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {tt('subscription.processing')}
              </div>
            ) : (
              <div className="flex items-center justify-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                {tt('subscription.completeSecureCheckout')}
              </div>
            )}
          </Button>

          <Button
            onClick={handleBack}
            variant="outline"
            size="md"
            className="w-full"
            disabled={createCheckoutMutation.isPending}
          >
            {tt('subscription.backToPlans')}
          </Button>
        </div>

        {/* Security Notice */}
        <div className="mt-6 text-center">
          <div className="flex items-center justify-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
              </svg>
              SSL Secured
            </div>
          </div>
          <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">
            Your payment information is encrypted and secure
          </p>
        </div>
      </div>
    );
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleModalClose}
      className="max-w-4xl p-0"
      showCloseButton={true}
    >
      <div className="bg-white dark:bg-gray-800 rounded-3xl">
        {step === 'selection' ? renderSelectionStep() : renderConfirmationStep()}
      </div>
    </Modal>
  );
}
