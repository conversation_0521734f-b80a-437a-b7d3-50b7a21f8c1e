import React, { useState, useEffect } from 'react';
import { useUsageStatistics, useSubscriptionStatus } from '../../hooks/useSubscription';
import { UsagePeriod } from '../../types';

interface UsageStatisticsProps {
  className?: string;
  showPeriodSelector?: boolean;
  defaultPeriod?: UsagePeriod;
}

export default function UsageStatistics({
  className = '',
  showPeriodSelector = true,
  defaultPeriod = 'month',
}: UsageStatisticsProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<UsagePeriod>(defaultPeriod);
  const { data: usageData, isLoading: usageLoading, error: usageError } = useUsageStatistics({ period: selectedPeriod });
  const { data: statusData } = useSubscriptionStatus();

  // Debug logging to help identify the issue
  useEffect(() => {
    console.log('🔍 UsageStatistics Debug:', {
      usageData,
      usageLoading,
      usageError,
      selectedPeriod,
    });
  }, [usageData, usageLoading, usageError, selectedPeriod]);

  const getUsagePercentage = (current: number, limit: number) => {
    if (limit === 0) return 0;
    return Math.min(100, (current / limit) * 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    if (percentage >= 50) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const getUsageColorLight = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-100 dark:bg-red-900/20';
    if (percentage >= 75) return 'bg-yellow-100 dark:bg-yellow-900/20';
    if (percentage >= 50) return 'bg-blue-100 dark:bg-blue-900/20';
    return 'bg-green-100 dark:bg-green-900/20';
  };

  const getUsageTextColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-700 dark:text-red-300';
    if (percentage >= 75) return 'text-yellow-700 dark:text-yellow-300';
    if (percentage >= 50) return 'text-blue-700 dark:text-blue-300';
    return 'text-green-700 dark:text-green-300';
  };

  const formatPeriodLabel = (period: UsagePeriod) => {
    switch (period) {
      case 'week':
        return 'This Week';
      case 'month':
        return 'This Month';
      case 'year':
        return 'This Year';
      default:
        return 'Current Period';
    }
  };

  const getUsageRecommendation = (creditsPercentage: number, queuesPercentage: number) => {
    if (creditsPercentage >= 90 || queuesPercentage >= 90) {
      return {
        type: 'error',
        message: 'You\'re at your usage limits. Consider upgrading your plan.',
        action: 'Upgrade Now'
      };
    }
    if (creditsPercentage >= 75 || queuesPercentage >= 75) {
      return {
        type: 'warning',
        message: 'You\'re approaching your usage limits.',
        action: 'View Plans'
      };
    }
    if (creditsPercentage < 25 && queuesPercentage < 25) {
      return {
        type: 'info',
        message: 'You have plenty of capacity remaining.',
        action: null
      };
    }
    return null;
  };

  if (usageLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if there's an error
  if (usageError) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="text-center py-8">
          <div className="text-red-500 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <p className="text-gray-900 dark:text-white font-medium mb-2">
            Failed to load usage statistics
          </p>
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            {(usageError as any)?.message || 'Unknown error occurred'}
          </p>
          <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">
            Check the console for more details
          </p>
        </div>
      </div>
    );
  }

  if (!usageData?.data) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="text-center py-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No Usage Data
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            Usage statistics will appear here once you start using the service.
          </p>
        </div>
      </div>
    );
  }

  // Check if usage data exists before accessing it
  if (!usageData?.data) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">
            No usage data available
          </p>
        </div>
      </div>
    );
  }

  const usage = usageData.data;

  // Additional safety checks for usage data structure
  if (!usage?.current || !usage?.limits) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">
            Invalid usage data format
          </p>
        </div>
      </div>
    );
  }

  const creditsPercentage = getUsagePercentage(
    usage.current?.credits || 0,
    usage.limits?.credits || 0
  );
  const queuesPercentage = getUsagePercentage(
    usage.current?.queues || 0,
    usage.limits?.queues || 0
  );
  const recommendation = getUsageRecommendation(creditsPercentage, queuesPercentage);

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Usage Statistics
        </h3>
        
        {showPeriodSelector && (
          <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            {(['week', 'month', 'year'] as UsagePeriod[]).map((period) => (
              <button
                key={period}
                onClick={() => setSelectedPeriod(period)}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                  selectedPeriod === period
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Period Label */}
      <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
        {formatPeriodLabel(selectedPeriod)}
      </p>

      {/* Usage Metrics */}
      <div className="space-y-6">
        {/* Credits Usage */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Credits Used
              </span>
            </div>
            <div className="text-right">
              <p className="text-lg font-bold text-gray-900 dark:text-white">
                {usage.current?.credits || 0}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                of {usage.limits?.credits || 0}
              </p>
            </div>
          </div>
          
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2">
            <div
              className={`h-3 rounded-full transition-all duration-500 ${getUsageColor(creditsPercentage)}`}
              style={{ width: `${creditsPercentage}%` }}
            ></div>
          </div>
          
          <div className="flex items-center justify-between text-xs">
            <span className={`font-medium ${getUsageTextColor(creditsPercentage)}`}>
              {creditsPercentage.toFixed(1)}% used
            </span>
            <span className="text-gray-500 dark:text-gray-400">
              {(usage.limits?.credits || 0) - (usage.current?.credits || 0)} remaining
            </span>
          </div>
        </div>

        {/* Queues Usage */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Queues Active
              </span>
            </div>
            <div className="text-right">
              <p className="text-lg font-bold text-gray-900 dark:text-white">
                {usage.current?.queues || 0}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                of {usage.limits?.queues || 0}
              </p>
            </div>
          </div>
          
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2">
            <div
              className={`h-3 rounded-full transition-all duration-500 ${getUsageColor(queuesPercentage)}`}
              style={{ width: `${queuesPercentage}%` }}
            ></div>
          </div>
          
          <div className="flex items-center justify-between text-xs">
            <span className={`font-medium ${getUsageTextColor(queuesPercentage)}`}>
              {queuesPercentage.toFixed(1)}% used
            </span>
            <span className="text-gray-500 dark:text-gray-400">
              {(usage.limits?.queues || 0) - (usage.current?.queues || 0)} available
            </span>
          </div>
        </div>
      </div>

      {/* Recommendation */}
      {recommendation && (
        <div className={`mt-6 p-4 rounded-lg ${getUsageColorLight(Math.max(creditsPercentage, queuesPercentage))}`}>
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 mt-0.5">
              {recommendation.type === 'error' ? (
                <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              ) : recommendation.type === 'warning' ? (
                <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              ) : (
                <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
            </div>
            <div className="flex-1">
              <p className={`text-sm font-medium ${getUsageTextColor(Math.max(creditsPercentage, queuesPercentage))}`}>
                {recommendation.message}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Current Plan Info */}
      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500 dark:text-gray-400">Current Plan:</span>
          <span className="font-medium text-gray-900 dark:text-white">
            {statusData?.data?.subscription?.planName || 'No Plan'}
          </span>
        </div>
      </div>
    </div>
  );
}
