import React, { useState } from 'react';
import { PaymentMethod, PaymentMethodType } from '../../types';

interface ChargilyPaymentCardProps {
  method: PaymentMethod;
  isSelected: boolean;
  selectedSubMethod: PaymentMethodType | '';
  onSelect: (method: PaymentMethodType) => void;
  onSubMethodChange?: (subMethod: PaymentMethodType) => void;
  disabled?: boolean;
  isRecommended?: boolean;
  showDetails?: boolean;
  className?: string;
}

export const ChargilyPaymentCard: React.FC<ChargilyPaymentCardProps> = ({
  method,
  isSelected,
  selectedSubMethod,
  onSelect,
  onSubMethodChange,
  disabled = false,
  isRecommended = false,
  showDetails = true,
  className = '',
}) => {
  const [expandedDetails, setExpandedDetails] = useState(false);

  const handleMainSelection = () => {
    if (disabled) return;
    const defaultSubMethod = method.supportedMethods[0] as PaymentMethodType;
    onSelect(defaultSubMethod);
  };

  const handleSubMethodSelection = (subMethod: PaymentMethodType) => {
    if (disabled) return;
    onSelect(subMethod);
    onSubMethodChange?.(subMethod);
  };

  return (
    <div className={`${className}`}>
      {/* Main Card */}
      <div 
        className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
          disabled 
            ? 'opacity-50 cursor-not-allowed' 
            : isSelected 
              ? 'border-green-500 bg-green-50 dark:bg-green-900/20 shadow-md ring-1 ring-green-500/20' 
              : 'border-gray-200 dark:border-gray-700 hover:border-green-300 dark:hover:border-green-600 hover:shadow-sm'
        }`}
        onClick={handleMainSelection}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <input
              type="radio"
              checked={isSelected}
              onChange={handleMainSelection}
              disabled={disabled}
              className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 focus:ring-green-500 dark:focus:ring-green-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
            
            <div className="flex items-center space-x-3">
              {/* Chargily Logo */}
              <ChargilyLogo />
              
              <div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-gray-900 dark:text-white">
                    {method.displayName}
                  </span>
                  {isRecommended && (
                    <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded-full dark:bg-green-900/20 dark:text-green-400">
                      موصى به
                    </span>
                  )}
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full dark:bg-blue-900/20 dark:text-blue-400">
                    Algeria
                  </span>
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {method.description}
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-green-700 dark:text-green-400">
              {method.currency}
            </span>
            {showDetails && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setExpandedDetails(!expandedDetails);
                }}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <svg 
                  className={`w-4 h-4 transition-transform ${expandedDetails ? 'rotate-180' : ''}`} 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            )}
          </div>
        </div>
        
        {/* Payment Method Options */}
        <div className="mt-4 space-y-2">
          <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
            Choose your payment method:
          </p>
          <div className="grid grid-cols-2 gap-2">
            {method.supportedMethods.map((subMethod) => (
              <PaymentSubMethodCard
                key={subMethod}
                method={subMethod as PaymentMethodType}
                isSelected={selectedSubMethod === subMethod}
                onSelect={() => handleSubMethodSelection(subMethod as PaymentMethodType)}
                disabled={disabled}
              />
            ))}
          </div>
        </div>
        
        {/* Features */}
        <div className="mt-3">
          <div className="flex flex-wrap gap-1">
            {method.features.slice(0, 3).map((feature, index) => (
              <span 
                key={index}
                className="text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded"
              >
                {feature}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* Expanded Details */}
      {expandedDetails && showDetails && (
        <div className="mt-2 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <ChargilyPaymentDetails method={method} />
        </div>
      )}
    </div>
  );
};

const ChargilyLogo: React.FC = () => (
  <div className="relative">
    <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center shadow-sm">
      <span className="text-white font-bold text-lg">C</span>
    </div>
    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center">
      <span className="text-xs font-bold text-green-800">DZ</span>
    </div>
  </div>
);

interface PaymentSubMethodCardProps {
  method: PaymentMethodType;
  isSelected: boolean;
  onSelect: () => void;
  disabled?: boolean;
}

const PaymentSubMethodCard: React.FC<PaymentSubMethodCardProps> = ({
  method,
  isSelected,
  onSelect,
  disabled = false,
}) => {
  const getMethodInfo = (method: PaymentMethodType) => {
    switch (method) {
      case 'edahabia':
        return {
          name: 'EDAHABIA',
          nameAr: 'الذهبية',
          description: 'Algerie Post',
          color: 'yellow',
          icon: '💳',
        };
      case 'cib':
        return {
          name: 'CIB',
          nameAr: 'سيب',
          description: 'SATIM',
          color: 'blue',
          icon: '🏦',
        };
      default:
        return {
          name: method.toUpperCase(),
          nameAr: '',
          description: '',
          color: 'gray',
          icon: '💳',
        };
    }
  };

  const methodInfo = getMethodInfo(method);
  
  const getColorClasses = (color: string, isSelected: boolean) => {
    const baseClasses = 'border rounded-lg p-3 cursor-pointer transition-all duration-200';
    
    if (disabled) {
      return `${baseClasses} opacity-50 cursor-not-allowed border-gray-200 dark:border-gray-700`;
    }
    
    if (isSelected) {
      switch (color) {
        case 'yellow':
          return `${baseClasses} border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20 ring-1 ring-yellow-500/20`;
        case 'blue':
          return `${baseClasses} border-blue-500 bg-blue-50 dark:bg-blue-900/20 ring-1 ring-blue-500/20`;
        default:
          return `${baseClasses} border-gray-500 bg-gray-50 dark:bg-gray-900/20`;
      }
    }
    
    return `${baseClasses} border-gray-200 dark:border-gray-700 hover:border-${color}-300 dark:hover:border-${color}-600`;
  };

  return (
    <div 
      className={getColorClasses(methodInfo.color, isSelected)}
      onClick={disabled ? undefined : onSelect}
    >
      <div className="flex items-center space-x-2">
        <input
          type="radio"
          checked={isSelected}
          onChange={onSelect}
          disabled={disabled}
          className={`w-3 h-3 text-${methodInfo.color}-600 bg-gray-100 border-gray-300 focus:ring-${methodInfo.color}-500`}
        />
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-1">
            <span className="text-lg">{methodInfo.icon}</span>
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {methodInfo.name}
              </p>
              {methodInfo.nameAr && (
                <p className="text-xs text-gray-500 dark:text-gray-400" dir="rtl">
                  {methodInfo.nameAr}
                </p>
              )}
            </div>
          </div>
          {methodInfo.description && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {methodInfo.description}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

const ChargilyPaymentDetails: React.FC<{ method: PaymentMethod }> = ({ method }) => (
  <div className="space-y-4">
    <div>
      <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
        About Chargily Pay
      </h5>
      <p className="text-sm text-gray-600 dark:text-gray-400">
        Chargily Pay is Algeria's leading payment gateway, providing secure and convenient 
        payment solutions for Algerian businesses and customers.
      </p>
    </div>
    
    <div>
      <h6 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
        Supported Payment Methods
      </h6>
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <span className="text-lg">💳</span>
          <div>
            <p className="text-sm font-medium text-gray-900 dark:text-white">EDAHABIA (الذهبية)</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">Algerie Post electronic payment card</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-lg">🏦</span>
          <div>
            <p className="text-sm font-medium text-gray-900 dark:text-white">CIB (سيب)</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">SATIM interbank payment system</p>
          </div>
        </div>
      </div>
    </div>
    
    <div>
      <h6 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
        Benefits
      </h6>
      <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
        <li className="flex items-center space-x-2">
          <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          <span>Local Algerian support in Arabic and French</span>
        </li>
        <li className="flex items-center space-x-2">
          <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          <span>No international transaction fees</span>
        </li>
        <li className="flex items-center space-x-2">
          <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          <span>Instant payment processing</span>
        </li>
        <li className="flex items-center space-x-2">
          <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          <span>Secure encryption and fraud protection</span>
        </li>
      </ul>
    </div>
    
    <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
      <p className="text-xs text-gray-500 dark:text-gray-400">
        Currency: {method.currency} • Processing time: Instant • Support: 24/7
      </p>
    </div>
  </div>
);

export default ChargilyPaymentCard;
