import React, { useState, useEffect } from 'react';
import { PaymentMethod, PaymentProcessor, PaymentMethodType } from '../../types';
import { usePaymentMethods, useRecommendedPaymentMethod } from '../../hooks/usePaymentMethods';
import { useAutoPaymentSelection } from '../../hooks/useAutoPaymentSelection';
import { getPaymentMethodFeatures } from '../../utils/payment-method-selection.utils';
import { ErrorDisplay } from '../error';
import { useFeatureFlags } from '../../hooks/useFeatureFlags';
import { useTranslation } from '../../hooks/useTranslation';

interface PaymentMethodSelectorProps {
  selectedProcessor: PaymentProcessor | '';
  selectedMethod: PaymentMethodType | '';
  onSelectionChange: (processor: PaymentProcessor, method: PaymentMethodType) => void;
  className?: string;
  disabled?: boolean;
  enableAutoSelection?: boolean;
  showAutoSelectionInfo?: boolean;
}

export const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  selectedProcessor,
  selectedMethod,
  onSelectionChange,
  className = '',
  disabled = false,
  enableAutoSelection = true,
  showAutoSelectionInfo = false,
}) => {
  const { data: paymentMethodsData, isLoading, error } = usePaymentMethods();
  const { recommendedMethod, isAlgeria } = useRecommendedPaymentMethod();
  const [localSelectedProcessor, setLocalSelectedProcessor] = useState<PaymentProcessor | ''>(selectedProcessor);
  const { isEnabled } = useFeatureFlags();
  const { t } = useTranslation('dashboard');

  // Function to translate payment method descriptions
  const translatePaymentDescription = (description: string): string => {
    switch (description) {
      case 'Local Algerian payment gateway supporting EDAHABIA and CIB':
        return t('subscription.localAlgerianPayment');
      case 'International payment processor supporting cards and PayPal':
        return t('subscription.internationalPaymentProcessor');
      default:
        return description;
    }
  };

  // Use auto-selection hook (only if feature is enabled)
  const {
    selectedProcessor: autoSelectedProcessor,
    selectedMethod: autoSelectedMethod,
    isAutoSelected,
    confidence,
    reasoning,
    handleUserSelection,
  } = useAutoPaymentSelection({
    enableAutoSelection: enableAutoSelection && isEnabled('CHARGILY_AUTO_SELECTION'),
    respectUserPreference: true,
    fallbackToDefault: true,
  });

  // Filter methods based on feature flags (moved before early returns)
  const availableMethods = ((paymentMethodsData as any)?.data?.methods || []).filter((method: any) => {
    // Filter Chargily based on feature flag
    if (method.id === 'chargily' && !isEnabled('CHARGILY_PAYMENT')) {
      console.log('🚫 Chargily filtered out - feature flag disabled');
      return false;
    }
    return true;
  });

  // Sync auto-selection with parent component
  useEffect(() => {
    if (enableAutoSelection && autoSelectedProcessor && autoSelectedMethod && !selectedProcessor) {
      setLocalSelectedProcessor(autoSelectedProcessor);
      onSelectionChange(autoSelectedProcessor, autoSelectedMethod);
    }
  }, [autoSelectedProcessor, autoSelectedMethod, selectedProcessor, onSelectionChange, enableAutoSelection]);

  // Debug logging
  useEffect(() => {
    console.log('💳 PaymentMethodSelector Debug:', {
      isChargilyEnabled: isEnabled('CHARGILY_PAYMENT'),
      isAutoSelectionEnabled: isEnabled('CHARGILY_AUTO_SELECTION'),
      availableMethodsCount: availableMethods.length,
      availableMethods: availableMethods.map((m: any) => ({ id: m.id, name: m.displayName })),
      recommendedMethod: recommendedMethod?.id,
      isAlgeria,
    });
  }, [availableMethods, recommendedMethod, isAlgeria, isEnabled]);



  if (isLoading) {
    return (
      <div className={`space-y-3 ${className}`}>
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('subscription.choosePaymentMethod')}
        </h4>
        <div className="animate-pulse space-y-3">
          {[1, 2].map((i) => (
            <div key={i} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                <div className="w-8 h-5 bg-gray-300 dark:bg-gray-600 rounded"></div>
                <div className="w-32 h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('subscription.choosePaymentMethod')}
        </h4>
        <ErrorDisplay
          error={error}
          className="mb-4"
        />
      </div>
    );
  }



  if (availableMethods.length === 0) {
    return (
      <div className={className}>
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('subscription.choosePaymentMethod')}
        </h4>
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">
            No payment methods available for your location.
          </p>
        </div>
      </div>
    );
  }

  const handleProcessorChange = (processor: PaymentProcessor, method: PaymentMethodType) => {
    setLocalSelectedProcessor(processor);
    onSelectionChange(processor, method);

    // Notify auto-selection hook about user interaction
    if (enableAutoSelection) {
      handleUserSelection(processor, method);
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
          {t('subscription.choosePaymentMethod')}
        </h4>
        {isAlgeria && (
          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full dark:bg-green-900/20 dark:text-green-400">
            Algeria
          </span>
        )}
      </div>

      <div className="space-y-3">
        {availableMethods.map((method: any) => (
          <PaymentMethodCard
            key={method.id}
            method={method}
            isSelected={localSelectedProcessor === method.id}
            selectedSubMethod={selectedMethod}
            onSelect={handleProcessorChange}
            disabled={disabled}
            isRecommended={recommendedMethod?.id === method.id}
          />
        ))}
      </div>

      {/* Auto-selection info */}
      {showAutoSelectionInfo && isAutoSelected && reasoning && false && (
        <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
          <div className="flex items-start space-x-2">
            <svg className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <div>
              <p className="text-sm font-medium text-green-900 dark:text-green-100">
                Auto-selected for you
                <span className={`ml-2 text-xs px-2 py-0.5 rounded-full ${
                  confidence === 'high'
                    ? 'bg-green-200 text-green-800 dark:bg-green-800 dark:text-green-200'
                    : confidence === 'medium'
                    ? 'bg-yellow-200 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-200'
                    : 'bg-gray-200 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
                }`}>
                  {confidence} confidence
                </span>
              </p>
              <p className="text-sm text-green-700 dark:text-green-300">
                {reasoning}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Recommendation info (when not auto-selected) */}
      {recommendedMethod && !isAutoSelected && (
        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-start space-x-2">
            <svg className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <div>
              <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                {t('subscription.recommendedForYou')}
              </p>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                {translatePaymentDescription(recommendedMethod.description)}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

interface PaymentMethodCardProps {
  method: PaymentMethod;
  isSelected: boolean;
  selectedSubMethod: PaymentMethodType | '';
  onSelect: (processor: PaymentProcessor, method: PaymentMethodType) => void;
  disabled?: boolean;
  isRecommended?: boolean;
}

const PaymentMethodCard: React.FC<PaymentMethodCardProps> = ({
  method,
  isSelected,
  selectedSubMethod,
  onSelect,
  disabled = false,
  isRecommended = false,
}) => {
  const { t } = useTranslation('dashboard');
  const features = getPaymentMethodFeatures(method);
  const defaultSubMethod = method.supportedMethods[0];

  // Function to translate payment method descriptions
  const translatePaymentDescription = (description: string): string => {
    switch (description) {
      case 'Local Algerian payment gateway supporting EDAHABIA and CIB':
        return t('subscription.localAlgerianPayment');
      case 'International payment processor supporting cards and PayPal':
        return t('subscription.internationalPaymentProcessor');
      default:
        return description;
    }
  };

  // Function to get currency description
  const getCurrencyDescription = (currency: string): string => {
    switch (currency) {
      case 'DZD':
        return t('subscription.localAlgerianPayment');
      case 'USD':
        return t('subscription.internationalPaymentProcessor');
      default:
        return currency;
    }
  };

  // Function to translate feature benefits
  const translateFeatureBenefit = (benefit: string): string => {
    switch (benefit) {
      case 'No international fees':
        return t('subscription.noInternationalFees');
      case 'DZD currency':
        return t('subscription.dzdCurrency');
      case 'Local Algerian support':
        return t('subscription.localAlgerianSupport');
      case 'Instant processing':
        return t('subscription.instantProcessing');
      case 'Multiple currencies':
        return t('subscription.multipleCurrencies');
      case 'International support':
        return t('subscription.internationalSupport');
      default:
        return benefit;
    }
  };

  const handleClick = () => {
    if (disabled) return;
    onSelect(method.id as PaymentProcessor, selectedSubMethod || defaultSubMethod);
  };

  return (
    <div 
      className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
        disabled 
          ? 'opacity-50 cursor-not-allowed' 
          : isSelected 
            ? 'border-brand-500 bg-brand-50 dark:bg-brand-900/20 shadow-sm' 
            : 'border-gray-200 dark:border-gray-700 hover:border-brand-300 dark:hover:border-brand-600'
      }`}
      onClick={handleClick}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <input
            type="radio"
            checked={isSelected}
            onChange={handleClick}
            disabled={disabled}
            className="w-4 h-4 text-brand-600 bg-gray-100 border-gray-300 focus:ring-brand-500 dark:focus:ring-brand-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
          />

          <div className="flex items-center space-x-3">
            {/* Currency Icon */}
            <CurrencyIcon currency={method.currency} />

            <div>
              <div className="flex items-center space-x-2">
                <span className="font-medium text-gray-900 dark:text-white">
                  {method.currency}
                </span>
                {isRecommended && (
                  <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded-full dark:bg-green-900/20 dark:text-green-400">
                    {t('subscription.recommended')}
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {getCurrencyDescription(method.currency)}
              </p>
            </div>
          </div>
        </div>

        <div className="text-right">
          <div className="flex flex-col items-end space-y-1">
            {method.supportedMethods.map((subMethod) => (
              <PaymentOperatorBadge key={subMethod} method={subMethod} />
            ))}
          </div>
        </div>
      </div>

      
      {/* Features */}
      {features.benefits.length > 0 && (
        <div className="mt-3">
          <div className="flex flex-wrap gap-1">
            {features.benefits.slice(0, 3).map((benefit, index) => (
              <span
                key={index}
                className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded"
              >
                {translateFeatureBenefit(benefit)}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

const CurrencyIcon: React.FC<{ currency: string }> = ({ currency }) => {
  if (currency === 'DZD') {
    return (
      <div className="w-8 h-8 bg-green-600 rounded flex items-center justify-center">
        <span className="text-white font-bold text-xs">DZD</span>
      </div>
    );
  }

  if (currency === 'USD') {
    return (
      <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
        <span className="text-white font-bold text-xs">USD</span>
      </div>
    );
  }

  return (
    <div className="w-8 h-8 bg-gray-400 rounded flex items-center justify-center">
      <span className="text-white font-bold text-xs">{currency}</span>
    </div>
  );
};

const PaymentOperatorBadge: React.FC<{ method: PaymentMethodType }> = ({ method }) => {
  const { t } = useTranslation('dashboard');

  const getBadgeStyle = (method: PaymentMethodType) => {
    switch (method) {
      case 'edahabia':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'cib':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'card':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'paypal':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getDisplayName = (method: PaymentMethodType) => {
    switch (method) {
      case 'edahabia':
        return t('subscription.edahabia');
      case 'cib':
        return t('subscription.cib');
      case 'card':
        return t('subscription.card');
      case 'paypal':
        return t('subscription.paypal');
      default:
        return String(method).toUpperCase();
    }
  };

  return (
    <span className={`text-xs font-medium px-2 py-1 rounded ${getBadgeStyle(method)}`}>
      {getDisplayName(method)}
    </span>
  );
};

export default PaymentMethodSelector;
