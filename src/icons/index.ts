// Import only the SVG files that actually exist in the icons directory
import { ReactComponent as AlertHexaIcon } from "./alert-hexa.svg?react";
import { ReactComponent as AlertIcon } from "./alert.svg?react";
import { ReactComponent as AngleDownIcon } from "./angle-down.svg?react";
import { ReactComponent as AngleLeftIcon } from "./angle-left.svg?react";
import { ReactComponent as AngleRightIcon } from "./angle-right.svg?react";
import { ReactComponent as AngleUpIcon } from "./angle-up.svg?react";
import { ReactComponent as ArrowDownIcon } from "./arrow-down.svg?react";
import { ReactComponent as ArrowRightIcon } from "./arrow-right.svg?react";
import { ReactComponent as ArrowUpIcon } from "./arrow-up.svg?react";
import { ReactComponent as AudioIcon } from "./audio.svg?react";
import { ReactComponent as BoltIcon } from "./bolt.svg?react";
import { ReactComponent as BoxCubeIcon } from "./box-cube.svg?react";
import { ReactComponent as BoxLineIcon } from "./box-line.svg?react";
import { ReactComponent as BoxIcon } from "./box.svg?react";
import { ReactComponent as CalendarIcon } from "./calendar.svg?react";
import { ReactComponent as CalenderLineIcon } from "./calender-line.svg?react";
import { ReactComponent as ChatIcon } from "./chat.svg?react";
import { ReactComponent as CheckCircleIcon } from "./check-circle.svg?react";
import { ReactComponent as CheckLineIcon } from "./check-line.svg?react";
import { ReactComponent as ChevronDownIcon } from "./chevron-down.svg?react";
import { ReactComponent as ChevronLeftIcon } from "./chevron-left.svg?react";
import { ReactComponent as ChevronUpIcon } from "./chevron-up.svg?react";
import { ReactComponent as CloseLineIcon } from "./close-line.svg?react";
import { ReactComponent as CloseIcon } from "./close.svg?react";
import { ReactComponent as CopyIcon } from "./copy.svg?react";
import { ReactComponent as DocsIcon } from "./docs.svg?react";
import { ReactComponent as DollarLineIcon } from "./dollar-line.svg?react";
import { ReactComponent as DownloadIcon } from "./download.svg?react";
import { ReactComponent as EnvelopeIcon } from "./envelope.svg?react";
import { ReactComponent as EyeCloseIcon } from "./eye-close.svg?react";
import { ReactComponent as EyeIcon } from "./eye.svg?react";
import { ReactComponent as FileIcon } from "./file.svg?react";
import { ReactComponent as FolderIcon } from "./folder.svg?react";
import { ReactComponent as GridIcon } from "./grid.svg?react";
import { ReactComponent as GroupIcon } from "./group.svg?react";
import { ReactComponent as HorizontalDotsIcon } from "./horizontal-dots.svg?react";
import { ReactComponent as InfoErrorIcon } from "./info-error.svg?react";
import { ReactComponent as InfoHexaIcon } from "./info-hexa.svg?react";
import { ReactComponent as InfoIcon } from "./info.svg?react";
import { ReactComponent as ListIcon } from "./list.svg?react";
import { ReactComponent as LockIcon } from "./lock.svg?react";
import { ReactComponent as MailLineIcon } from "./mail-line.svg?react";
import { ReactComponent as MoreDotIcon } from "./moredot.svg?react";
import { ReactComponent as PageIcon } from "./page.svg?react";
import { ReactComponent as PaperPlaneIcon } from "./paper-plane.svg?react";
import { ReactComponent as PencilIcon } from "./pencil.svg?react";
import { ReactComponent as PieChartIcon } from "./pie-chart.svg?react";
import { ReactComponent as PlugInIcon } from "./plug-in.svg?react";
import { ReactComponent as PlusIcon } from "./plus.svg?react";
import { ReactComponent as ShootingStarIcon } from "./shooting-star.svg?react";
import { ReactComponent as TableIcon } from "./table.svg?react";
import { ReactComponent as TaskIcon } from "./task-icon.svg?react";
import { ReactComponent as TimeIcon } from "./time.svg?react";
import { ReactComponent as TrashIcon } from "./trash.svg?react";
import { ReactComponent as UserCircleIcon } from "./user-circle.svg?react";
import { ReactComponent as UserLineIcon } from "./user-line.svg?react";
import { ReactComponent as VideosIcon } from "./videos.svg?react";

// Create aliases for commonly used icons to maintain compatibility
export const EditIcon = PencilIcon; // Use pencil icon for edit
export const DeleteIcon = TrashIcon; // Use trash icon for delete
export const EyeSlashIcon = EyeCloseIcon; // Alias for eye-close
export const SearchIcon = InfoIcon; // Temporary alias
export const FilterIcon = InfoIcon; // Temporary alias
export const SortIcon = InfoIcon; // Temporary alias
export const ArrowLeftIcon = AngleLeftIcon; // Use angle-left for arrow-left
export const ChevronRightIcon = AngleRightIcon; // Use angle-right for chevron-right
export const HomeIcon = InfoIcon; // Temporary alias
export const DashboardIcon = GridIcon; // Use grid for dashboard
export const UserIcon = UserCircleIcon; // Use user-circle for user
export const UsersIcon = GroupIcon; // Use group for users
export const SettingsIcon = InfoIcon; // Temporary alias
export const BellIcon = AlertIcon; // Use alert for bell
export const MailIcon = EnvelopeIcon; // Use envelope for mail
export const PhoneIcon = InfoIcon; // Temporary alias
export const LocationIcon = InfoIcon; // Temporary alias
export const CalenderIcon = CalendarIcon; // Alias for calendar (fixing typo)
export const DateIcon = CalendarIcon; // Use calendar for date
export const ClockIcon = TimeIcon; // Use time for clock
export const StarIcon = ShootingStarIcon; // Use shooting-star for star
export const HeartIcon = InfoIcon; // Temporary alias
export const ThumbsUpIcon = InfoIcon; // Temporary alias
export const ThumbsDownIcon = InfoIcon; // Temporary alias
export const ShareIcon = PaperPlaneIcon; // Use paper-plane for share
export const UploadIcon = InfoIcon; // Temporary alias

// Customer management specific aliases
export const XMarkIcon = CloseIcon; // Alias for close icon
export const IdentificationIcon = InfoIcon; // Temporary alias
export const DocumentTextIcon = DocsIcon; // Use docs for document text
export const ChartBarIcon = PieChartIcon; // Use pie-chart for chart bar
export const UserGroupIcon = GroupIcon; // Use group for user group
export const MagnifyingGlassIcon = InfoIcon; // Temporary alias

// Additional missing aliases
export const TrashBinIcon = TrashIcon; // Alias for trash icon
export const HorizontaLDots = HorizontalDotsIcon; // Fix typo in import

// Export all icons
export {
  AlertHexaIcon,
  AlertIcon,
  AngleDownIcon,
  AngleLeftIcon,
  AngleRightIcon,
  AngleUpIcon,
  ArrowDownIcon,
  ArrowRightIcon,
  ArrowUpIcon,
  AudioIcon,
  BoltIcon,
  BoxCubeIcon,
  BoxLineIcon,
  BoxIcon,
  CalendarIcon,
  CalenderLineIcon,
  ChatIcon,
  CheckCircleIcon,
  CheckLineIcon,
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronUpIcon,
  CloseLineIcon,
  CloseIcon,
  CopyIcon,
  DocsIcon,
  DollarLineIcon,
  DownloadIcon,
  EnvelopeIcon,
  EyeCloseIcon,
  EyeIcon,
  FileIcon,
  FolderIcon,
  GridIcon,
  GroupIcon,
  HorizontalDotsIcon,
  InfoErrorIcon,
  InfoHexaIcon,
  InfoIcon,
  ListIcon,
  LockIcon,
  MailLineIcon,
  MoreDotIcon,
  PageIcon,
  PaperPlaneIcon,
  PencilIcon,
  PieChartIcon,
  PlugInIcon,
  PlusIcon,
  ShootingStarIcon,
  TableIcon,
  TaskIcon,
  TimeIcon,
  TrashIcon,
  UserCircleIcon,
  UserLineIcon,
  VideosIcon,
  // Note: Aliases are exported as individual export statements above, not in this list
};
