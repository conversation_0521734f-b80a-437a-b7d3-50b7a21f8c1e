import React, { createContext, useContext, useReducer, useEffect, useRef, ReactNode } from 'react';
import { AuthState, User, Provider, LoginRequest, LoginResponse, UserSubscriptionData, UsedCreditsData } from '../types';
import { AuthService } from '../services/auth.service';
import { SubscriptionService } from '../services/subscription.service';
import { UserService } from '../services/user.service';
import { ErrorLogger } from '../lib/error-utils';
import { config } from '../lib/config';
import toast from 'react-hot-toast';
import { LanguageCode } from './LanguageContext';

// Auth action types
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; provider: Provider; token: string } }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'UPDATE_PROVIDER'; payload: Provider }
  | { type: 'CLEAR_ERROR' }
  | { type: 'LOGIN_ATTEMPT_START' }
  | { type: 'LOGIN_ATTEMPT_END' }
  | { type: 'SUBSCRIPTION_START' }
  | { type: 'SUBSCRIPTION_SUCCESS'; payload: UserSubscriptionData }
  | { type: 'SUBSCRIPTION_ERROR'; payload: string }
  | { type: 'SUBSCRIPTION_REFRESH' }
  | { type: 'USED_CREDITS_START' }
  | { type: 'USED_CREDITS_SUCCESS'; payload: UsedCreditsData }
  | { type: 'USED_CREDITS_ERROR'; payload: string }
  | { type: 'USED_CREDITS_REFRESH' };

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  provider: null,
  token: null,
  isLoading: false, // Start with loading to check stored auth
  error: null,
  subscription: null,
  subscriptionLoading: false,
  subscriptionError: null,
  usedCredits: null,
  usedCreditsLoading: false,
  usedCreditsError: null,
  isTryingToLogIn: false,
};

// Auth reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  // Debug auth actions in development
  if (import.meta.env.DEV) {
    console.log('🔐 Auth action dispatched:', action.type, action);
    console.trace('Auth action stack trace');
  }

  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case 'AUTH_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        provider: action.payload.provider,
        token: action.payload.token,
        isLoading: false,
        error: null,
      };

    case 'AUTH_ERROR':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        provider: null,
        token: null,
        isLoading: false,
        error: action.payload,
      };

    case 'AUTH_LOGOUT':
      return {
        ...initialState,
        isLoading: false,
      };

    case 'UPDATE_PROVIDER':
      return {
        ...state,
        provider: action.payload,
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    case 'LOGIN_ATTEMPT_START':
      return {
        ...state,
        isTryingToLogIn: true,
      };

    case 'LOGIN_ATTEMPT_END':
      return {
        ...state,
        isTryingToLogIn: false,
      };

    case 'SUBSCRIPTION_START':
      return {
        ...state,
        subscriptionLoading: true,
        subscriptionError: null,
      };

    case 'SUBSCRIPTION_SUCCESS':
      return {
        ...state,
        subscription: action.payload,
        subscriptionLoading: false,
        subscriptionError: null,
      };

    case 'SUBSCRIPTION_ERROR':
      return {
        ...state,
        subscription: null,
        subscriptionLoading: false,
        subscriptionError: action.payload,
      };

    case 'SUBSCRIPTION_REFRESH':
      return {
        ...state,
        subscriptionLoading: true,
        subscriptionError: null,
      };

    case 'USED_CREDITS_START':
      return {
        ...state,
        usedCreditsLoading: true,
        usedCreditsError: null,
      };

    case 'USED_CREDITS_SUCCESS':
      return {
        ...state,
        usedCredits: action.payload,
        usedCreditsLoading: false,
        usedCreditsError: null,
      };

    case 'USED_CREDITS_ERROR':
      return {
        ...state,
        usedCredits: null,
        usedCreditsLoading: false,
        usedCreditsError: action.payload,
      };

    case 'USED_CREDITS_REFRESH':
      return {
        ...state,
        usedCreditsLoading: true,
        usedCreditsError: null,
      };

    default:
      return state;
  }
};

// Auth context type
interface AuthContextType extends AuthState {
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  updateProvider: (provider: Provider) => void;
  clearError: () => void;
  checkAuthStatus: () => Promise<void>;
  setAuthenticatedUser: (user: User, provider: Provider, token: string) => Promise<void>;
  fetchSubscriptionData: () => Promise<void>;
  refreshSubscriptionData: () => Promise<void>;
  hasActiveSubscription: () => boolean;
  getCurrentCredits: () => number;
  getTotalAvailableCredits: () => number;
  getCurrentQueueLimit: () => number;
  getCurrentQueueUsed: () => number;
  isAtCreditLimit: (threshold?: number) => boolean;
  isAtQueueLimit: () => boolean;
  fetchUsedCreditsData: () => Promise<void>;
  refreshUsedCreditsData: () => Promise<void>;
  getUsedCreditsThisMonth: () => number;
  getRemainingCredits: () => number;
  getMonthlyAllocatedCredits: () => number;
  getCompletedAppointmentsThisMonth: () => number;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check authentication status on mount (only once and not during login attempts)
  useEffect(() => {
    console.log("authContext subscription console.log",state)
    // Prevent multiple calls due to StrictMode or re-renders
    // Also don't check auth status if we're currently trying to log in
    if ( !state.isTryingToLogIn) {
      checkAuthStatus();
    }
  }, []); // Re-run if login attempt state changes


  useEffect(() => {
      console.log("AuthContext state subcsription", state);
  }, [state.usedCredits]);

  /**
   * Sync local language preference with backend after authentication
   * This ensures the backend preferred language matches the localStorage language
   */
  const syncLanguagePreference = async (): Promise<void> => {
    try {
      const localLanguage = localStorage.getItem('dalti-language') as LanguageCode;

      if (localLanguage && ['en', 'ar', 'fr'].includes(localLanguage)) {
        console.log(`🌐 Syncing language preference: ${localLanguage}`);
        await UserService.updatePreferredLanguage(localLanguage);
        console.log(`✅ Language preference synced successfully: ${localLanguage}`);
      }
    } catch (error) {
      // Don't throw error - this is a non-critical operation
      console.warn('⚠️ Failed to sync language preference after login:', error);
      ErrorLogger.log(error as Error, {
        context: 'syncLanguagePreference',
        localLanguage: localStorage.getItem('dalti-language')
      });
    }
  };

  const checkAuthStatus = async (): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });

      // Check if user is authenticated and session is valid
      if (!AuthService.isAuthenticated() || !AuthService.isSessionValid()) {
        // Session expired or invalid, clear everything
        AuthService.logout();
        dispatch({ type: 'AUTH_LOGOUT' });
        return;
      }

      // Get stored user data
      const storedData = AuthService.getStoredUser();
      const token = AuthService.getToken();

      if (storedData && token) {
        // Update last activity since we're restoring the session
        AuthService.updateLastActivity();

        dispatch({
          type: 'AUTH_SUCCESS',
          payload: {
            user: storedData.user,
            provider: storedData.provider,
            token,
          },
        });

        // Sync language preference with backend before fetching data
        await syncLanguagePreference();

        // Fetch subscription data after successful auth restoration
        fetchSubscriptionData();

        // Fetch used credits data after successful auth restoration (deferred and non-blocking)
        setTimeout(() => {
          fetchUsedCreditsData().catch(error => {
            console.warn('Failed to fetch used credits data during auth restoration:', error);
          });
        }, 100);
      } else {
        // Clear any partial data and logout
        AuthService.logout();
        dispatch({ type: 'AUTH_LOGOUT' });
      }
    } catch (error) {
      ErrorLogger.log(error as Error, { context: 'checkAuthStatus' });
      // Clear any corrupted data and logout
      AuthService.logout();
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  };

  const login = async (credentials: LoginRequest): Promise<any> => {
    try {
      dispatch({ type: 'LOGIN_ATTEMPT_START' });
      dispatch({ type: 'AUTH_START' });

      const response: LoginResponse = await AuthService.login(credentials);

      // Store tokens and user data
      const token = response.sessionId; // Using sessionId as token for now
      AuthService.storeTokens(token);
      AuthService.storeUserData(response.user, response.provider);
      AuthService.updateLastActivity(); // Set initial activity timestamp

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          provider: response.provider,
          token,
        },
      });

      // Sync language preference with backend before fetching data
      await syncLanguagePreference();

      // Fetch subscription data after successful login
      fetchSubscriptionData();

      // Fetch used credits data after successful login (deferred and non-blocking)
      setTimeout(() => {
        fetchUsedCreditsData().catch(error => {
          console.warn('Failed to fetch used credits data during login:', error);
        });
      }, 100);

      toast.success('Login successful!');
      dispatch({ type: 'LOGIN_ATTEMPT_END' });
    } catch (error: any) {
      const errorMessage = error?.message || 'Login failed. Please try again.';
      ErrorLogger.log(error, { context: 'login', credentials: { identifier: credentials.identifier } });
      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      dispatch({ type: 'LOGIN_ATTEMPT_END' });
      toast.error(errorMessage);
      throw error;
    }
  };

  const logout = (): void => {
    try {
      AuthService.logout();
      dispatch({ type: 'AUTH_LOGOUT' });
      toast.success('Logged out successfully');
      // Redirect to login page after logout
      window.location.href = '/signin';
    } catch (error) {
      ErrorLogger.log(error as Error, { context: 'logout' });
      // Still dispatch logout even if there's an error
      dispatch({ type: 'AUTH_LOGOUT' });
      window.location.href = '/signin';
    }
  };

  const updateProvider = (provider: Provider): void => {
    try {
      // Update stored data
      if (state.user) {
        AuthService.storeUserData(state.user, provider);
      }
      dispatch({ type: 'UPDATE_PROVIDER', payload: provider });
    } catch (error) {
      ErrorLogger.log(error as Error, { context: 'updateProvider' });
    }
  };

  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const setAuthenticatedUser = async (user: User, provider: Provider, token: string): Promise<void> => {
    // Update activity timestamp when setting authenticated user
    AuthService.updateLastActivity();

    dispatch({
      type: 'AUTH_SUCCESS',
      payload: {
        user,
        provider,
        token,
      },
    });

    // Sync language preference with backend before fetching data
    await syncLanguagePreference();

    // Fetch subscription data after successful authentication
    fetchSubscriptionData();

    // Fetch used credits data after successful authentication (deferred and non-blocking)
    setTimeout(() => {
      fetchUsedCreditsData().catch(error => {
        console.warn('Failed to fetch used credits data during authentication:', error);
      });
    }, 100);
  };

  const fetchSubscriptionData = async (): Promise<void> => {
    if (!state.isAuthenticated) return;

    try {
      dispatch({ type: 'SUBSCRIPTION_START' });
      const response = await SubscriptionService.getUserPaymentStatus();

      if (response.success) {
        dispatch({
          type: 'SUBSCRIPTION_SUCCESS',
          payload: response.data,
        });
      } else {
        dispatch({
          type: 'SUBSCRIPTION_ERROR',
          payload: response.message || 'Failed to fetch subscription data',
        });
      }
    } catch (error) {
      ErrorLogger.log(error as Error, { context: 'fetchSubscriptionData' });
      dispatch({
        type: 'SUBSCRIPTION_ERROR',
        payload: 'Failed to fetch subscription data',
      });
    }
  };

  const refreshSubscriptionData = async (): Promise<void> => {
    dispatch({ type: 'SUBSCRIPTION_REFRESH' });
    await fetchSubscriptionData();
  };

  // Used Credits Functions
  const fetchUsedCreditsData = async (): Promise<void> => {
    console.log('🔍 fetchUsedCreditsData called - isAuthenticated:', state.isAuthenticated);
    if (!state.isAuthenticated) {
      console.log('❌ fetchUsedCreditsData: User not authenticated, skipping');
      return;
    }

    try {
      console.log('📡 fetchUsedCreditsData: Making API call to /api/auth/payment/usage');
      dispatch({ type: 'USED_CREDITS_START' });
      const response = await AuthService.getUsedCredits();
      console.log('📡 fetchUsedCreditsData: API response received:', response);

      if (response.success) {
        console.log('✅ fetchUsedCreditsData: Success, dispatching data:', response.data);
        dispatch({
          type: 'USED_CREDITS_SUCCESS',
          payload: response.data,
        });
      } else {
        console.log('❌ fetchUsedCreditsData: API returned error:', response.message);
        dispatch({
          type: 'USED_CREDITS_ERROR',
          payload: response.message || 'Failed to fetch used credits data',
        });
      }
    } catch (error) {
      console.error('❌ fetchUsedCreditsData: Exception caught:', error);
      ErrorLogger.log(error as Error, { context: 'fetchUsedCreditsData' });
      dispatch({
        type: 'USED_CREDITS_ERROR',
        payload: 'Failed to fetch used credits data',
      });
    }
  };

  const refreshUsedCreditsData = async (): Promise<void> => {
    dispatch({ type: 'USED_CREDITS_REFRESH' });
    await fetchUsedCreditsData();
  };

  const getUsedCreditsThisMonth = (): number => {
    return state.usedCredits?.credits?.used || 0;
  };

  const getRemainingCredits = (): number => {
    return state.usedCredits?.credits?.remaining || 0;
  };

  const getMonthlyAllocatedCredits = (): number => {
    return state.usedCredits?.credits?.allocated || 0;
  };

  const getCompletedAppointmentsThisMonth = (): number => {
    return state.usedCredits?.appointments?.completedMonth || 0;
  };

  const hasActiveSubscription = (): boolean => {
    return state.subscription?.subscription?.isActive || false;
  };

  const getCurrentCredits = (): number => {
    return state.usedCredits?.credits?.used || 0;
  };

  const getTotalAvailableCredits = (): number => {
    return state.usedCredits?.credits?.totalAvailable || 0;
  };

  const getCurrentQueueLimit = (): number => {
    return state.usedCredits?.limits?.queues || 0;
  };

  const getCurrentQueueUsed = (): number => {
    return state.usedCredits?.limits?.usedQueues || 0;
  };

  const isAtCreditLimit = (threshold: number = 0.1): boolean => {
    if (!state.subscription) return false;
    const credits = state.usedCredits?.credits?.used || 0;
    const limit = state.usedCredits?.credits?.totalAvailable || 0;
    return credits >= (limit * threshold);
  };

  const isAtQueueLimit = (): boolean => {
    console.log('isAtQueueLimit called ', state);
    if (!state.subscription) return false;
    const current = state.usedCredits?.limits?.usedQueues || 0;
    const limit = state.usedCredits?.limits?.queues || 0;
    return current >= limit;
  };



  const contextValue: AuthContextType = {
    ...state,
    login,
    logout,
    updateProvider,
    clearError,
    checkAuthStatus,
    setAuthenticatedUser,
    fetchSubscriptionData,
    refreshSubscriptionData,
    hasActiveSubscription,
    getCurrentCredits,
    getTotalAvailableCredits,
    getCurrentQueueLimit,
    getCurrentQueueUsed,
    isAtCreditLimit,
    isAtQueueLimit,
    fetchUsedCreditsData,
    refreshUsedCreditsData,
    getUsedCreditsThisMonth,
    getRemainingCredits,
    getMonthlyAllocatedCredits,
    getCompletedAppointmentsThisMonth,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
