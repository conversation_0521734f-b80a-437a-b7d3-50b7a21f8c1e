import { ApiError } from '../types';
import { config } from './config';

/**
 * Error logging utility
 */
export class ErrorLogger {
  static log(error: Error | ApiError | string, context?: any): void {
    const timestamp = new Date().toISOString();
    const errorData = {
      timestamp,
      error: this.serializeError(error),
      context,
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.getCurrentUserId(),
    };

    // Log to console in development
    if (config.app.environment === 'development') {
      console.error('Error logged:', errorData);
    }

    // Store in localStorage for debugging (limited storage)
    this.storeErrorLocally(errorData);

    // Here you would typically send to your logging service
    // this.sendToLoggingService(errorData);
  }

  private static serializeError(error: Error | ApiError | string): any {
    if (typeof error === 'string') {
      return { message: error, type: 'string' };
    }

    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: error.stack,
        type: 'Error',
      };
    }

    return {
      ...error,
      type: 'ApiError',
    };
  }

  private static getCurrentUserId(): string | null {
    try {
      const userData = localStorage.getItem(config.auth.userKey);
      if (userData) {
        const user = JSON.parse(userData);
        return user.id || null;
      }
    } catch {
      // Ignore parsing errors
    }
    return null;
  }

  private static storeErrorLocally(errorData: any): void {
    try {
      const key = 'error_logs';
      const existing = localStorage.getItem(key);
      const logs = existing ? JSON.parse(existing) : [];
      
      // Keep only last 50 errors to prevent storage overflow
      logs.push(errorData);
      if (logs.length > 50) {
        logs.splice(0, logs.length - 50);
      }
      
      localStorage.setItem(key, JSON.stringify(logs));
    } catch {
      // Ignore storage errors
    }
  }

  static getStoredErrors(): any[] {
    try {
      const logs = localStorage.getItem('error_logs');
      return logs ? JSON.parse(logs) : [];
    } catch {
      return [];
    }
  }

  static clearStoredErrors(): void {
    localStorage.removeItem('error_logs');
  }
}

/**
 * Error classification utilities
 */
export const ErrorClassifier = {
  isNetworkError(error: any): boolean {
    return (
      error?.status === 0 ||
      error?.code === 'NETWORK_ERROR' ||
      error?.message?.includes('Network Error') ||
      error?.message?.includes('fetch')
    );
  },

  isAuthError(error: any): boolean {
    return error?.status === 401 || error?.status === 403;
  },

  isValidationError(error: any): boolean {
    return error?.status === 422 || (error?.errors && Array.isArray(error.errors));
  },

  isServerError(error: any): boolean {
    return error?.status >= 500;
  },

  isClientError(error: any): boolean {
    return error?.status >= 400 && error?.status < 500;
  },

  getErrorType(error: any): string {
    if (this.isNetworkError(error)) return 'network';
    if (this.isAuthError(error)) return 'auth';
    if (this.isValidationError(error)) return 'validation';
    if (this.isServerError(error)) return 'server';
    if (this.isClientError(error)) return 'client';
    return 'unknown';
  },
};

/**
 * Error message formatting utilities
 */
export const ErrorFormatter = {
  formatApiError(error: ApiError): string {
    if (error.errors && error.errors.length > 0) {
      return error.errors.join(', ');
    }
    return error.message || 'An unexpected error occurred';
  },

  formatValidationErrors(errors: any[]): Record<string, string> {
    const formatted: Record<string, string> = {};
    
    errors.forEach((error) => {
      if (typeof error === 'object' && error.field && error.message) {
        formatted[error.field] = error.message;
      }
    });
    
    return formatted;
  },

  getUserFriendlyMessage(error: any): string {
    const errorType = ErrorClassifier.getErrorType(error);
    
    switch (errorType) {
      case 'network':
        return 'Unable to connect to the server. Please check your internet connection and try again.';
      case 'auth':
        return 'Your session has expired. Please sign in again.';
      case 'validation':
        return 'Please check your input and try again.';
      case 'server':
        return 'A server error occurred. Please try again later.';
      default:
        return error?.message || 'An unexpected error occurred. Please try again.';
    }
  },
};

/**
 * Error retry utilities
 */
export const ErrorRetry = {
  shouldRetry(error: any, attempt: number, maxAttempts: number = 3): boolean {
    if (attempt >= maxAttempts) return false;
    
    // Don't retry auth errors or validation errors
    if (ErrorClassifier.isAuthError(error) || ErrorClassifier.isValidationError(error)) {
      return false;
    }
    
    // Retry network errors and server errors
    return ErrorClassifier.isNetworkError(error) || ErrorClassifier.isServerError(error);
  },

  getRetryDelay(attempt: number, baseDelay: number = 1000): number {
    // Exponential backoff with jitter
    const delay = baseDelay * Math.pow(2, attempt);
    const jitter = Math.random() * 0.1 * delay;
    return delay + jitter;
  },

  async withRetry<T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (!this.shouldRetry(error, attempt, maxAttempts)) {
          throw error;
        }
        
        if (attempt < maxAttempts - 1) {
          const delay = this.getRetryDelay(attempt, baseDelay);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError;
  },
};

/**
 * Global error handler for unhandled promise rejections
 */
export const setupGlobalErrorHandling = (): void => {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    ErrorLogger.log(event.reason, { type: 'unhandledrejection' });
    
    // Prevent the default browser behavior
    event.preventDefault();
  });

  // Handle global errors
  window.addEventListener('error', (event) => {
    ErrorLogger.log(event.error || event.message, {
      type: 'global',
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    });
  });
};

/**
 * Error boundary error handler
 */
export const handleErrorBoundaryError = (error: Error, errorInfo: any): void => {
  ErrorLogger.log(error, {
    type: 'react-error-boundary',
    componentStack: errorInfo.componentStack,
  });
};
