/**
 * Utility functions for organizing and working with provider categories
 */

import { ProviderCategory } from '../types';

/**
 * Organize categories into parent and child structure
 */
export function organizeCategories(categories: ProviderCategory[]) {
  const parentCategories = categories.filter(cat => !cat.parentId);
  const childCategories = categories.filter(cat => cat.parentId);

  // Group children by parent ID
  const childrenByParent = childCategories.reduce((acc, child) => {
    if (!acc[child.parentId!]) {
      acc[child.parentId!] = [];
    }
    acc[child.parentId!].push(child);
    return acc;
  }, {} as Record<number, ProviderCategory[]>);

  // Sort parent categories by sortOrder and title
  const sortedParents = parentCategories.sort((a, b) => {
    if (a.sortOrder !== b.sortOrder) {
      return a.sortOrder - b.sortOrder;
    }
    return a.title.localeCompare(b.title);
  });

  // Sort children within each parent
  Object.keys(childrenByParent).forEach(parentId => {
    childrenByParent[parseInt(parentId)] = childrenByParent[parseInt(parentId)].sort((a, b) => {
      if (a.sortOrder !== b.sortOrder) {
        return a.sortOrder - b.sortOrder;
      }
      return a.title.localeCompare(b.title);
    });
  });

  return {
    parentCategories: sortedParents,
    childrenByParent,
    allCategories: categories,
  };
}

/**
 * Get children categories for a specific parent
 */
export function getChildrenForParent(categories: ProviderCategory[], parentId: number): ProviderCategory[] {
  return categories
    .filter(cat => cat.parentId === parentId)
    .sort((a, b) => {
      if (a.sortOrder !== b.sortOrder) {
        return a.sortOrder - b.sortOrder;
      }
      return a.title.localeCompare(b.title);
    });
}

/**
 * Get parent categories only
 */
export function getParentCategories(categories: ProviderCategory[]): ProviderCategory[] {
  return categories
    .filter(cat => !cat.parentId)
    .sort((a, b) => {
      if (a.sortOrder !== b.sortOrder) {
        return a.sortOrder - b.sortOrder;
      }
      return a.title.localeCompare(b.title);
    });
}

/**
 * Find a category by ID
 */
export function findCategoryById(categories: ProviderCategory[], id: number): ProviderCategory | undefined {
  return categories.find(cat => cat.id === id);
}

/**
 * Get the full category path (parent > child)
 */
export function getCategoryPath(categories: ProviderCategory[], categoryId: number): string {
  const category = findCategoryById(categories, categoryId);
  if (!category) return '';

  if (category.parentId) {
    const parent = findCategoryById(categories, category.parentId);
    return parent ? `${parent.title} > ${category.title}` : category.title;
  }

  return category.title;
}

/**
 * Check if a category has children
 */
export function hasChildren(categories: ProviderCategory[], parentId: number): boolean {
  return categories.some(cat => cat.parentId === parentId);
}

/**
 * Get category icon/emoji based on category data
 * First checks metadata.icon, then falls back to title-based mapping
 */
export function getCategoryIcon(categoryTitle: string, metadata?: any): string {
  // First, try to get icon from metadata (multiple possible fields)
  if (metadata) {
    // Check for icon field
    if (metadata.icon && typeof metadata.icon === 'string' && metadata.icon.trim()) {
      return metadata.icon;
    }

    // Check for emoji field (alternative naming)
    if (metadata.emoji && typeof metadata.emoji === 'string' && metadata.emoji.trim()) {
      return metadata.emoji;
    }

    // Check for iconUrl or similar (could be used for custom icons in the future)
    if (metadata.iconUrl && typeof metadata.iconUrl === 'string' && metadata.iconUrl.trim()) {
      // For now, return a generic icon if URL is provided
      return '🔗';
    }
  }
  const iconMap: Record<string, string> = {
    // Parent categories
    'Healthcare': '🏥',
    'Legal': '⚖️',
    'Financial': '💰',
    'Automotive': '🚗',
    'Home Services': '🏠',
    'Education': '📚',
    'Beauty & Wellness': '💅',
    'Professional Services': '💼',
    'Food & Dining': '🍽️',
    'Retail': '🛍️',
    'Government & Public Services': '🏛️',
    'Arts & Entertainment': '🎭',
    'Sports & Recreation': '⚽',
    'Pet Services': '🐕',
    'Trades & Construction': '🔨',
    
    // Child categories
    'Doctor': '👨‍⚕️',
    'Dentist': '🦷',
    'Physiotherapist': '🏃‍♂️',
    'Optometrist': '👁️',
    'Chiropractor': '🦴',
    'Pharmacy': '💊',
    'Notary': '📋',
    'Paralegal': '📄',
    'Mediator': '🤝',
    'Accountant': '🧮',
    'Financial Advisor': '📊',
    'Insurance Agent': '🛡️',
    'Bookkeeper': '📖',
    'Mechanic': '🔧',
    'Car Wash': '🚿',
    'Tire Shop': '🛞',
    'Body Shop': '🔨',
    'Plumber': '🚰',
    'Electrician': '⚡',
    'Landscaper': '🌱',
    'HVAC Technician': '❄️',
    'Painter': '🎨',
    'Cleaning Service': '🧽',
    'Tutor': '👨‍🏫',
    'Music Teacher': '🎵',
    'Driving School': '🚙',
    'Language School': '🗣️',
    'Hair Salon': '💇‍♀️',
    'Barber Shop': '💇‍♂️',
    'Nail Salon': '💅',
    'Spa': '🧖‍♀️',
    'Massage Therapist': '💆‍♀️',
    'Gym': '💪',
    'Consultant': '👔',
    'Marketing Agency': '📢',
  };

  // Fallback to title-based mapping for categories without metadata icons
  return iconMap[categoryTitle] || '📋';
}

/**
 * Get category icon using the full category object (preferred method)
 */
export function getCategoryIconFromObject(category: ProviderCategory): string {
  return getCategoryIcon(category.title, category.metadata);
}

/**
 * Alternative export name to force cache refresh
 */
export const getCategoryIconForComponent = getCategoryIconFromObject;

/**
 * Get category color from metadata, with fallback
 */
export function getCategoryColor(category: ProviderCategory): string {
  // Check metadata for color
  if (category.metadata?.color && typeof category.metadata.color === 'string') {
    return category.metadata.color;
  }

  // Default fallback color
  return '#6B7280'; // gray-500
}

/**
 * Check if category has custom metadata
 */
export function hasCustomMetadata(category: ProviderCategory): boolean {
  return !!(category.metadata && Object.keys(category.metadata).length > 0);
}
