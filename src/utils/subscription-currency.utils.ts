import { SupportedCurrency, formatCurrency } from './currency.utils';

/**
 * Utility functions for handling subscription plan pricing with dynamic currency
 */

/**
 * Extract numeric price from formatted price string
 * Handles various formats like "$9.99", "€19.99", "Free", "د.ج 1500"
 */
export function extractPriceFromString(priceString: string): number | null {
  // Handle "Free" case
  if (priceString.toLowerCase().includes('free') || priceString.toLowerCase().includes('gratuit') || priceString.toLowerCase().includes('مجاني')) {
    return 0;
  }

  // Remove currency symbols and extract number
  const cleanPrice = priceString
    .replace(/[€$د\.ج\s]/g, '') // Remove common currency symbols
    .replace(/[^\d.,]/g, '') // Keep only digits, dots, and commas
    .replace(',', '.'); // Normalize decimal separator

  const price = parseFloat(cleanPrice);
  return isNaN(price) ? null : price;
}

/**
 * Format subscription plan price with user's preferred currency
 * This is a temporary solution until backend supports dynamic currency
 */
export function formatSubscriptionPrice(
  originalPrice: string,
  targetCurrency: SupportedCurrency,
  exchangeRates?: Record<string, number>
): string {
  // Handle "Free" plans
  if (originalPrice.toLowerCase().includes('free') || originalPrice.toLowerCase().includes('gratuit') || originalPrice.toLowerCase().includes('مجاني')) {
    switch (targetCurrency) {
      case 'DZD':
        return 'مجاني';
      case 'EUR':
        return 'Gratuit';
      default:
        return 'Free';
    }
  }

  // Extract numeric value
  const numericPrice = extractPriceFromString(originalPrice);
  if (numericPrice === null) {
    return originalPrice; // Return original if we can't parse it
  }

  // If it's already 0 (free), format as free
  if (numericPrice === 0) {
    switch (targetCurrency) {
      case 'DZD':
        return 'مجاني';
      case 'EUR':
        return 'Gratuit';
      default:
        return 'Free';
    }
  }

  // Detect original currency from the price string
  let originalCurrency: SupportedCurrency = 'DZD'; // Default
  if (originalPrice.includes('€')) {
    originalCurrency = 'EUR';
  } else if (originalPrice.includes('د.ج')) {
    originalCurrency = 'DZD';
  }

  // If target currency is the same as original, return formatted version
  if (originalCurrency === targetCurrency) {
    return formatCurrency(numericPrice, targetCurrency);
  }

  // For now, we'll use simple conversion rates
  // In a real application, you'd fetch these from an API
  const defaultExchangeRates: Record<string, Record<string, number>> = {
    EUR: { DZD: 158 },
    DZD: { EUR: 0.0063 },
  };

  const rates = exchangeRates || defaultExchangeRates;
  const conversionRate = rates[originalCurrency]?.[targetCurrency] || 1;
  const convertedPrice = numericPrice * conversionRate;

  return formatCurrency(convertedPrice, targetCurrency);
}

/**
 * Format subscription plan with dynamic currency
 * Updates the plan object with user's preferred currency
 */
export function formatPlanWithCurrency(
  plan: any,
  targetCurrency: SupportedCurrency,
  exchangeRates?: Record<string, number>
): any {
  return {
    ...plan,
    price: formatSubscriptionPrice(plan.price, targetCurrency, exchangeRates),
    originalPrice: plan.price, // Keep original for reference
  };
}

/**
 * Format multiple plans with dynamic currency
 */
export function formatPlansWithCurrency(
  plans: any[],
  targetCurrency: SupportedCurrency,
  exchangeRates?: Record<string, number>
): any[] {
  return plans.map(plan => formatPlanWithCurrency(plan, targetCurrency, exchangeRates));
}

/**
 * Get currency-specific plan recommendations
 * Some plans might be more suitable for certain currencies/regions
 */
export function getCurrencySpecificRecommendations(
  plans: any[],
  currency: SupportedCurrency
): string[] {
  const recommendations: string[] = [];

  switch (currency) {
    case 'DZD':
      // For Algerian market, recommend plans with good value
      recommendations.push('Consider plans with higher credit amounts for better value in DZD');
      break;
    case 'EUR':
      // For European market
      recommendations.push('Monthly plans offer good flexibility for European businesses');
      break;
  }

  return recommendations;
}

/**
 * Validate if a price conversion makes sense
 * Helps prevent displaying unrealistic converted prices
 */
export function validatePriceConversion(
  originalPrice: number,
  convertedPrice: number,
  originalCurrency: SupportedCurrency,
  targetCurrency: SupportedCurrency
): boolean {
  // Basic sanity checks
  if (originalPrice <= 0 || convertedPrice <= 0) {
    return originalPrice === 0 && convertedPrice === 0;
  }

  // Check if conversion ratio is reasonable (between 0.01 and 100)
  const ratio = convertedPrice / originalPrice;
  return ratio >= 0.01 && ratio <= 100;
}

/**
 * Get localized currency display preferences
 */
export function getCurrencyDisplayPreferences(currency: SupportedCurrency) {
  switch (currency) {
    case 'DZD':
      return {
        showDecimals: false,
        compactThreshold: 1000,
        preferredFormat: 'symbol-after',
      };
    case 'EUR':
      return {
        showDecimals: true,
        compactThreshold: 10000,
        preferredFormat: 'symbol-after',
      };
    default:
      return {
        showDecimals: true,
        compactThreshold: 10000,
        preferredFormat: 'symbol-after',
      };
  }
}
