/**
 * Manual test runner for profile completion functionality
 * This can be used to test the implementation without a full test framework
 */

import { calculateProfileCompletion } from './profile-completion';
import { ProfileCompletionData } from '../types/profile-completion';

// Test data
const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  role: 'CLIENT' as const,
  isEmailVerified: true,
  isPhoneVerified: true,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
};

const testCases = [
  {
    name: 'Empty Provider (0% completion)',
    data: {
      user: mockUser,
      provider: {
        id: 1,
        userId: 'user-123',
        isSetupComplete: false,
        isVerified: false,
        averageRating: 0,
        totalReviews: 0,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        providingPlaces: [],
        services: [],
        queues: [],
      },
    },
  },
  {
    name: 'Partial Provider Info (30% completion)',
    data: {
      user: mockUser,
      provider: {
        id: 1,
        userId: 'user-123',
        title: 'Test Business',
        phone: '+123456789',
        presentation: 'Test business description',
        providerCategoryId: 1,
        isSetupComplete: false,
        isVerified: false,
        averageRating: 0,
        totalReviews: 0,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        providingPlaces: [],
        services: [],
        queues: [],
      },
    },
  },
  {
    name: 'Complete Provider (100% completion)',
    data: {
      user: mockUser,
      provider: {
        id: 1,
        userId: 'user-123',
        title: 'Test Business',
        phone: '+123456789',
        presentation: 'Test business description',
        providerCategoryId: 1,
        logoId: 'logo-123',
        isSetupComplete: false,
        isVerified: false,
        averageRating: 4.5,
        totalReviews: 10,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        logo: {
          id: 'logo-123',
          filename: 'logo.png',
          originalName: 'business-logo.png',
          mimeType: 'image/png',
          size: 1024,
          url: 'https://example.com/logo.png',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        providingPlaces: [
          {
            id: 1,
            sProviderId: 1,
            name: 'Main Location',
            address: '123 Test Street',
            city: 'Test City',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        ],
        services: [
          {
            id: 1,
            sProviderId: 1,
            title: 'Test Service',
            duration: 30,
            price: 50,
            pointsRequirements: 1,
            isPublic: true,
            deliveryType: 'at_location' as const,
            servedRegions: ['Test City'],
            color: '#3B82F6',
            acceptOnline: true,
            acceptNew: true,
            notificationOn: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        ],
        queues: [
          {
            id: 1,
            sProviderId: 1,
            sProvidingPlaceId: 1,
            title: 'Test Queue',
            isActive: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        ],
      },
    },
  },
];

/**
 * Run manual tests
 */
export function runProfileCompletionTests() {
  console.log('🧪 Running Profile Completion Tests...\n');

  testCases.forEach((testCase, index) => {
    console.log(`Test ${index + 1}: ${testCase.name}`);
    console.log('=' .repeat(50));

    try {
      const result = calculateProfileCompletion(testCase.data as ProfileCompletionData);
      
      console.log(`Overall Percentage: ${result.overallPercentage}%`);
      console.log(`Overall Completed: ${result.overallCompleted}`);
      console.log(`Should Mark Complete: ${result.shouldMarkAsComplete}`);
      
      console.log('\nBreakdown:');
      Object.entries(result.breakdown).forEach(([section, data]) => {
        console.log(`  ${section}: ${data.percentage}% (${data.completed ? '✅' : '❌'}) - ${data.details}`);
      });
      
      if (result.nextSteps.length > 0) {
        console.log('\nNext Steps:');
        result.nextSteps.forEach(step => console.log(`  • ${step}`));
      }
      
      if (result.criticalMissing.length > 0) {
        console.log('\nCritical Missing:');
        result.criticalMissing.forEach(item => console.log(`  ⚠️ ${item}`));
      }
      
      console.log('\n✅ Test passed\n');
    } catch (error) {
      console.error('❌ Test failed:', error);
      console.log('');
    }
  });

  console.log('🎉 All tests completed!');
}

/**
 * Test specific scenarios
 */
export function testSpecificScenarios() {
  console.log('🔍 Testing Specific Scenarios...\n');

  // Test weighted calculation
  const partialData = {
    user: mockUser,
    provider: {
      ...testCases[0].data.provider,
      title: 'Test',
      phone: '+123',
      presentation: 'Desc',
      providerCategoryId: 1,
      logoId: 'logo',
    },
  };

  const result = calculateProfileCompletion(partialData as ProfileCompletionData);
  const expectedPercentage = 30 + 10; // Provider info (30%) + Logo (10%)
  
  console.log(`Weighted Calculation Test:`);
  console.log(`Expected: ${expectedPercentage}%, Got: ${result.overallPercentage}%`);
  console.log(result.overallPercentage === expectedPercentage ? '✅ Passed' : '❌ Failed');
  console.log('');

  // Test 80% threshold
  const almostCompleteData = {
    user: mockUser,
    provider: {
      ...testCases[2].data.provider,
      queues: [], // Remove queues (15%) to get 85%
    },
  };

  const almostResult = calculateProfileCompletion(almostCompleteData as ProfileCompletionData);
  console.log(`80% Threshold Test:`);
  console.log(`Percentage: ${almostResult.overallPercentage}%`);
  console.log(`Should be completed: ${almostResult.overallCompleted}`);
  console.log(`Should mark as complete: ${almostResult.shouldMarkAsComplete}`);
  console.log(almostResult.overallCompleted && !almostResult.shouldMarkAsComplete ? '✅ Passed' : '❌ Failed');
}

// Export for use in browser console or Node.js
if (typeof window !== 'undefined') {
  // Browser environment
  (window as any).testProfileCompletion = {
    runTests: runProfileCompletionTests,
    testScenarios: testSpecificScenarios,
  };
  console.log('Profile completion tests available at window.testProfileCompletion');
}
