import { TFunction } from 'i18next';

/**
 * Translation utility functions
 */

// Helper to create translation keys with namespace
export const createTranslationKey = (namespace: string, key: string): string => {
  return `${namespace}:${key}`;
};

// Helper to get nested translation keys
export const getNestedKey = (parent: string, child: string): string => {
  return `${parent}.${child}`;
};

// Helper for pluralization
export const getPluralKey = (key: string, count: number): string => {
  return count === 1 ? key : `${key}_plural`;
};

// Helper for conditional translation based on context
export const getContextualTranslation = (
  t: TFunction,
  baseKey: string,
  context?: string,
  fallback?: string
): string => {
  const contextKey = context ? `${baseKey}_${context}` : baseKey;
  const translation = t(contextKey);
  
  // If translation is the same as key (not found), try fallback
  if (translation === contextKey && fallback) {
    return t(fallback);
  }
  
  return translation;
};

// Helper for interpolated translations
export const getInterpolatedTranslation = (
  t: TFunction,
  key: string,
  values: Record<string, any>
): string => {
  return t(key, values);
};

// Helper for date/time formatting with locale
export const formatDateTime = (
  date: Date | string,
  locale: string,
  options?: Intl.DateTimeFormatOptions
): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  };
  
  return new Intl.DateTimeFormat(locale, { ...defaultOptions, ...options }).format(dateObj);
};

// Helper for number formatting with locale
export const formatNumber = (
  number: number,
  locale: string,
  options?: Intl.NumberFormatOptions
): string => {
  return new Intl.NumberFormat(locale, options).format(number);
};

// Helper for currency formatting
export const formatCurrency = (
  amount: number,
  locale: string,
  currency: string = 'USD'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount);
};

// Helper for relative time formatting
export const formatRelativeTime = (
  date: Date | string,
  locale: string
): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
  
  const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
  
  if (diffInSeconds < 60) {
    return rtf.format(-diffInSeconds, 'second');
  } else if (diffInSeconds < 3600) {
    return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
  } else if (diffInSeconds < 86400) {
    return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
  } else if (diffInSeconds < 2592000) {
    return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
  } else if (diffInSeconds < 31536000) {
    return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
  } else {
    return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
  }
};

// Helper for list formatting
export const formatList = (
  items: string[],
  locale: string,
  type: 'conjunction' | 'disjunction' = 'conjunction'
): string => {
  const listFormatter = new Intl.ListFormat(locale, { 
    style: 'long', 
    type 
  });
  return listFormatter.format(items);
};

// Helper to validate translation keys exist
export const validateTranslationKey = (
  t: TFunction,
  key: string,
  namespace?: string
): boolean => {
  const fullKey = namespace ? `${namespace}:${key}` : key;
  const translation = t(fullKey);
  return translation !== fullKey;
};

// Helper to get all available translations for a key across languages
export const getTranslationsForKey = (
  key: string,
  languages: string[],
  i18n: any
): Record<string, string> => {
  const translations: Record<string, string> = {};
  
  languages.forEach(lang => {
    const translation = i18n.getResource(lang, 'common', key);
    if (translation) {
      translations[lang] = translation;
    }
  });
  
  return translations;
};

// Helper for RTL-aware text direction
export const getTextDirection = (isRTL: boolean): 'ltr' | 'rtl' => {
  return isRTL ? 'rtl' : 'ltr';
};

// Helper for RTL-aware alignment
export const getTextAlignment = (
  alignment: 'start' | 'end' | 'center',
  isRTL: boolean
): 'left' | 'right' | 'center' => {
  if (alignment === 'center') return 'center';
  if (alignment === 'start') return isRTL ? 'right' : 'left';
  if (alignment === 'end') return isRTL ? 'left' : 'right';
  return 'left';
};

// Helper to generate translation key suggestions
export const generateTranslationKeySuggestions = (text: string): string[] => {
  const suggestions: string[] = [];
  
  // Convert to camelCase
  const camelCase = text
    .toLowerCase()
    .replace(/[^a-zA-Z0-9\s]/g, '')
    .replace(/\s+(.)/g, (_, char) => char.toUpperCase());
  
  // Convert to snake_case
  const snakeCase = text
    .toLowerCase()
    .replace(/[^a-zA-Z0-9\s]/g, '')
    .replace(/\s+/g, '_');
  
  // Convert to kebab-case
  const kebabCase = text
    .toLowerCase()
    .replace(/[^a-zA-Z0-9\s]/g, '')
    .replace(/\s+/g, '-');
  
  suggestions.push(camelCase, snakeCase, kebabCase);
  
  return suggestions.filter(Boolean);
};

export default {
  createTranslationKey,
  getNestedKey,
  getPluralKey,
  getContextualTranslation,
  getInterpolatedTranslation,
  formatDateTime,
  formatNumber,
  formatCurrency,
  formatRelativeTime,
  formatList,
  validateTranslationKey,
  getTranslationsForKey,
  getTextDirection,
  getTextAlignment,
  generateTranslationKeySuggestions,
};
