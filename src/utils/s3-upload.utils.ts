/**
 * S3 Upload Utilities
 * Handles the two-phase upload workflow using AWS S3 pre-signed URLs
 */

export interface UploadUrlResponse {
  uploadUrl: string;
  uploadFields: Record<string, string>;
  file: {
    id: string;
    name: string;
    type: string;
    key: string;
  };
  user?: {
    id: string;
    profilePictureId: string;
  };
  provider?: {
    id: string;
    logoId: string;
  };
}

export interface UploadProgressCallback {
  (progress: number): void;
}

export interface UploadOptions {
  file: File;
  onProgress?: UploadProgressCallback;
  validateFile?: boolean;
}

export interface S3UploadResult {
  success: boolean;
  fileData: UploadUrlResponse['file'];
  userData?: UploadUrlResponse['user'];
  providerData?: UploadUrlResponse['provider'];
}

/**
 * Validates file for upload
 */
export const validateUploadFile = (file: File): { isValid: boolean; error?: string } => {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/png'];
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Only JPEG and PNG images are allowed'
    };
  }

  // Check file size (10MB limit)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'File size must be less than 10MB'
    };
  }

  return { isValid: true };
};

/**
 * Uploads file to S3 using pre-signed URL
 */
export const uploadToS3 = async (
  uploadUrl: string,
  uploadFields: Record<string, string>,
  file: File,
  onProgress?: UploadProgressCallback
): Promise<void> => {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    
    // Add all upload fields first
    Object.entries(uploadFields).forEach(([key, value]) => {
      formData.append(key, value);
    });
    
    // Add the file last (required by S3)
    formData.append('file', file);

    const xhr = new XMLHttpRequest();

    // Track upload progress
    if (onProgress) {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          onProgress(progress);
        }
      });
    }

    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        resolve();
      } else {
        reject(new Error(`Upload failed with status ${xhr.status}: ${xhr.statusText}`));
      }
    });

    xhr.addEventListener('error', () => {
      reject(new Error('Upload failed due to network error'));
    });

    xhr.addEventListener('abort', () => {
      reject(new Error('Upload was aborted'));
    });

    xhr.open('POST', uploadUrl);
    xhr.send(formData);
  });
};

/**
 * Complete two-phase upload workflow
 */
export const performS3Upload = async (
  generateUrlFn: (fileName: string, fileType: string) => Promise<UploadUrlResponse>,
  options: UploadOptions
): Promise<S3UploadResult> => {
  const { file, onProgress, validateFile = true } = options;

  // Validate file if requested
  if (validateFile) {
    const validation = validateUploadFile(file);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }
  }

  try {
    // Phase 1: Generate pre-signed URL
    onProgress?.(0);
    const urlResponse = await generateUrlFn(file.name, file.type);

    // Phase 2: Upload to S3
    await uploadToS3(
      urlResponse.uploadUrl,
      urlResponse.uploadFields,
      file,
      (progress) => {
        // Map S3 upload progress to 10-100% range
        // (0-10% was for URL generation)
        const mappedProgress = 10 + (progress * 0.9);
        onProgress?.(Math.round(mappedProgress));
      }
    );

    onProgress?.(100);

    return {
      success: true,
      fileData: urlResponse.file,
      userData: urlResponse.user,
      providerData: urlResponse.provider,
    };
  } catch (error) {
    onProgress?.(0);
    throw error;
  }
};

/**
 * Utility to create a file preview URL
 */
export const createFilePreview = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
