import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import PageMeta from '../../components/common/PageMeta';
import AuthLayout from './AuthPageLayout';
import OtpInput from '../../components/auth/OtpInput';
import Button from '../../components/ui/button/Button';
import Label from '../../components/form/Label';
import Input from '../../components/form/input/InputField';
import { ErrorDisplay } from '../../components/error';
import { ChevronLeftIcon, ChevronRightIcon, EyeCloseIcon, EyeIcon } from '../../icons';
import {
  usePasswordResetRequest,
  usePasswordResetVerify,
  usePasswordReset,
} from '../../hooks/useAuthMutations';
import { useAuthTranslation, useCommonTranslation } from '../../hooks/useTranslation';

// Step 1: Request reset
const requestSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

// Step 2: Verify OTP
const verifySchema = z.object({
  otp: z.string().min(6, 'OTP must be 6 digits').max(6, 'OTP must be 6 digits'),
});

// Step 3: Reset password
const resetSchema = z.object({
  newPassword: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type RequestFormData = z.infer<typeof requestSchema>;
type VerifyFormData = z.infer<typeof verifySchema>;
type ResetFormData = z.infer<typeof resetSchema>;

type Step = 'request' | 'verify' | 'reset';

export default function ResetPassword() {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState<Step>('request');
  const [email, setEmail] = useState('');
  const [resetToken, setResetToken] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { t } = useAuthTranslation();
  const { currentLanguage, t: tCommon } = useCommonTranslation();

  const requestMutation = usePasswordResetRequest();
  const verifyMutation = usePasswordResetVerify();
  const resetMutation = usePasswordReset();

  // Step 1: Request reset form
  const requestForm = useForm<RequestFormData>({
    resolver: zodResolver(requestSchema),
  });

  // Step 2: Verify OTP form
  const verifyForm = useForm<VerifyFormData>({
    resolver: zodResolver(verifySchema),
  });

  // Step 3: Reset password form
  const resetForm = useForm<ResetFormData>({
    resolver: zodResolver(resetSchema),
  });

  const handleRequestSubmit = async (data: RequestFormData) => {
    try {
      await requestMutation.mutateAsync({ email: data.email });
      setEmail(data.email);
      setCurrentStep('verify');
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleVerifySubmit = async (data: VerifyFormData) => {
    try {
      const result = await verifyMutation.mutateAsync({
        email,
        otp: data.otp,
      });
      setResetToken(result.resetToken);
      setCurrentStep('reset');
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleResetSubmit = async (data: ResetFormData) => {
    try {
      await resetMutation.mutateAsync({
        resetToken,
        newPassword: data.newPassword,
      });
      navigate('/signin', {
        state: { message: 'Password reset successfully. Please sign in with your new password.' },
      });
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleOtpChange = (value: string) => {
    verifyForm.setValue('otp', value);
  };

  const isLoading = requestMutation.isPending || verifyMutation.isPending || resetMutation.isPending;

  const renderStepContent = () => {
    switch (currentStep) {
      case 'request':
        return (
          <div>
            <div className="mb-6 text-center">
              <h1 className="mb-2 font-semibold text-gray-800 text-title-sm dark:text-white/90 sm:text-title-md">
                {t('resetPassword.title')}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('resetPassword.subtitle')}
              </p>
            </div>

            <form onSubmit={requestForm.handleSubmit(handleRequestSubmit)} className="space-y-6">
              <div>
                <Label>
                  {t('resetPassword.email')} <span className="text-red-500">*</span>
                </Label>
                <Input
                  {...requestForm.register('email')}
                  type="email"
                  placeholder={t('resetPassword.emailPlaceholder')}
                  disabled={isLoading}
                />
                {requestForm.formState.errors.email && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {requestForm.formState.errors.email.message}
                  </p>
                )}
              </div>

              {requestMutation.error && (
                <ErrorDisplay
                  error={requestMutation.error}
                  variant="banner"
                  size="sm"
                />
              )}

              <Button
                type="submit"
                className="w-full"
                size="sm"
                disabled={isLoading}
              >
                {isLoading ? t('resetPassword.sending') || 'Sending...' : t('resetPassword.sendCode')}
              </Button>
            </form>
          </div>
        );

      case 'verify':
        return (
          <div>
            <div className="mb-6 text-center">
              <h1 className="mb-2 font-semibold text-gray-800 text-title-sm dark:text-white/90 sm:text-title-md">
                {t('resetPassword.verifyTitle') || 'Verify Reset Code'}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('resetPassword.verifySubtitle') || 'Enter the 6-digit code sent to'}{' '}
                <span className="font-medium text-gray-700 dark:text-gray-300">
                  {email}
                </span>
              </p>
            </div>

            <form onSubmit={verifyForm.handleSubmit(handleVerifySubmit)} className="space-y-6">
              <div>
                <label className="block mb-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t('resetPassword.verificationCode') || 'Verification Code'}
                </label>
                <OtpInput
                  value={verifyForm.watch('otp') || ''}
                  onChange={handleOtpChange}
                  error={!!verifyForm.formState.errors.otp}
                  disabled={isLoading}
                  autoFocus
                />
                {verifyForm.formState.errors.otp && (
                  <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                    {verifyForm.formState.errors.otp.message}
                  </p>
                )}
              </div>

              {verifyMutation.error && (
                <ErrorDisplay
                  error={verifyMutation.error}
                  variant="banner"
                  size="sm"
                />
              )}

              <div className="flex space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  className="flex-1"
                  onClick={() => setCurrentStep('request')}
                  disabled={isLoading}
                >
                  {tCommon('actions.back')}
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                  size="sm"
                  disabled={isLoading || (verifyForm.watch('otp') || '').length !== 6}
                >
                  {isLoading ? t('resetPassword.verifying') || 'Verifying...' : t('resetPassword.verifyCodeButton') || 'Verify Code'}
                </Button>
              </div>
            </form>
          </div>
        );

      case 'reset':
        return (
          <div>
            <div className="mb-6 text-center">
              <h1 className="mb-2 font-semibold text-gray-800 text-title-sm dark:text-white/90 sm:text-title-md">
                {t('resetPassword.setNewPasswordTitle') || 'Set New Password'}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('resetPassword.setNewPasswordSubtitle') || 'Enter your new password below'}
              </p>
            </div>

            <form onSubmit={resetForm.handleSubmit(handleResetSubmit)} className="space-y-6">
              <div>
                <Label>
                  {t('resetPassword.newPassword') || 'New Password'} <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    {...resetForm.register('newPassword')}
                    type={showPassword ? 'text' : 'password'}
                    placeholder={t('resetPassword.newPasswordPlaceholder')}
                    disabled={isLoading}
                    className={currentLanguage === 'ar' ? 'pl-10' : 'pr-10'}
                  />
                  <span
                    onClick={() => setShowPassword(!showPassword)}
                    className={`absolute z-30 -translate-y-1/2 cursor-pointer top-1/2 ${
                      currentLanguage === 'ar' ? 'left-4' : 'right-4'
                    }`}
                  >
                    {showPassword ? (
                      <EyeIcon className="fill-gray-500 dark:fill-gray-400 size-5" />
                    ) : (
                      <EyeCloseIcon className="fill-gray-500 dark:fill-gray-400 size-5" />
                    )}
                  </span>
                </div>
                {resetForm.formState.errors.newPassword && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {resetForm.formState.errors.newPassword.message}
                  </p>
                )}
              </div>

              <div>
                <Label>
                  {t('resetPassword.confirmPassword') || 'Confirm Password'} <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    {...resetForm.register('confirmPassword')}
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder={t('resetPassword.confirmPasswordPlaceholder')}
                    disabled={isLoading}
                    className={currentLanguage === 'ar' ? 'pl-10' : 'pr-10'}
                  />
                  <span
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className={`absolute z-30 -translate-y-1/2 cursor-pointer top-1/2 ${
                      currentLanguage === 'ar' ? 'left-4' : 'right-4'
                    }`}
                  >
                    {showConfirmPassword ? (
                      <EyeIcon className="fill-gray-500 dark:fill-gray-400 size-5" />
                    ) : (
                      <EyeCloseIcon className="fill-gray-500 dark:fill-gray-400 size-5" />
                    )}
                  </span>
                </div>
                {resetForm.formState.errors.confirmPassword && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {resetForm.formState.errors.confirmPassword.message}
                  </p>
                )}
              </div>

              {resetMutation.error && (
                <ErrorDisplay
                  error={resetMutation.error}
                  variant="banner"
                  size="sm"
                />
              )}

              <Button
                type="submit"
                className="w-full"
                size="sm"
                disabled={isLoading}
              >
                {isLoading ? t('resetPassword.resetting') || 'Resetting...' : t('resetPassword.resetButton')}
              </Button>
            </form>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <PageMeta
        title={`${t('resetPassword.title')} | Dalti Provider Dashboard`}
        description="Reset your provider account password"
      />
      <AuthLayout>
        <div className="flex flex-col flex-1 w-full overflow-y-auto lg:w-1/2 no-scrollbar">
          <div className="w-full max-w-md mx-auto mb-5 sm:pt-10">
            <Link
              to="/signin"
              className="inline-flex items-center text-sm text-gray-500 transition-colors hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              {currentLanguage === 'ar' ? (
                <ChevronRightIcon className="size-5" />
              ) : (
                <ChevronLeftIcon className="size-5" />
              )}
              {t('resetPassword.backToSignIn')}
            </Link>
          </div>
          <div className="flex flex-col justify-center flex-1 w-full max-w-md mx-auto">
            <div>
              {renderStepContent()}

              <div className="mt-5">
                <p className="text-sm font-normal text-center text-gray-700 dark:text-gray-400 sm:text-start">
                  {t('resetPassword.rememberPassword') || 'Remember your password?'}{' '}
                  <Link
                    to="/signin"
                    className="text-brand-500 hover:text-brand-600 dark:text-brand-400"
                  >
                    {t('signIn.title')}
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </AuthLayout>
    </>
  );
}
