import React from 'react';
import { useTranslation, useCommonTranslation, useAuthTranslation } from '../hooks/useTranslation';
import { useLanguage } from '../context/LanguageContext';
import LanguageSwitcher from '../components/common/LanguageSwitcher';
import PageMeta from '../components/common/PageMeta';

const TestI18n: React.FC = () => {
  const { t: tCommon } = useCommonTranslation();
  const { t: tAuth } = useAuthTranslation();
  const { currentLanguage, direction, isRTL, languageInfo } = useLanguage();

  return (
    <div className={`p-6 space-y-6 ${isRTL ? 'text-right' : 'text-left'}`} dir={direction}>
      <PageMeta
        title="Internationalization Test - Dalti Provider Dashboard"
        description="Testing multi-language support and RTL functionality"
      />
      
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Internationalization Test
        </h1>
        
        {/* Language Info */}
        <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Current Language Info</h2>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Language:</strong> {languageInfo.name} ({languageInfo.nativeName})
            </div>
            <div>
              <strong>Code:</strong> {currentLanguage}
            </div>
            <div>
              <strong>Direction:</strong> {direction}
            </div>
            <div>
              <strong>Is RTL:</strong> {isRTL ? 'Yes' : 'No'}
            </div>
          </div>
        </div>

        {/* Language Switcher */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">Language Switcher</h2>
          <LanguageSwitcher variant="standalone" />
        </div>

        {/* Common Translations */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">Common Translations</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
              <strong>Navigation:</strong>
              <ul className="mt-1 space-y-1">
                <li>{tCommon('navigation.dashboard')}</li>
                <li>{tCommon('navigation.calendar')}</li>
                <li>{tCommon('navigation.appointments')}</li>
                <li>{tCommon('navigation.customers')}</li>
              </ul>
            </div>
            <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded">
              <strong>Actions:</strong>
              <ul className="mt-1 space-y-1">
                <li>{tCommon('actions.save')}</li>
                <li>{tCommon('actions.cancel')}</li>
                <li>{tCommon('actions.delete')}</li>
                <li>{tCommon('actions.edit')}</li>
              </ul>
            </div>
            <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded">
              <strong>Status:</strong>
              <ul className="mt-1 space-y-1">
                <li>{tCommon('status.active')}</li>
                <li>{tCommon('status.pending')}</li>
                <li>{tCommon('status.completed')}</li>
                <li>{tCommon('status.cancelled')}</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Auth Translations */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">Auth Translations</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded">
              <strong>Sign In:</strong>
              <ul className="mt-1 space-y-1">
                <li>{tAuth('signIn.title')}</li>
                <li>{tAuth('signIn.email')}</li>
                <li>{tAuth('signIn.password')}</li>
                <li>{tAuth('signIn.signInButton')}</li>
              </ul>
            </div>
            <div className="p-3 bg-pink-50 dark:bg-pink-900/20 rounded">
              <strong>Sign Up:</strong>
              <ul className="mt-1 space-y-1">
                <li>{tAuth('signUp.title')}</li>
                <li>{tAuth('signUp.firstName')}</li>
                <li>{tAuth('signUp.lastName')}</li>
                <li>{tAuth('signUp.signUpButton')}</li>
              </ul>
            </div>
          </div>
        </div>

        {/* RTL Test */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">RTL Layout Test</h2>
          <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span>Start aligned text</span>
              <span>End aligned text</span>
            </div>
            <div className="flex items-center gap-4">
              <button className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                {tCommon('actions.save')}
              </button>
              <button className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                {tCommon('actions.cancel')}
              </button>
            </div>
          </div>
        </div>

        {/* Messages Test */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">Messages</h2>
          <div className="space-y-2">
            <div className="p-3 bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 rounded">
              {tCommon('messages.success')}: {tCommon('messages.operationSuccessful')}
            </div>
            <div className="p-3 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 rounded">
              {tCommon('messages.warning')}: {tCommon('messages.pleaseWait')}
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded">
              {tCommon('messages.info')}: {tCommon('messages.loading')}
            </div>
          </div>
        </div>

        {/* Time Test */}
        <div>
          <h2 className="text-lg font-semibold mb-2">Time Expressions</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
            <span className="p-2 bg-gray-100 dark:bg-gray-700 rounded">{tCommon('time.today')}</span>
            <span className="p-2 bg-gray-100 dark:bg-gray-700 rounded">{tCommon('time.yesterday')}</span>
            <span className="p-2 bg-gray-100 dark:bg-gray-700 rounded">{tCommon('time.tomorrow')}</span>
            <span className="p-2 bg-gray-100 dark:bg-gray-700 rounded">{tCommon('time.thisWeek')}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestI18n;
