import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';
import { useAppointment, useAppointments, useUpdateAppointmentStatus } from '../../hooks/useAppointments';
import { Appointment } from '../../types';
import Button from '../../components/ui/button/Button';
import PageMeta from '../../components/common/PageMeta';
import { formatLocalTime, formatLocalDate } from '../../utils/timezone';
import { useCommonTranslation, useManagementTranslation } from '../../hooks/useTranslation';
import { useLanguage } from '../../context/LanguageContext';
import toast from 'react-hot-toast';

interface ServiceSessionProps {}

const ServiceSession: React.FC<ServiceSessionProps> = () => {
  const { appointmentId } = useParams<{ appointmentId: string }>();
  const navigate = useNavigate();
  const { t: tCommon, currentLanguage } = useCommonTranslation();
  const { t } = useManagementTranslation();
  const { isRTL } = useLanguage();
  
  const { data: appointment, isLoading, error } = useAppointment(Number(appointmentId));
  // Get all appointments and filter client-side for better debugging
  const { data: allAppointments, isLoading: isLoadingAppointments } = useAppointments();

  console.log('All appointments loading:', isLoadingAppointments);
  console.log('All appointments data:', allAppointments);
  const updateStatusMutation = useUpdateAppointmentStatus();

  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [isTimerActive, setIsTimerActive] = useState(false);
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null);
  const [extendMinutes, setExtendMinutes] = useState<number>(15);

  // Initialize timer when appointment loads
  useEffect(() => {
    if (appointment && appointment.status === 'InProgress') {
      const duration = appointment.serviceDuration || appointment.service?.duration || 30;
      const startTime = appointment.realAppointmentStartTime 
        ? new Date(appointment.realAppointmentStartTime)
        : new Date();
      
      setSessionStartTime(startTime);
      const endTime = new Date(startTime.getTime() + duration * 60000);
      const remaining = Math.max(0, endTime.getTime() - Date.now());
      setTimeRemaining(Math.floor(remaining / 1000));
      setIsTimerActive(remaining > 0);
    }
  }, [appointment]);

  // Timer countdown effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isTimerActive && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            setIsTimerActive(false);
            toast.info('Session time has ended!');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isTimerActive, timeRemaining]);

  // Get next appointments
  const nextAppointments = React.useMemo(() => {
    if (!allAppointments || !appointment) {
      console.log('❌ No appointments data or current appointment:', {
        allAppointments: allAppointments?.length || 0,
        appointment: !!appointment
      });
      return [];
    }

    const currentTime = new Date();
    console.log('📊 Processing appointments:', {
      totalAppointments: allAppointments.length,
      currentAppointmentId: appointment.id,
      currentTime: currentTime.toISOString()
    });

    // Log all appointments for debugging
    allAppointments.forEach(apt => {
      console.log(`📅 Appointment ${apt.id}:`, {
        status: apt.status,
        startTime: apt.expectedAppointmentStartTime,
        customer: `${apt.customer?.firstName || 'Unknown'} ${apt.customer?.lastName || 'Customer'}`,
        service: apt.service?.title || 'Unknown Service'
      });
    });

    const filtered = allAppointments
      .filter(apt => {
        const isNotCurrent = apt.id !== appointment.id;
        const appointmentTime = new Date(apt.expectedAppointmentStartTime);
        const isFuture = appointmentTime > currentTime;
        const hasValidStatus = ['confirmed', 'pending'].includes(apt.status);

        const passes = isNotCurrent && isFuture && hasValidStatus;

        console.log(`🔍 Filtering appointment ${apt.id}:`, {
          isNotCurrent,
          isFuture: `${isFuture} (${appointmentTime.toISOString()} vs ${currentTime.toISOString()})`,
          hasValidStatus: `${hasValidStatus} (status: ${apt.status})`,
          passes,
          customer: `${apt.customer?.firstName} ${apt.customer?.lastName}`,
          service: apt.service?.title
        });

        return passes;
      })
      .sort((a, b) =>
        new Date(a.expectedAppointmentStartTime).getTime() -
        new Date(b.expectedAppointmentStartTime).getTime()
      )
      .slice(0, 5);

    console.log('✅ Final filtered next appointments:', filtered.length);
    filtered.forEach(apt => {
      console.log(`  - ${apt.customer?.firstName} ${apt.customer?.lastName} at ${apt.expectedAppointmentStartTime}`);
    });

    return filtered;
  }, [allAppointments, appointment]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleCompleteSession = async () => {
    if (!appointment) return;

    try {
      await updateStatusMutation.mutateAsync({
        id: appointment.id,
        status: {
          status: 'completed',
          notes: t('serviceSession.completedFromSessionPage', 'Session completed from service session page')
        }
      });
      toast.success(t('serviceSession.sessionCompletedSuccessfully', 'Session completed successfully!'));
      navigate('/appointments');
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleExtendTimer = () => {
    const additionalSeconds = extendMinutes * 60;
    setTimeRemaining(prev => prev + additionalSeconds);
    setIsTimerActive(true);
    toast.success(t('serviceSession.timerExtended', 'Timer extended by {{minutes}} minutes', { minutes: extendMinutes }));
  };

  const handleEndSession = () => {
    navigate('/appointments');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-600"></div>
      </div>
    );
  }

  if (error || !appointment) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {t('serviceSession.appointmentNotFound', 'Appointment Not Found')}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {t('serviceSession.couldNotLoad', 'The appointment session could not be loaded.')}
          </p>
          <Button onClick={() => navigate('/appointments')}>
            {tCommon('navigation.backToAppointments', 'Back to Appointments')}
          </Button>
        </div>
      </div>
    );
  }

  if (appointment.status !== 'InProgress') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {t('serviceSession.sessionNotActive', 'Session Not Active')}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {t('serviceSession.notInProgress', 'This appointment is not currently in progress.')}
          </p>
          <Button onClick={() => navigate('/appointments')}>
            {tCommon('navigation.backToAppointments', 'Back to Appointments')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <>
      <PageMeta
        title={t('serviceSession.title', 'Service Session')}
        description={t('serviceSession.description', 'Active service session management')}
      />

      <div className="p-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {t('serviceSession.title', 'Service Session')}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {appointment.service?.title} - {appointment.customer?.firstName} {appointment.customer?.lastName}
                </p>
              </div>
              <Button
                variant="outline"
                onClick={handleEndSession}
                className="text-gray-600 border-gray-300"
              >
                {tCommon('navigation.backToAppointments', 'Back to Appointments')}
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Timer Section */}
            <div className="lg:col-span-2">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 text-center">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {t('serviceSession.sessionTimer', 'Session Timer')}
                </h2>
                
                <div className={`text-6xl font-mono font-bold mb-6 ${
                  timeRemaining <= 300 ? 'text-red-600' : 
                  timeRemaining <= 600 ? 'text-yellow-600' : 
                  'text-green-600'
                }`}>
                  {formatTime(timeRemaining)}
                </div>

                <div className="flex items-center justify-center gap-4 mb-6">
                  <select
                    value={extendMinutes}
                    onChange={(e) => setExtendMinutes(Number(e.target.value))}
                    className="rounded-lg border border-gray-300 bg-white dark:bg-gray-700 px-3 py-2 text-sm"
                  >
                    <option value={5}>{t('serviceSession.extendOptions.5minutes', '5 minutes')}</option>
                    <option value={10}>{t('serviceSession.extendOptions.10minutes', '10 minutes')}</option>
                    <option value={15}>{t('serviceSession.extendOptions.15minutes', '15 minutes')}</option>
                    <option value={30}>{t('serviceSession.extendOptions.30minutes', '30 minutes')}</option>
                  </select>
                  <Button
                    onClick={handleExtendTimer}
                    variant="outline"
                    size="sm"
                  >
                    {t('serviceSession.extendTimer', 'Extend Timer')}
                  </Button>
                </div>

                <Button
                  onClick={handleCompleteSession}
                  disabled={updateStatusMutation.isPending}
                  className="bg-green-600 hover:bg-green-700 text-white px-8 py-3"
                >
                  {updateStatusMutation.isPending ? t('serviceSession.completing', 'Completing...') : t('serviceSession.completeSession', 'Complete Session')}
                </Button>
              </div>
            </div>

            {/* Customer Details */}
            <div className="space-y-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {t('serviceSession.currentCustomer', 'Current Customer')}
                </h3>
                
                <div className="space-y-3">
                  <div className="flex items-center">
                    <div className={`w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-lg ${isRTL ? 'ml-3' : 'mr-3'}`}>
                      {appointment.customer?.firstName?.charAt(0)}{appointment.customer?.lastName?.charAt(0)}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {appointment.customer?.firstName} {appointment.customer?.lastName}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {appointment.customer?.email}
                      </p>
                    </div>
                  </div>
                  
                  {appointment.customer?.phone && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">{tCommon('common.phone', 'Phone')}:</span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {appointment.customer.phone}
                      </span>
                    </div>
                  )}
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">{tCommon('common.service', 'Service')}:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {appointment.service?.title}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">{tCommon('common.duration', 'Duration')}:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {currentLanguage === 'ar'
                        ? `${appointment.service?.duration || appointment.serviceDuration} دقيقة`
                        : `${appointment.service?.duration || appointment.serviceDuration} ${tCommon('common.minutes', 'minutes')}`
                      }
                    </span>
                  </div>

                  {appointment.notes && (
                    <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">{tCommon('common.notes', 'Notes')}:</p>
                      <p className="text-sm text-gray-900 dark:text-white">
                        {appointment.notes}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Next Appointments */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {t('serviceSession.comingNext', 'Coming Next')} ({nextAppointments.length})
                </h3>
                
                {nextAppointments.length === 0 ? (
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {t('serviceSession.noUpcomingAppointments', 'No upcoming appointments')}
                  </p>
                ) : (
                  <div className="space-y-3">
                    {nextAppointments.map((apt) => (
                      <div key={apt.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {apt.customer?.firstName} {apt.customer?.lastName}
                          </p>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            {apt.service?.title}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {formatLocalTime(apt.expectedAppointmentStartTime)}
                          </p>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            {formatLocalDate(apt.expectedAppointmentStartTime, { 
                              month: 'short', 
                              day: 'numeric' 
                            })}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ServiceSession;
