import React, { useState } from 'react';
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import Button from "../../components/ui/button/Button";
import { Modal } from "../../components/ui/modal";
import { useModal } from "../../hooks/useModal";
import { ErrorDisplay } from "../../components/error";
import { useLocations, useDeleteLocation } from "../../hooks/useLocations";
import LocationForm from "../../components/locations/LocationForm";
import LocationCard from "../../components/locations/LocationCard";
import LocationHoursManager from "../../components/locations/LocationHoursManager";
import { Location, LocationFilters } from "../../types";
import { useManagementTranslation, useCommonTranslation } from "../../hooks/useTranslation";

// Separate component for location results to prevent full page re-render
interface LocationResultsProps {
  filters: LocationFilters;
  onEditLocation: (location: Location) => void;
  onDeleteLocation: (id: number) => void;
  onManageHours: (location: Location) => void;
  onCreateLocation: () => void;
  deleteLocationMutation: any;
}

const LocationResults: React.FC<LocationResultsProps> = React.memo(({
  filters,
  onEditLocation,
  onDeleteLocation,
  onManageHours,
  onCreateLocation,
  deleteLocationMutation
}) => {
  const { data: locations, isLoading, error } = useLocations(filters);
  const { t, currentLanguage } = useManagementTranslation();

  // Custom translations for locations page
  const locationTranslations = {
    ar: {
      noLocationsFound: "لم يتم العثور على مواقع",
      noLocationsFiltered: "لا توجد مواقع تطابق المرشحات الحالية. حاول تعديل معايير البحث.",
      getStarted: "ابدأ بإضافة موقع عملك الأول.",
      addFirstLocation: "إضافة موقعك الأول",
      failedToLoad: "فشل في تحميل المواقع"
    },
    en: {
      noLocationsFound: "No locations found",
      noLocationsFiltered: "No locations match your current filters. Try adjusting your search criteria.",
      getStarted: "Get started by adding your first business location.",
      addFirstLocation: "Add Your First Location",
      failedToLoad: "Failed to load locations"
    },
    fr: {
      noLocationsFound: "Aucun lieu trouvé",
      noLocationsFiltered: "Aucun lieu ne correspond à vos filtres actuels. Essayez d'ajuster vos critères de recherche.",
      getStarted: "Commencez par ajouter votre premier lieu d'affaires.",
      addFirstLocation: "Ajouter votre premier lieu",
      failedToLoad: "Échec du chargement des lieux"
    }
  };

  const currentLang = currentLanguage as keyof typeof locationTranslations;
  const lt = (key: keyof typeof locationTranslations.ar) =>
    locationTranslations[currentLang]?.[key] || locationTranslations.en[key] || key;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <ErrorDisplay
          error={error}
          title={lt('failedToLoad')}
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <>
      {/* Locations Grid */}
      {locations && locations.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {locations.map((location) => (
            <LocationCard
              key={location.id}
              location={location}
              onEdit={() => onEditLocation(location)}
              onDelete={() => onDeleteLocation(location.id)}
              onManageHours={() => onManageHours(location)}
              isDeleting={deleteLocationMutation.isPending}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {lt('noLocationsFound')}
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            {Object.keys(filters).length > 0
              ? lt('noLocationsFiltered')
              : lt('getStarted')
            }
          </p>
          {Object.keys(filters).length === 0 && (
            <Button onClick={onCreateLocation}>
              {lt('addFirstLocation')}
            </Button>
          )}
        </div>
      )}
    </>
  );
});

export default function LocationsManagement() {
  const [editingLocation, setEditingLocation] = useState<Location | null>(null);
  const [selectedLocationForHours, setSelectedLocationForHours] = useState<Location | null>(null);
  const [filters, setFilters] = useState<LocationFilters>({});
  const [modalType, setModalType] = useState<'location' | 'hours' | null>(null);
  const { isOpen, openModal, closeModal } = useModal();
  const { t, currentLanguage } = useManagementTranslation();
  const { t: tCommon } = useCommonTranslation();

  // Custom translations for filter labels and buttons
  const filterTranslations = {
    ar: {
      addLocation: "إضافة موقع",
      city: "المدينة",
      amenities: "المرافق",
      search: "البحث",
      cityPlaceholder: "تصفية حسب المدينة...",
      searchPlaceholder: "البحث في المواقع...",
      allLocations: "جميع المواقع",
      withParking: "مع موقف سيارات",
      withElevator: "مع مصعد",
      handicapAccessible: "مناسب لذوي الاحتياجات الخاصة"
    },
    en: {
      addLocation: "Add Location",
      city: "City",
      amenities: "Amenities",
      search: "Search",
      cityPlaceholder: "Filter by city...",
      searchPlaceholder: "Search locations...",
      allLocations: "All Locations",
      withParking: "With Parking",
      withElevator: "With Elevator",
      handicapAccessible: "Handicap Accessible"
    },
    fr: {
      addLocation: "Ajouter un lieu",
      city: "Ville",
      amenities: "Équipements",
      search: "Rechercher",
      cityPlaceholder: "Filtrer par ville...",
      searchPlaceholder: "Rechercher des lieux...",
      allLocations: "Tous les lieux",
      withParking: "Avec parking",
      withElevator: "Avec ascenseur",
      handicapAccessible: "Accessible aux handicapés"
    }
  };

  const currentLang = currentLanguage as keyof typeof filterTranslations;
  const ft = (key: keyof typeof filterTranslations.ar) =>
    filterTranslations[currentLang]?.[key] || filterTranslations.en[key] || key;

  // Remove useLocations hook from here - it's now in LocationResults component
  const deleteLocationMutation = useDeleteLocation();

  const handleCreateLocation = () => {
    setEditingLocation(null);
    setModalType('location');
    openModal();
  };

  const handleEditLocation = (location: Location) => {
    setEditingLocation(location);
    setModalType('location');
    openModal();
  };

  const handleDeleteLocation = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this location? This action cannot be undone.')) {
      try {
        await deleteLocationMutation.mutateAsync(id);
      } catch (error) {
        // Error handled by mutation
      }
    }
  };

  const handleManageHours = (location: Location) => {
    setSelectedLocationForHours(location);
    setModalType('hours');
    openModal();
  };

  const handleCloseModal = () => {
    setEditingLocation(null);
    setSelectedLocationForHours(null);
    setModalType(null);
    closeModal();
  };

  const handleSuccess = () => {
    handleCloseModal();
  };

  const handleFilterChange = (newFilters: Partial<LocationFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  return (
    <>
      <PageMeta
        title="Locations Management | Provider Dashboard"
        description="Manage your business locations, addresses, and operating hours"
      />
      <PageBreadcrumb pageTitle={tCommon('navigation.locations')} />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('locations.title')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {t('locations.description')}
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleCreateLocation}
              size="sm"
            >
              {ft('addLocation')}
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {ft('city')}
              </label>
              <input
                type="text"
                placeholder={ft('cityPlaceholder')}
                value={filters.city || ''}
                onChange={(e) => handleFilterChange({ city: e.target.value || undefined })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {ft('amenities')}
              </label>
              <select
                value={
                  filters.hasParking ? 'parking' :
                  filters.hasElevator ? 'elevator' :
                  filters.hasHandicapAccess ? 'handicap' : ''
                }
                onChange={(e) => {
                  const value = e.target.value;
                  handleFilterChange({
                    hasParking: value === 'parking' ? true : undefined,
                    hasElevator: value === 'elevator' ? true : undefined,
                    hasHandicapAccess: value === 'handicap' ? true : undefined,
                  });
                }}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">{ft('allLocations')}</option>
                <option value="parking">{ft('withParking')}</option>
                <option value="elevator">{ft('withElevator')}</option>
                <option value="handicap">{ft('handicapAccessible')}</option>
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {ft('search')}
              </label>
              <input
                type="text"
                placeholder={ft('searchPlaceholder')}
                value={filters.search || ''}
                onChange={(e) => handleFilterChange({ search: e.target.value || undefined })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-500"
              />
            </div>
          </div>
        </div>

        {/* Locations Grid - Now handled by LocationResults component */}
        <LocationResults
          filters={filters}
          onEditLocation={handleEditLocation}
          onDeleteLocation={handleDeleteLocation}
          onManageHours={handleManageHours}
          onCreateLocation={handleCreateLocation}
          deleteLocationMutation={deleteLocationMutation}
        />
      </div>

      {/* Location Modal */}
      <Modal
        isOpen={isOpen}
        onClose={handleCloseModal}
        className="max-w-[900px] p-0"
      >
        {modalType === 'location' ? (
          <LocationForm
            location={editingLocation}
            onClose={handleCloseModal}
            onSuccess={handleSuccess}
          />
        ) : modalType === 'hours' && selectedLocationForHours ? (
          <LocationHoursManager
            location={selectedLocationForHours}
            onClose={handleCloseModal}
          />
        ) : null}
      </Modal>
    </>
  );
}
