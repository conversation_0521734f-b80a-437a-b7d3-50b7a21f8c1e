import React, { useState } from 'react';
import Button from "../../../components/ui/button/Button";
import { Modal } from "../../../components/ui/modal";
import { useModal } from "../../../hooks/useModal";
import { ErrorDisplay } from "../../../components/error";
import ConfirmationDialog from "../../../components/ui/confirmation/ConfirmationDialog";
import { useConfirmation } from "../../../hooks/useConfirmation";
import { useLocations, useDeleteLocation } from "../../../hooks/useLocations";
import LocationForm from "../../../components/locations/LocationForm";
import LocationCard from "../../../components/locations/LocationCard";
import { Location, LocationFilters } from "../../../types";
import { useManagementTranslation, useCommonTranslation } from "../../../hooks/useTranslation";
import { useRTL } from "../../../context/LanguageContext";

// Separate component for location results
interface LocationResultsProps {
  filters: LocationFilters;
  onEditLocation: (location: Location) => void;
  onDeleteLocation: (id: number) => void;
  onCreateLocation: () => void;
  deleteLocationMutation: any;
}

const LocationResults: React.FC<LocationResultsProps> = React.memo(({
  filters,
  onEditLocation,
  onDeleteLocation,
  onCreateLocation,
  deleteLocationMutation
}) => {
  const { data: locations, isLoading, error } = useLocations(filters);
  const { t } = useManagementTranslation();
  const { isRTL, direction } = useRTL();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12" dir={direction}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-600"></div>
        <p className={`text-gray-600 dark:text-gray-400 ${isRTL ? 'mr-3' : 'ml-3'}`}>
          {t('locations.loadingLocations', 'Loading locations...')}
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div dir={direction}>
        <ErrorDisplay
          error={error}
          title={t('locations.failedToLoad', 'Failed to load locations')}
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  if (!locations || locations.length === 0) {
    return (
      <div className="text-center py-12" dir={direction}>
        <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {t('locations.noLocationsFound', 'No locations found')}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          {t('locations.getStarted', 'Get started by adding your first business location.')}
        </p>
        <Button onClick={onCreateLocation} className="bg-brand-600 hover:bg-brand-700">
          {t('locations.addFirstLocation', 'Add Your First Location')}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6" dir={direction}>
      <div className={`flex items-center  justify-between`}>
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t('locations.yourLocations', 'Your Locations')} ({locations.length})
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {t('locations.manageLocations', 'Manage your business locations and operating hours')}
          </p>
        </div>
        <Button onClick={onCreateLocation} className="bg-brand-600 hover:bg-brand-700">
          {t('locations.addLocation', 'Add Location')}
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {locations.map((location) => (
          <LocationCard
            key={location.id}
            location={location}
            onEdit={() => onEditLocation(location)}
            onDelete={() => onDeleteLocation(location.id)}
            onManageHours={() => {}} // Could be implemented later
            isDeleting={deleteLocationMutation.isPending}
          />
        ))}
      </div>
    </div>
  );
});

LocationResults.displayName = 'LocationResults';

export default function LocationsTab() {
  const { t } = useManagementTranslation();
  const { t: tCommon } = useCommonTranslation();
  const { isRTL, direction } = useRTL();
  const [filters, setFilters] = useState<LocationFilters>({});
  const [editingLocation, setEditingLocation] = useState<Location | null>(null);

  const deleteLocationMutation = useDeleteLocation();

  const {
    isOpen: isLocationModalOpen,
    openModal: openLocationModal,
    closeModal: closeLocationModal,
  } = useModal();

  const confirmation = useConfirmation();

  const handleCreateLocation = () => {
    setEditingLocation(null);
    openLocationModal();
  };

  const handleEditLocation = (location: Location) => {
    setEditingLocation(location);
    openLocationModal();
  };

  const handleDeleteLocation = async (locationId: number) => {
    const confirmed = await confirmation.confirm({
      title: t('locations.deleteConfirmTitle', 'Delete Location'),
      message: t('locations.deleteConfirmMessage', 'Are you sure you want to delete this location? This action cannot be undone.'),
      confirmText: tCommon('actions.delete'),
      cancelText: tCommon('actions.cancel'),
      variant: 'danger'
    });

    if (confirmed) {
      try {
        await deleteLocationMutation.mutateAsync(locationId);
        confirmation.close();
      } catch (error) {
        confirmation.close();
        // Error handled by mutation
      }
    }
  };

  const handleLocationFormSuccess = () => {
    closeLocationModal();
    setEditingLocation(null);
  };

  return (
    <div className="space-y-6" dir={direction}>
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          {t('locations.title', 'Locations Management')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          {t('locations.subtitle', 'Set up your business locations and operating hours')}
        </p>
      </div>

      {/* Locations List */}
      <LocationResults
        filters={filters}
        onEditLocation={handleEditLocation}
        onDeleteLocation={handleDeleteLocation}
        onCreateLocation={handleCreateLocation}
        deleteLocationMutation={deleteLocationMutation}
      />

      {/* Location Form Modal */}
      <Modal
        isOpen={isLocationModalOpen}
        onClose={closeLocationModal}
        className="max-w-2xl"
        showCloseButton={true}
      >
        <LocationForm
          location={editingLocation}
          onSuccess={handleLocationFormSuccess}
          onClose={closeLocationModal}
        />
      </Modal>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmation.isOpen}
        onClose={confirmation.cancel}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        variant={confirmation.variant}
        isLoading={confirmation.isLoading}
      />
    </div>
  );
}
