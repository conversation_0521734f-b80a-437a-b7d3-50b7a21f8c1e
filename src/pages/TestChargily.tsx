import React, { useState } from 'react';
import { usePaymentMethods, useRecommendedPaymentMethod } from '../hooks/usePaymentMethods';
import { useUserLocation } from '../hooks/useUserLocation';
import { useAutoPaymentSelection } from '../hooks/useAutoPaymentSelection';
import PaymentMethodSelector from '../components/subscription/PaymentMethodSelector';
import ChargilyPaymentCard from '../components/subscription/ChargilyPaymentCard';
import HardcodedPaymentMethodsTest from '../components/test/HardcodedPaymentMethodsTest';
import { PaymentProcessor, PaymentMethodType } from '../types';
import { formatCurrency } from '../utils/currency.utils';
import Button from '../components/ui/button/Button';

/**
 * Test page for Chargily Pay integration
 * This page allows testing all aspects of the payment method selection system
 */
export default function TestChargily() {
  const [selectedProcessor, setSelectedProcessor] = useState<PaymentProcessor | ''>('');
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethodType | ''>('');
  const [testScenario, setTestScenario] = useState<'algerian' | 'international' | 'auto'>('auto');

  // Hooks for testing
  const { data: paymentMethodsData, isLoading: methodsLoading, error: methodsError } = usePaymentMethods();
  const { location, isLoading: locationLoading, refreshLocation } = useUserLocation();
  const { 
    recommendedMethod, 
    alternatives, 
    reasoning, 
    confidence,
    isLoading: recommendationLoading 
  } = useRecommendedPaymentMethod();
  
  const {
    selectedProcessor: autoSelectedProcessor,
    selectedMethod: autoSelectedMethod,
    isAutoSelected,
    confidence: autoConfidence,
    reasoning: autoReasoning,
    handleUserSelection,
    resetAutoSelection,
    forceAutoSelection,
    isLoading: autoSelectionLoading,
  } = useAutoPaymentSelection();

  const handlePaymentMethodChange = (processor: PaymentProcessor, method: PaymentMethodType) => {
    setSelectedProcessor(processor);
    setSelectedMethod(method);
  };

  const handleTestScenarioChange = (scenario: 'algerian' | 'international' | 'auto') => {
    setTestScenario(scenario);
    resetAutoSelection();
    setSelectedProcessor('');
    setSelectedMethod('');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Chargily Pay Integration Test
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Test and validate the Chargily Pay payment method integration
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column: Test Controls */}
          <div className="space-y-6">
            {/* Test Scenario Selector */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Test Scenario
              </h2>
              <div className="space-y-3">
                {[
                  { value: 'auto', label: 'Auto-Detection', description: 'Use actual location detection' },
                  { value: 'algerian', label: 'Algerian User', description: 'Simulate Algerian user' },
                  { value: 'international', label: 'International User', description: 'Simulate non-Algerian user' },
                ].map((scenario) => (
                  <label key={scenario.value} className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="scenario"
                      value={scenario.value}
                      checked={testScenario === scenario.value}
                      onChange={(e) => handleTestScenarioChange(e.target.value as any)}
                      className="w-4 h-4 text-brand-600"
                    />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">{scenario.label}</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{scenario.description}</p>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Location Information */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Location Detection
                </h2>
                <Button
                  onClick={refreshLocation}
                  variant="outline"
                  size="sm"
                  disabled={locationLoading}
                >
                  {locationLoading ? 'Detecting...' : 'Refresh'}
                </Button>
              </div>
              
              {locationLoading ? (
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
                </div>
              ) : location ? (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Country:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {location.country} ({location.countryCode})
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Is Algeria:</span>
                    <span className={`font-medium ${location.isAlgeria ? 'text-green-600' : 'text-gray-900 dark:text-white'}`}>
                      {location.isAlgeria ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Detection Method:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {location.detectionMethod}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Confidence:</span>
                    <span className={`font-medium ${
                      location.confidence === 'high' ? 'text-green-600' :
                      location.confidence === 'medium' ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {location.confidence}
                    </span>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">Location not detected</p>
              )}
            </div>

            {/* Payment Methods Data */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Available Payment Methods
              </h2>
              
              {methodsLoading ? (
                <div className="animate-pulse space-y-2">
                  <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
                  <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
                </div>
              ) : methodsError ? (
                <div className="text-red-600 dark:text-red-400">
                  Error loading payment methods: {methodsError.message}
                </div>
              ) : paymentMethodsData?.data?.methods ? (
                <div className="space-y-3">
                  {paymentMethodsData.data.methods.map((method) => (
                    <div key={method.id} className="border border-gray-200 dark:border-gray-700 rounded p-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {method.displayName}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {method.description}
                          </p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {method.supportedMethods.map((subMethod) => (
                              <span key={subMethod} className="text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                                {subMethod}
                              </span>
                            ))}
                          </div>
                        </div>
                        <div className="text-right">
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {method.currency}
                          </span>
                          {method.isRecommended && (
                            <div className="text-xs text-green-600 dark:text-green-400">
                              Recommended
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">No payment methods available</p>
              )}
            </div>

            {/* Recommendation Info */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Recommendation Engine
              </h2>
              
              {recommendationLoading ? (
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-full mb-2"></div>
                  <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-2/3"></div>
                </div>
              ) : recommendedMethod ? (
                <div className="space-y-3">
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      Recommended: {recommendedMethod.displayName}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {reasoning}
                    </p>
                    <p className="text-sm">
                      Confidence: <span className={`font-medium ${
                        confidence === 'high' ? 'text-green-600' :
                        confidence === 'medium' ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {confidence}
                      </span>
                    </p>
                  </div>
                  
                  {alternatives.length > 0 && (
                    <div>
                      <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Alternatives:
                      </p>
                      <div className="space-y-1">
                        {alternatives.map((alt) => (
                          <p key={alt.id} className="text-sm text-gray-500 dark:text-gray-400">
                            • {alt.displayName}
                          </p>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">No recommendation available</p>
              )}
            </div>

            {/* Auto-Selection Info */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Auto-Selection
                </h2>
                <div className="space-x-2">
                  <Button
                    onClick={resetAutoSelection}
                    variant="outline"
                    size="sm"
                  >
                    Reset
                  </Button>
                  <Button
                    onClick={forceAutoSelection}
                    variant="outline"
                    size="sm"
                  >
                    Force
                  </Button>
                </div>
              </div>
              
              {autoSelectionLoading ? (
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-full mb-2"></div>
                  <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Auto-Selected:</span>
                    <span className={`font-medium ${isAutoSelected ? 'text-green-600' : 'text-gray-500'}`}>
                      {isAutoSelected ? 'Yes' : 'No'}
                    </span>
                  </div>
                  {autoSelectedProcessor && (
                    <>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Processor:</span>
                        <span className="font-medium text-gray-900 dark:text-white">
                          {autoSelectedProcessor}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Method:</span>
                        <span className="font-medium text-gray-900 dark:text-white">
                          {autoSelectedMethod}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Confidence:</span>
                        <span className={`font-medium ${
                          autoConfidence === 'high' ? 'text-green-600' :
                          autoConfidence === 'medium' ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {autoConfidence}
                        </span>
                      </div>
                      {autoReasoning && (
                        <div>
                          <span className="text-gray-600 dark:text-gray-400">Reasoning:</span>
                          <p className="text-sm text-gray-900 dark:text-white mt-1">
                            {autoReasoning}
                          </p>
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Right Column: Interactive Components */}
          <div className="space-y-6">
            {/* Hardcoded Payment Methods Test */}
            <HardcodedPaymentMethodsTest />
            {/* Payment Method Selector Test */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Payment Method Selector
              </h2>
              
              <PaymentMethodSelector
                selectedProcessor={selectedProcessor}
                selectedMethod={selectedMethod}
                onSelectionChange={handlePaymentMethodChange}
                enableAutoSelection={true}
                showAutoSelectionInfo={true}
              />
              
              {/* Selection Summary */}
              {selectedProcessor && selectedMethod && (
                <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Selected: {selectedProcessor} - {selectedMethod}
                  </p>
                </div>
              )}
            </div>

            {/* Chargily Payment Card Test */}
            {paymentMethodsData?.data?.methods?.find(m => m.id === 'chargily') && (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Chargily Payment Card
                </h2>
                
                <ChargilyPaymentCard
                  method={paymentMethodsData.data.methods.find(m => m.id === 'chargily')!}
                  isSelected={selectedProcessor === 'chargily'}
                  selectedSubMethod={selectedMethod}
                  onSelect={(method) => handlePaymentMethodChange('chargily', method)}
                  isRecommended={recommendedMethod?.id === 'chargily'}
                  showDetails={true}
                />
              </div>
            )}

            {/* Currency Formatting Test */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Currency Formatting Test
              </h2>
              
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">DZD (Algerian Dinar)</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {formatCurrency(5000, 'DZD')}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">USD (US Dollar)</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {formatCurrency(29.99, 'USD')}
                    </p>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Large Amount (DZD)</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {formatCurrency(150000, 'DZD', { compact: true })}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Large Amount (USD)</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {formatCurrency(1500, 'USD', { compact: true })}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Test Results Summary */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Test Results Summary
              </h2>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Location Detection:</span>
                  <span className={`font-medium ${location ? 'text-green-600' : 'text-red-600'}`}>
                    {location ? '✓ Working' : '✗ Failed'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Payment Methods API:</span>
                  <span className={`font-medium ${paymentMethodsData ? 'text-green-600' : 'text-red-600'}`}>
                    {paymentMethodsData ? '✓ Working' : '✗ Failed'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Recommendation Engine:</span>
                  <span className={`font-medium ${recommendedMethod ? 'text-green-600' : 'text-yellow-600'}`}>
                    {recommendedMethod ? '✓ Working' : '⚠ No Recommendation'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Auto-Selection:</span>
                  <span className={`font-medium ${isAutoSelected ? 'text-green-600' : 'text-yellow-600'}`}>
                    {isAutoSelected ? '✓ Active' : '⚠ Inactive'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Chargily Available:</span>
                  <span className={`font-medium ${
                    paymentMethodsData?.data?.methods?.some(m => m.id === 'chargily') ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {paymentMethodsData?.data?.methods?.some(m => m.id === 'chargily') ? '✓ Available' : '✗ Not Available'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
