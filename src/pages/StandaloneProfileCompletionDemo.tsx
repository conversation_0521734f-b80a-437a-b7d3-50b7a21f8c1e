/**
 * Standalone Profile Completion Demo Page
 * Works without authentication or app layout
 */

import React, { useState } from 'react';
import { calculateProfileCompletion } from '../utils/profile-completion';
import { ProfileCompletionData } from '../types/profile-completion';

const StandaloneProfileCompletionDemo: React.FC = () => {
  const [selectedScenario, setSelectedScenario] = useState('empty');

  // Mock user
  const mockUser = {
    id: 'demo-user',
    email: '<EMAIL>',
    firstName: 'Demo',
    lastName: 'User',
    role: 'CLIENT' as const,
    isEmailVerified: true,
    isPhoneVerified: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  };

  // Demo scenarios
  const scenarios = {
    empty: {
      name: 'Empty Provider (0%)',
      provider: {
        id: 1,
        userId: 'demo-user',
        isSetupComplete: false,
        isVerified: false,
        averageRating: 0,
        totalReviews: 0,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        providingPlaces: [],
        services: [],
        queues: [],
      },
    },
    partial: {
      name: 'Partial Setup (65%)',
      provider: {
        id: 1,
        userId: 'demo-user',
        title: 'Demo Business',
        phone: '+**********',
        presentation: 'This is a demo business for testing profile completion.',
        providerCategoryId: 1,
        isSetupComplete: false,
        isVerified: false,
        averageRating: 0,
        totalReviews: 0,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        providingPlaces: [
          {
            id: 1,
            sProviderId: 1,
            name: 'Main Office',
            address: '123 Demo Street',
            city: 'Demo City',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        ],
        services: [
          {
            id: 1,
            sProviderId: 1,
            title: 'Demo Service',
            duration: 60,
            price: 100,
            pointsRequirements: 1,
            isPublic: true,
            deliveryType: 'at_location' as const,
            servedRegions: ['Demo City'],
            color: '#3B82F6',
            acceptOnline: true,
            acceptNew: true,
            notificationOn: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        ],
        queues: [],
      },
    },
    complete: {
      name: 'Complete Setup (100%)',
      provider: {
        id: 1,
        userId: 'demo-user',
        title: 'Complete Demo Business',
        phone: '+**********',
        presentation: 'This is a complete demo business with all sections filled.',
        providerCategoryId: 1,
        logoId: 'demo-logo',
        isSetupComplete: false,
        isVerified: false,
        averageRating: 4.8,
        totalReviews: 25,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        logo: {
          id: 'demo-logo',
          filename: 'logo.png',
          originalName: 'business-logo.png',
          mimeType: 'image/png',
          size: 2048,
          url: 'https://via.placeholder.com/200x200/3B82F6/FFFFFF?text=LOGO',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        providingPlaces: [
          {
            id: 1,
            sProviderId: 1,
            name: 'Main Office',
            address: '123 Demo Street',
            city: 'Demo City',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        ],
        services: [
          {
            id: 1,
            sProviderId: 1,
            title: 'Premium Service',
            duration: 60,
            price: 150,
            pointsRequirements: 1,
            isPublic: true,
            deliveryType: 'at_location' as const,
            servedRegions: ['Demo City'],
            color: '#3B82F6',
            acceptOnline: true,
            acceptNew: true,
            notificationOn: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        ],
        queues: [
          {
            id: 1,
            sProviderId: 1,
            sProvidingPlaceId: 1,
            title: 'Main Queue',
            isActive: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        ],
      },
    },
  };

  const currentScenario = scenarios[selectedScenario as keyof typeof scenarios];
  const completionData: ProfileCompletionData = {
    user: mockUser,
    provider: currentScenario.provider,
  };

  const completion = calculateProfileCompletion(completionData);

  const getProgressBarColor = (percentage: number) => {
    if (percentage >= 100) return 'bg-green-500';
    if (percentage >= 80) return 'bg-blue-500';
    if (percentage >= 50) return 'bg-yellow-500';
    if (percentage >= 25) return 'bg-orange-500';
    return 'bg-red-500';
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Profile Completion Feature Demo
          </h1>
          <p className="text-gray-600">
            Interactive demo of the profile completion feature with different provider scenarios.
            This demo shows how the completion percentage is calculated and how the UI responds.
          </p>
          <div className="mt-4">
            <a 
              href="/" 
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              ← Back to Dashboard
            </a>
          </div>
        </div>

        {/* Scenario Selector */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Select Scenario</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Object.entries(scenarios).map(([key, scenario]) => (
              <button
                key={key}
                onClick={() => setSelectedScenario(key)}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  selectedScenario === key
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
              >
                <div className="font-medium">{scenario.name}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Profile Completion Card */}
        <div className="bg-white dark:bg-white/[0.03] rounded-2xl shadow-theme-sm border border-gray-200 dark:border-gray-800 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-brand-100 dark:bg-brand-500/15 rounded-full flex items-center justify-center">
                <span className="text-brand-600 dark:text-brand-400 text-lg">📊</span>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white/90">
                  Profile Setup Progress
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {completion.overallPercentage >= 100 ? 'Complete' :
                   completion.overallPercentage >= 80 ? 'Almost Complete' :
                   completion.overallPercentage >= 50 ? 'In Progress' : 'Getting Started'} • {completion.overallPercentage}%
                </p>
              </div>
            </div>
          </div>

          {/* Overall Progress Bar */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">Overall Progress</span>
              <span className="text-sm font-semibold text-gray-900">
                {completion.overallPercentage}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full transition-all duration-300 ${getProgressBarColor(completion.overallPercentage)}`}
                style={{ width: `${completion.overallPercentage}%` }}
              />
            </div>
          </div>

          {/* Status Message */}
          {completion.overallPercentage >= 80 ? (
            <div className="bg-green-50 border border-green-200 rounded-md p-3 mb-4">
              <div className="flex items-center">
                <span className="text-green-500 mr-2">✅</span>
                <span className="text-green-800 text-sm font-medium">
                  Your profile is ready to accept appointments!
                </span>
              </div>
            </div>
          ) : (
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
              <div className="flex items-center">
                <span className="text-blue-500 mr-2">ℹ️</span>
                <span className="text-blue-800 text-sm">
                  Complete your profile to start accepting appointments
                </span>
              </div>
            </div>
          )}

          {/* Section Breakdown */}
          <div className="border-t border-gray-200 pt-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Section Details:</h4>
            <div className="space-y-3">
              {Object.entries(completion.breakdown).map(([sectionKey, section]) => (
                <div key={sectionKey} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                  <div className="flex items-center space-x-3">
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-900 capitalize">
                          {sectionKey.replace(/([A-Z])/g, ' $1').trim()}
                        </span>
                        {section.completed ? (
                          <span className="text-green-500 text-xs">✓</span>
                        ) : (
                          <span className="text-red-500 text-xs">✗</span>
                        )}
                      </div>
                      <p className="text-xs text-gray-600">{section.details}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-700">
                      {section.percentage}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Next Steps */}
          {completion.nextSteps.length > 0 && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Next Steps:</h4>
              <ul className="space-y-1">
                {completion.nextSteps.map((step, index) => (
                  <li key={index} className="text-sm text-gray-600 flex items-center">
                    <span className="text-blue-500 mr-2">•</span>
                    {step}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* Completion Analysis */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Completion Analysis</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Overall Status</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Percentage:</span>
                  <span className="font-medium">{completion.overallPercentage}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Completed:</span>
                  <span className={completion.overallCompleted ? 'text-green-600' : 'text-red-600'}>
                    {completion.overallCompleted ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Should Mark Complete:</span>
                  <span className={completion.shouldMarkAsComplete ? 'text-green-600' : 'text-gray-600'}>
                    {completion.shouldMarkAsComplete ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Section Weights</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Profile Picture:</span>
                  <span className="font-medium">10%</span>
                </div>
                <div className="flex justify-between">
                  <span>Provider Info:</span>
                  <span className="font-medium">30%</span>
                </div>
                <div className="flex justify-between">
                  <span>Locations:</span>
                  <span className="font-medium">25%</span>
                </div>
                <div className="flex justify-between">
                  <span>Services:</span>
                  <span className="font-medium">20%</span>
                </div>
                <div className="flex justify-between">
                  <span>Queues:</span>
                  <span className="font-medium">15%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StandaloneProfileCompletionDemo;
