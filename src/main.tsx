import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { I18nextProvider } from "react-i18next";
import { Toaster } from "react-hot-toast";
import "./index.css";
import "swiper/swiper-bundle.css";
import "flatpickr/dist/flatpickr.css";
import App from "./App.tsx";
import { AppWrapper } from "./components/common/PageMeta.tsx";
import { ThemeProvider } from "./context/ThemeContext.tsx";
import { AuthProvider } from "./context/AuthContext.tsx";
import { LanguageProvider } from "./context/LanguageContext.tsx";
import { CurrencyProvider } from "./context/CurrencyContext.tsx";
import { ErrorBoundary, setupGlobalErrorHandling } from "./components/error";
import i18n from "./lib/i18n";

// Setup global error handling
setupGlobalErrorHandling();

// Debug navigation events in development
if (import.meta.env.DEV) {
  console.log('🔍 Navigation debugging enabled');

  // Track page unload/reload events
  window.addEventListener('beforeunload', (e) => {
    console.log('🔄 Page is about to unload/refresh:', e);
    console.trace('beforeunload stack trace');
  });

  // Track navigation events
  window.addEventListener('popstate', (e) => {
    console.log('🔄 Browser back/forward navigation:', e);
    console.trace('popstate stack trace');
  });

  // Track window.location changes using a different approach
  // We'll use a MutationObserver to watch for URL changes
  let currentUrl = window.location.href;

  // Check for URL changes periodically
  setInterval(() => {
    if (window.location.href !== currentUrl) {
      console.log('🔄 URL changed from:', currentUrl, 'to:', window.location.href);
      console.trace('URL change stack trace');
      currentUrl = window.location.href;
    }
  }, 100);

  // Track history API calls
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;

  history.pushState = function(...args) {
    console.log('🔄 history.pushState called with:', args);
    console.trace('pushState stack trace');
    return originalPushState.apply(this, args);
  };

  history.replaceState = function(...args) {
    console.log('🔄 history.replaceState called with:', args);
    console.trace('replaceState stack trace');
    return originalReplaceState.apply(this, args);
  };

  // Track form submissions that might cause page refresh
  document.addEventListener('submit', (e) => {
    console.log('📝 Form submitted:', e.target);
    console.log('📝 Form action:', (e.target as HTMLFormElement).action);
    console.log('📝 Form method:', (e.target as HTMLFormElement).method);
    console.trace('Form submission stack trace');
  });
}

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
    mutations: {
      retry: 1,
    },
  },
});

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <ErrorBoundary>
      <I18nextProvider i18n={i18n}>
        <QueryClientProvider client={queryClient}>
          <LanguageProvider>
            <ThemeProvider>
              <AuthProvider>
                <CurrencyProvider>
                  <AppWrapper>
                    <App />
                    <Toaster
                      position="top-right"
                      toastOptions={{
                        duration: 4000,
                        style: {
                          background: 'var(--toast-bg)',
                          color: 'var(--toast-color)',
                        },
                      }}
                    />
                  </AppWrapper>
                </CurrencyProvider>
              </AuthProvider>
            </ThemeProvider>
          </LanguageProvider>
        </QueryClientProvider>
      </I18nextProvider>
    </ErrorBoundary>
  </StrictMode>,
);
