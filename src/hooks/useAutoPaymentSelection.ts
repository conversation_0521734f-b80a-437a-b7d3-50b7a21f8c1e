import { useState, useEffect, useCallback } from 'react';
import { usePaymentMethods, useRecommendedPaymentMethod } from './usePaymentMethods';
import { useUserLocation } from './useUserLocation';
import { useSubscriptionStatus } from './useSubscription';
import { PaymentProcessor, PaymentMethodType } from '../types';
import { PaymentMethodCriteria } from '../utils/payment-method-selection.utils';

/**
 * Auto-selection configuration
 */
interface AutoSelectionConfig {
  enableAutoSelection: boolean;
  respectUserPreference: boolean;
  fallbackToDefault: boolean;
  delayMs: number;
}

/**
 * Auto-selection result
 */
interface AutoSelectionResult {
  processor: PaymentProcessor | '';
  method: PaymentMethodType | '';
  isAutoSelected: boolean;
  confidence: 'high' | 'medium' | 'low';
  reasoning: string;
}

/**
 * Hook for automatic payment method selection
 */
export const useAutoPaymentSelection = (
  config: Partial<AutoSelectionConfig> = {},
  additionalCriteria?: Partial<PaymentMethodCriteria>
) => {
  const defaultConfig: AutoSelectionConfig = {
    enableAutoSelection: true,
    respectUserPreference: true,
    fallbackToDefault: true,
    delayMs: 500, // Small delay to ensure all data is loaded
    ...config,
  };

  const [autoSelection, setAutoSelection] = useState<AutoSelectionResult>({
    processor: '',
    method: '',
    isAutoSelected: false,
    confidence: 'low',
    reasoning: '',
  });

  const [hasUserInteracted, setHasUserInteracted] = useState(false);

  const { location, isLoading: locationLoading } = useUserLocation();
  const { data: paymentMethodsData, isLoading: methodsLoading } = usePaymentMethods();
  const { data: subscriptionData } = useSubscriptionStatus();
  const { 
    recommendedMethod, 
    reasoning, 
    confidence,
    isLoading: recommendationLoading 
  } = useRecommendedPaymentMethod(additionalCriteria);

  const isLoading = locationLoading || methodsLoading || recommendationLoading;

  /**
   * Perform automatic selection
   */
  const performAutoSelection = useCallback(() => {
    if (!defaultConfig.enableAutoSelection || isLoading) {
      return;
    }

    // Don't auto-select if user has already interacted
    if (hasUserInteracted && defaultConfig.respectUserPreference) {
      return;
    }

    // Check if we have enough data for auto-selection
    if (!location || !paymentMethodsData?.data?.methods) {
      return;
    }

    let selectedProcessor: PaymentProcessor | '' = '';
    let selectedMethod: PaymentMethodType | '' = '';
    let selectionReasoning = '';
    let selectionConfidence: 'high' | 'medium' | 'low' = 'low';

    // Priority 1: Use previous payment method if available
    if (subscriptionData?.subscription?.paymentProcessor && 
        subscriptionData?.subscription?.paymentMethod) {
      const prevProcessor = subscriptionData.subscription.paymentProcessor as PaymentProcessor;
      const prevMethod = subscriptionData.subscription.paymentMethod as PaymentMethodType;
      
      // Verify the previous method is still available
      const isStillAvailable = paymentMethodsData.data.methods.some(method => 
        method.id === prevProcessor && method.supportedMethods.includes(prevMethod)
      );
      
      if (isStillAvailable) {
        selectedProcessor = prevProcessor;
        selectedMethod = prevMethod;
        selectionReasoning = 'Using your previous payment method';
        selectionConfidence = 'high';
      }
    }

    // Priority 2: Use recommended method
    if (!selectedProcessor && recommendedMethod) {
      selectedProcessor = recommendedMethod.id as PaymentProcessor;
      selectedMethod = recommendedMethod.supportedMethods[0] as PaymentMethodType;
      selectionReasoning = reasoning || 'Recommended for your location';
      selectionConfidence = confidence;
    }

    // Priority 3: Fallback to first available method
    if (!selectedProcessor && defaultConfig.fallbackToDefault && paymentMethodsData.data.methods.length > 0) {
      const fallbackMethod = paymentMethodsData.data.methods[0];
      selectedProcessor = fallbackMethod.id as PaymentProcessor;
      selectedMethod = fallbackMethod.supportedMethods[0] as PaymentMethodType;
      selectionReasoning = 'Default payment method';
      selectionConfidence = 'low';
    }

    // Update auto-selection state
    if (selectedProcessor && selectedMethod) {
      setAutoSelection({
        processor: selectedProcessor,
        method: selectedMethod,
        isAutoSelected: true,
        confidence: selectionConfidence,
        reasoning: selectionReasoning,
      });
    }
  }, [
    defaultConfig,
    isLoading,
    hasUserInteracted,
    location,
    paymentMethodsData,
    subscriptionData,
    recommendedMethod,
    reasoning,
    confidence,
  ]);

  /**
   * Handle user interaction (manual selection)
   */
  const handleUserSelection = useCallback((processor: PaymentProcessor, method: PaymentMethodType) => {
    setHasUserInteracted(true);
    setAutoSelection({
      processor,
      method,
      isAutoSelected: false,
      confidence: 'high',
      reasoning: 'User selected',
    });
  }, []);

  /**
   * Reset auto-selection (useful for testing or form reset)
   */
  const resetAutoSelection = useCallback(() => {
    setHasUserInteracted(false);
    setAutoSelection({
      processor: '',
      method: '',
      isAutoSelected: false,
      confidence: 'low',
      reasoning: '',
    });
  }, []);

  /**
   * Force auto-selection (override user preference)
   */
  const forceAutoSelection = useCallback(() => {
    setHasUserInteracted(false);
    performAutoSelection();
  }, [performAutoSelection]);

  // Perform auto-selection when data is ready
  useEffect(() => {
    if (!isLoading && defaultConfig.enableAutoSelection) {
      const timer = setTimeout(performAutoSelection, defaultConfig.delayMs);
      return () => clearTimeout(timer);
    }
  }, [isLoading, performAutoSelection, defaultConfig.enableAutoSelection, defaultConfig.delayMs]);

  return {
    // Selection state
    selectedProcessor: autoSelection.processor,
    selectedMethod: autoSelection.method,
    isAutoSelected: autoSelection.isAutoSelected,
    confidence: autoSelection.confidence,
    reasoning: autoSelection.reasoning,
    
    // User interaction state
    hasUserInteracted,
    
    // Loading state
    isLoading,
    
    // Actions
    handleUserSelection,
    resetAutoSelection,
    forceAutoSelection,
    
    // Recommendation data
    recommendedMethod,
    availableMethods: paymentMethodsData?.data?.methods || [],
    
    // Location data
    userLocation: location,
    isAlgeria: location?.isAlgeria || false,
  };
};

/**
 * Hook for auto-selection with Algerian user optimization
 */
export const useAlgerianAutoSelection = (additionalCriteria?: Partial<PaymentMethodCriteria>) => {
  return useAutoPaymentSelection(
    {
      enableAutoSelection: true,
      respectUserPreference: true,
      fallbackToDefault: true,
      delayMs: 300, // Faster for Algerian users
    },
    additionalCriteria
  );
};

/**
 * Hook for auto-selection with international user optimization
 */
export const useInternationalAutoSelection = (additionalCriteria?: Partial<PaymentMethodCriteria>) => {
  return useAutoPaymentSelection(
    {
      enableAutoSelection: true,
      respectUserPreference: true,
      fallbackToDefault: true,
      delayMs: 800, // Slower, more conservative for international users
    },
    additionalCriteria
  );
};

/**
 * Hook for conservative auto-selection (minimal automation)
 */
export const useConservativeAutoSelection = (additionalCriteria?: Partial<PaymentMethodCriteria>) => {
  return useAutoPaymentSelection(
    {
      enableAutoSelection: false, // Disabled by default
      respectUserPreference: true,
      fallbackToDefault: false,
      delayMs: 1000,
    },
    additionalCriteria
  );
};

/**
 * Hook for aggressive auto-selection (maximum automation)
 */
export const useAggressiveAutoSelection = (additionalCriteria?: Partial<PaymentMethodCriteria>) => {
  return useAutoPaymentSelection(
    {
      enableAutoSelection: true,
      respectUserPreference: false, // Override user preference
      fallbackToDefault: true,
      delayMs: 100, // Very fast
    },
    additionalCriteria
  );
};
