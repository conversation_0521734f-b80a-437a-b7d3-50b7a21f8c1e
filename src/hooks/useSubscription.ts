import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { SubscriptionService } from '../services/subscription.service';
import {
  CheckoutSessionRequest,
  SubscriptionFilters,
  UsagePeriod,
} from '../types';
import { ErrorLogger } from '../lib/error-utils';
import toast from 'react-hot-toast';

/**
 * Query keys for subscription-related queries
 */
export const subscriptionKeys = {
  all: ['subscription'] as const,
  plans: () => [...subscriptionKeys.all, 'plans'] as const,
  status: () => [...subscriptionKeys.all, 'status'] as const,
  usage: (filters?: SubscriptionFilters) => [...subscriptionKeys.all, 'usage', filters] as const,
  portal: () => [...subscriptionKeys.all, 'portal'] as const,
};

/**
 * Hook for fetching subscription plans
 * Public endpoint - no authentication required
 */
export const useSubscriptionPlans = () => {
  return useQuery({
    queryKey: subscriptionKeys.plans(),
    queryFn: () => SubscriptionService.getSubscriptionPlans(),
    staleTime: 10 * 60 * 1000, // 10 minutes (plans don't change often)
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchSubscriptionPlans' });
    },
  });
};

/**
 * Hook for fetching user's subscription status
 * Requires authentication
 */
export const useSubscriptionStatus = () => {
  return useQuery({
    queryKey: subscriptionKeys.status(),
    queryFn: () => SubscriptionService.getUserPaymentStatus(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchSubscriptionStatus' });
    },
  });
};

/**
 * Hook for fetching usage statistics
 * Requires authentication
 */
export const useUsageStatistics = (filters?: SubscriptionFilters) => {
  return useQuery({
    queryKey: subscriptionKeys.usage(filters),
    queryFn: () => SubscriptionService.getUsageStatistics(filters),
    staleTime: 1 * 60 * 1000, // 1 minute (usage changes frequently)
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchUsageStatistics' });
    },
  });
};

/**
 * Hook for fetching usage statistics for a specific period
 * Convenience hook for common usage patterns
 */
export const useUsageForPeriod = (period: UsagePeriod) => {
  return useUsageStatistics({ period });
};

/**
 * Hook for checking customer portal availability
 * Requires authentication
 */
export const useCustomerPortalAccess = () => {
  return useQuery({
    queryKey: subscriptionKeys.portal(),
    queryFn: () => SubscriptionService.getCustomerPortalUrl(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 1, // Portal might not be available, don't retry too much
    throwOnError: false, // Don't throw errors, just return undefined
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchCustomerPortalAccess' });
    },
  });
};

/**
 * Hook for creating a checkout session
 * Requires authentication
 */
export const useCreateCheckoutSession = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CheckoutSessionRequest) => SubscriptionService.createCheckoutSession(data),
    onSuccess: (response) => {
      // Invalidate subscription-related queries after successful checkout creation
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.status() });
      
      // Redirect to checkout URL
      if (response.success && response.data.sessionUrl) {
        SubscriptionService.redirectToCheckout(response.data.sessionUrl);
      }
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to create checkout session';
      ErrorLogger.log(error, { context: 'createCheckoutSession' });
      toast.error(message);
    },
  });
};

/**
 * Hook for accessing customer portal
 * Requires authentication and active subscription
 */
export const useCustomerPortal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => SubscriptionService.redirectToCustomerPortal(),
    onSuccess: () => {
      // Invalidate subscription status when returning from portal
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.status() });
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.usage() });
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to access customer portal';
      ErrorLogger.log(error, { context: 'accessCustomerPortal' });
      toast.error(message);
    },
  });
};

/**
 * Hook for checking if user has active subscription
 * Convenience hook that extracts subscription status
 */
export const useHasActiveSubscription = () => {
  const { data: statusData, isLoading, error } = useSubscriptionStatus();
  
  return {
    hasActiveSubscription: statusData?.data?.subscription?.isActive || false,
    subscriptionStatus: statusData?.data?.subscription?.status,
    isLoading,
    error,
  };
};

/**
 * Hook for checking current credits
 * Convenience hook for credit checking
 */
export const useCurrentCredits = () => {
  const { data: statusData, isLoading, error } = useSubscriptionStatus();
  
  return {
    credits: statusData?.data?.user?.credits || 0,
    isLoading,
    error,
  };
};

/**
 * Hook for checking queue limits
 * Convenience hook for queue limit checking
 */
export const useQueueLimits = () => {
  const { data: statusData, isLoading, error } = useSubscriptionStatus();
  const { data: usageData } = useUsageStatistics();
  
  return {
    currentQueues: usageData?.data?.current?.queues || 0,
    maxQueues: statusData?.data?.user?.queues || 0,
    isAtLimit: (usageData?.data?.current?.queues || 0) >= (statusData?.data?.user?.queues || 0),
    isLoading,
    error,
  };
};

/**
 * Hook for checking if approaching credit limit
 * Utility hook for usage warnings
 */
export const useIsApproachingCreditLimit = (threshold: number = 0.2) => {
  const { data: usageData, isLoading, error } = useUsageStatistics();
  
  const isApproaching = usageData?.data ? (() => {
    const { current, limits } = usageData.data;
    if (limits.credits === 0) return false;
    const usagePercentage = current.credits / limits.credits;
    return usagePercentage >= (1 - threshold);
  })() : false;
  
  return {
    isApproachingLimit: isApproaching,
    currentCredits: usageData?.data?.current?.credits || 0,
    maxCredits: usageData?.data?.limits?.credits || 0,
    usagePercentage: usageData?.data ? 
      (usageData.data.current.credits / usageData.data.limits.credits) * 100 : 0,
    isLoading,
    error,
  };
};

/**
 * Hook for getting subscription plan by ID
 * Utility hook to find specific plan details
 */
export const useSubscriptionPlanById = (planId: string) => {
  const { data: plansData, isLoading, error } = useSubscriptionPlans();
  
  const plan = plansData?.data?.plans?.find(p => p.id === planId);
  
  return {
    plan,
    isLoading,
    error,
  };
};

/**
 * Hook for invalidating subscription data
 * Utility hook for manual data refresh
 */
export const useRefreshSubscriptionData = () => {
  const queryClient = useQueryClient();
  
  return {
    refreshAll: () => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.all });
    },
    refreshStatus: () => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.status() });
    },
    refreshUsage: () => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.usage() });
    },
    refreshPlans: () => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.plans() });
    },
  };
};
