import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AppointmentService } from '../services/appointment.service';
import {
  AppointmentCreateRequest,
  AppointmentUpdateRequest,
  AppointmentFilters,
  AppointmentStatusUpdate,
} from '../types';
import { ErrorLogger } from '../lib/error-utils';
import toast from 'react-hot-toast';

/**
 * Hook for fetching appointments
 */
export const useAppointments = (filters?: AppointmentFilters) => {
  return useQuery({
    queryKey: ['appointments', filters],
    queryFn: () => AppointmentService.getAppointments(filters),
    staleTime: 30 * 1000, // 30 seconds - more aggressive for dashboard widgets
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchInterval: 60 * 1000, // Refetch every minute for dashboard widgets
    refetchIntervalInBackground: true, // Continue refreshing when tab is not active
    refetchOnWindowFocus: true, // Refetch when window regains focus
    refetchOnMount: true, // Always refetch when component mounts
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchAppointments' });
    },
  });
};

/**
 * Hook for fetching a single appointment
 */
export const useAppointment = (id: number) => {
  return useQuery({
    queryKey: ['appointments', id],
    queryFn: () => AppointmentService.getAppointment(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchAppointment' });
    },
  });
};

/**
 * Hook for creating an appointment
 */
export const useCreateAppointment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: AppointmentCreateRequest) => AppointmentService.createAppointment(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      queryClient.invalidateQueries({ queryKey: ['appointment-stats'] });
      queryClient.invalidateQueries({ queryKey: ['available-slots'] });
      toast.success('Appointment created successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to create appointment';
      ErrorLogger.log(error, { context: 'createAppointment' });
      toast.error(message);
    },
  });
};

/**
 * Hook for updating an appointment
 */
export const useUpdateAppointment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: AppointmentUpdateRequest }) =>
      AppointmentService.updateAppointment(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      queryClient.invalidateQueries({ queryKey: ['appointments', id] });
      queryClient.invalidateQueries({ queryKey: ['appointment-stats'] });
      toast.success('Appointment updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to update appointment';
      ErrorLogger.log(error, { context: 'updateAppointment' });
      toast.error(message);
    },
  });
};

/**
 * Hook for deleting an appointment
 */
export const useDeleteAppointment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => AppointmentService.deleteAppointment(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      queryClient.invalidateQueries({ queryKey: ['appointment-stats'] });
      toast.success('Appointment deleted successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to delete appointment';
      ErrorLogger.log(error, { context: 'deleteAppointment' });
      toast.error(message);
    },
  });
};

/**
 * Hook for updating appointment status
 */
export const useUpdateAppointmentStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: number; status: AppointmentStatusUpdate }) =>
      AppointmentService.updateAppointmentStatus(id, status),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      queryClient.invalidateQueries({ queryKey: ['appointments', id] });
      queryClient.invalidateQueries({ queryKey: ['appointment-stats'] });
      toast.success('Appointment status updated!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to update appointment status';
      ErrorLogger.log(error, { context: 'updateAppointmentStatus' });
      toast.error(message);
    },
  });
};

/**
 * Hook to get the current active appointment (InProgress status)
 * Fetches all appointments and filters client-side for InProgress status
 */
export const useActiveAppointment = () => {
  return useQuery({
    queryKey: ['appointments', 'active'],
    queryFn: async () => {
      // Fetch all appointments without any filters
      const appointments = await AppointmentService.getAppointments();

      // Filter client-side for InProgress status
      const activeAppointment = appointments.find(apt => apt.status === 'InProgress') || null;

      return activeAppointment;
    },
    refetchInterval: 15 * 1000, // Refetch every 15 seconds - more aggressive for active sessions
    staleTime: 5 * 1000, // Consider data stale after 5 seconds
    refetchIntervalInBackground: true, // Continue refreshing when tab is not active
    refetchOnWindowFocus: true, // Refetch when window regains focus
    refetchOnMount: true, // Always refetch when component mounts
    retry: 2,
  });
};

/**
 * Hook for fetching appointment statistics
 */
export const useAppointmentStats = (filters?: { startDate?: string; endDate?: string }) => {
  return useQuery({
    queryKey: ['appointment-stats', filters],
    queryFn: () => AppointmentService.getAppointmentStats(filters),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchAppointmentStats' });
    },
  });
};

/**
 * Hook for fetching available time slots
 */
export const useAvailableTimeSlots = (params: {
  serviceId: number;
  locationId: number;
  date: string;
}) => {
  return useQuery({
    queryKey: ['available-slots', params],
    queryFn: () => AppointmentService.getAvailableTimeSlots(params),
    enabled: !!(params.serviceId && params.locationId && params.date),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchAvailableTimeSlots' });
    },
  });
};

/**
 * Hook for confirming an appointment
 */
export const useConfirmAppointment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => AppointmentService.confirmAppointment(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      queryClient.invalidateQueries({ queryKey: ['appointments', id] });
      queryClient.invalidateQueries({ queryKey: ['appointment-stats'] });
      toast.success('Appointment confirmed!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to confirm appointment';
      ErrorLogger.log(error, { context: 'confirmAppointment' });
      toast.error(message);
    },
  });
};

/**
 * Hook for cancelling an appointment
 */
export const useCancelAppointment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reason }: { id: number; reason?: string }) =>
      AppointmentService.cancelAppointment(id, reason),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      queryClient.invalidateQueries({ queryKey: ['appointments', id] });
      queryClient.invalidateQueries({ queryKey: ['appointment-stats'] });
      toast.success('Appointment cancelled!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to cancel appointment';
      ErrorLogger.log(error, { context: 'cancelAppointment' });
      toast.error(message);
    },
  });
};

/**
 * Hook for completing an appointment
 */
export const useCompleteAppointment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, notes }: { id: number; notes?: string }) =>
      AppointmentService.completeAppointment(id, notes),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      queryClient.invalidateQueries({ queryKey: ['appointments', id] });
      queryClient.invalidateQueries({ queryKey: ['appointment-stats'] });
      toast.success('Appointment completed!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to complete appointment';
      ErrorLogger.log(error, { context: 'completeAppointment' });
      toast.error(message);
    },
  });
};

/**
 * Hook for rescheduling an appointment
 */
export const useRescheduleAppointment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, newDateTime }: { id: number; newDateTime: string }) =>
      AppointmentService.rescheduleAppointment(id, newDateTime),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      queryClient.invalidateQueries({ queryKey: ['appointments', id] });
      queryClient.invalidateQueries({ queryKey: ['appointment-stats'] });
      queryClient.invalidateQueries({ queryKey: ['available-slots'] });
      toast.success('Appointment rescheduled!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to reschedule appointment';
      ErrorLogger.log(error, { context: 'rescheduleAppointment' });
      toast.error(message);
    },
  });
};

/**
 * Hook for fetching appointments by date range
 */
export const useAppointmentsByDateRange = (startDate: string, endDate: string) => {
  return useQuery({
    queryKey: ['appointments', 'date-range', startDate, endDate],
    queryFn: () => AppointmentService.getAppointmentsByDateRange(startDate, endDate),
    enabled: !!(startDate && endDate),
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchAppointmentsByDateRange' });
    },
  });
};
