import { useTranslation as useI18nTranslation, UseTranslationOptions } from 'react-i18next';
import { useLanguage } from '../context/LanguageContext';

// Define available namespaces
export type TranslationNamespace = 'common' | 'auth' | 'dashboard' | 'forms' | 'errors' | 'calendar' | 'management';

// Custom hook that wraps react-i18next's useTranslation with our types
export const useTranslation = (
  namespace: TranslationNamespace | TranslationNamespace[] = 'common',
  options?: UseTranslationOptions
) => {
  const { t, i18n, ready } = useI18nTranslation(namespace, options);
  const { currentLanguage, isRTL, direction } = useLanguage();

  return {
    t,
    i18n,
    ready,
    currentLanguage,
    isRTL,
    direction,
  };
};

// Utility hook for common translations
export const useCommonTranslation = () => {
  return useTranslation('common');
};

// Utility hook for auth translations
export const useAuthTranslation = () => {
  return useTranslation('auth');
};

// Utility hook for dashboard translations
export const useDashboardTranslation = () => {
  return useTranslation('dashboard');
};

// Utility hook for form translations
export const useFormTranslation = () => {
  return useTranslation('forms');
};

// Utility hook for error translations
export const useErrorTranslation = () => {
  return useTranslation('errors');
};

// Utility hook for calendar translations
export const useCalendarTranslation = () => {
  return useTranslation('calendar');
};

// Utility hook for management translations
export const useManagementTranslation = () => {
  return useTranslation('management');
};

// Utility hook for multiple namespaces
export const useMultipleTranslations = (namespaces: TranslationNamespace[]) => {
  return useTranslation(namespaces);
};

export default useTranslation;
