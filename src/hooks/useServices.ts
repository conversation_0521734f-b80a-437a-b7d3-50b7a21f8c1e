import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ServiceService } from '../services/service.service';
import {
  ServiceCreateRequest,
  ServiceUpdateRequest,
  ServiceCategoryCreateRequest,
  ServiceCategoryUpdateRequest,
  ServiceFilters,
} from '../types';
import { ErrorLogger } from '../lib/error-utils';
import toast from 'react-hot-toast';

/**
 * Hook for fetching services
 */
export const useServices = (filters?: ServiceFilters) => {
  return useQuery({
    queryKey: ['services', filters],
    queryFn: () => ServiceService.getServices(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchServices' });
    },
  });
};

/**
 * Hook for fetching a single service
 */
export const useService = (id: number) => {
  return useQuery({
    queryKey: ['services', id],
    queryFn: () => ServiceService.getService(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchService' });
    },
  });
};

/**
 * Hook for creating a service
 */
export const useCreateService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ServiceCreateRequest) => ServiceService.createService(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['services'] });
      queryClient.invalidateQueries({ queryKey: ['service-stats'] });
      toast.success('Service created successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to create service';
      ErrorLogger.log(error, { context: 'createService' });
      toast.error(message);
    },
  });
};

/**
 * Hook for updating a service
 */
export const useUpdateService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: ServiceUpdateRequest }) =>
      ServiceService.updateService(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['services'] });
      queryClient.invalidateQueries({ queryKey: ['services', id] });
      queryClient.invalidateQueries({ queryKey: ['service-stats'] });
      toast.success('Service updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to update service';
      ErrorLogger.log(error, { context: 'updateService' });
      toast.error(message);
    },
  });
};

/**
 * Hook for deleting a service
 */
export const useDeleteService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => ServiceService.deleteService(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['services'] });
      queryClient.invalidateQueries({ queryKey: ['service-stats'] });
      toast.success('Service deleted successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to delete service';
      ErrorLogger.log(error, { context: 'deleteService' });
      toast.error(message);
    },
  });
};

/**
 * Hook for fetching service statistics
 */
export const useServiceStats = () => {
  return useQuery({
    queryKey: ['service-stats'],
    queryFn: () => ServiceService.getServiceStats(),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchServiceStats' });
    },
  });
};

// Service Categories Hooks
/**
 * Hook for fetching service categories
 */
export const useServiceCategories = () => {
  return useQuery({
    queryKey: ['service-categories'],
    queryFn: () => ServiceService.getServiceCategories(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchServiceCategories' });
    },
  });
};

/**
 * Hook for creating a service category
 */
export const useCreateServiceCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ServiceCategoryCreateRequest) =>
      ServiceService.createServiceCategory(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['service-categories'] });
      toast.success('Category created successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to create category';
      ErrorLogger.log(error, { context: 'createServiceCategory' });
      toast.error(message);
    },
  });
};

/**
 * Hook for updating a service category
 */
export const useUpdateServiceCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: ServiceCategoryUpdateRequest }) =>
      ServiceService.updateServiceCategory(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['service-categories'] });
      toast.success('Category updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to update category';
      ErrorLogger.log(error, { context: 'updateServiceCategory' });
      toast.error(message);
    },
  });
};

/**
 * Hook for deleting a service category
 */
export const useDeleteServiceCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => ServiceService.deleteServiceCategory(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['service-categories'] });
      queryClient.invalidateQueries({ queryKey: ['services'] }); // Services might be affected
      toast.success('Category deleted successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to delete category';
      ErrorLogger.log(error, { context: 'deleteServiceCategory' });
      toast.error(message);
    },
  });
};
