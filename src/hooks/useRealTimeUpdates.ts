import { useEffect, useRef, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../context/AuthContext';
import { webSocketService } from '../services/websocket.service';
import toast from 'react-hot-toast';

/**
 * Hook for managing real-time updates via WebSocket
 */
export const useRealTimeUpdates = () => {
  const queryClient = useQueryClient();
  const { provider } = useAuth();
  const isConnectedRef = useRef(false);

  const refreshData = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['appointments'] });
    queryClient.invalidateQueries({ queryKey: ['appointment-stats'] });
    queryClient.invalidateQueries({ queryKey: ['services'] });
    queryClient.invalidateQueries({ queryKey: ['locations'] });
    queryClient.invalidateQueries({ queryKey: ['queues'] });
    // Dashboard-specific invalidations
    queryClient.invalidateQueries({ queryKey: ['dashboard'] });
  }, [queryClient]);

  useEffect(() => {
    if (!provider?.id || isConnectedRef.current) return;

    const connectWebSocket = async () => {
      try {
        await webSocketService.connect(provider.id);
        isConnectedRef.current = true;

        // Set up event listeners
        webSocketService.onQueueStateUpdate((data) => {
          console.log('Queue state updated:', data);
          queryClient.invalidateQueries({ queryKey: ['appointments'] });
          queryClient.invalidateQueries({ queryKey: ['queues', data.queueId] });
        });

        webSocketService.onAppointmentStatusChanged((data) => {
          console.log('Appointment status changed:', data);
          queryClient.invalidateQueries({ queryKey: ['appointments'] });
          queryClient.invalidateQueries({ queryKey: ['appointments', data.appointmentId] });
          // Refresh dashboard data when appointment status changes
          queryClient.invalidateQueries({ queryKey: ['dashboard'] });

          // Show notification
          toast.success(`Appointment status updated to ${data.newStatus}`);
        });

        webSocketService.onNewAppointmentBooked((data) => {
          console.log('New appointment booked:', data);
          queryClient.invalidateQueries({ queryKey: ['appointments'] });
          queryClient.invalidateQueries({ queryKey: ['appointment-stats'] });
          // Refresh dashboard data when new appointment is booked
          queryClient.invalidateQueries({ queryKey: ['dashboard'] });

          // Show notification
          toast.success(`New appointment booked: ${data.customerName} - ${data.serviceName}`);
        });

        webSocketService.onAppointmentCanceled((data) => {
          console.log('Appointment canceled:', data);
          queryClient.invalidateQueries({ queryKey: ['appointments'] });
          queryClient.invalidateQueries({ queryKey: ['appointment-stats'] });
          // Refresh dashboard data when appointment is canceled
          queryClient.invalidateQueries({ queryKey: ['dashboard'] });

          // Show notification
          toast.info('Appointment has been canceled');
        });

        webSocketService.onAppointmentRescheduled((data) => {
          console.log('Appointment rescheduled:', data);
          queryClient.invalidateQueries({ queryKey: ['appointments'] });
          // Refresh dashboard data when appointment is rescheduled
          queryClient.invalidateQueries({ queryKey: ['dashboard'] });

          // Show notification
          toast.info('Appointment has been rescheduled');
        });

        webSocketService.onProviderCreditsUpdated((data) => {
          console.log('Provider credits updated:', data);
          queryClient.invalidateQueries({ queryKey: ['provider-profile'] });

          // Show notification
          if (data.change > 0) {
            toast.success(`Credits increased by ${data.change}. New balance: ${data.newCredits}`);
          } else if (data.change < 0) {
            toast.info(`Credits used: ${Math.abs(data.change)}. Remaining: ${data.newCredits}`);
          }
        });

      } catch (error) {
        console.error('Failed to connect to WebSocket:', error);
        // Fallback to polling if WebSocket fails
        const fallbackInterval = setInterval(() => {
          refreshData();
        }, 60000); // Poll every minute as fallback

        return () => clearInterval(fallbackInterval);
      }
    };

    connectWebSocket();

    // Cleanup on unmount
    return () => {
      if (isConnectedRef.current) {
        webSocketService.removeAllListeners();
        webSocketService.disconnect();
        isConnectedRef.current = false;
      }
    };
  }, [provider?.id, queryClient, refreshData]);

  return {
    refreshData,
    isConnected: () => webSocketService.isConnected(),
    requestQueueStatus: (queueId?: number) => webSocketService.requestQueueStatus(queueId),
    notifyQueueChange: (queueId: number) => webSocketService.notifyQueueChange(queueId),
  };
};

/**
 * Hook for managing real-time notifications with browser notifications
 */
export const useRealTimeNotifications = () => {
  const { provider } = useAuth();

  useEffect(() => {
    if (!provider) return;

    // Request notification permission
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          console.log('✅ Notification permission granted');
        }
      });
    }

    // Set up WebSocket notification handlers
    const handleNewAppointment = (data: any) => {
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('New Appointment Booked', {
          body: `${data.customerName} - ${data.serviceName}`,
          icon: '/favicon.ico',
          tag: `new-appointment-${data.appointmentId}`,
        });
      }
    };

    const handleStatusChange = (data: any) => {
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('Appointment Status Updated', {
          body: `Appointment status changed to ${data.newStatus}`,
          icon: '/favicon.ico',
          tag: `status-change-${data.appointmentId}`,
        });
      }
    };

    const handleCancellation = (data: any) => {
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('Appointment Canceled', {
          body: data.reason || 'An appointment has been canceled',
          icon: '/favicon.ico',
          tag: `canceled-${data.appointmentId}`,
        });
      }
    };

    // Subscribe to WebSocket events
    webSocketService.onNewAppointmentBooked(handleNewAppointment);
    webSocketService.onAppointmentStatusChanged(handleStatusChange);
    webSocketService.onAppointmentCanceled(handleCancellation);

    // Cleanup
    return () => {
      webSocketService.off('newAppointmentBooked', handleNewAppointment);
      webSocketService.off('appointmentStatusChanged', handleStatusChange);
      webSocketService.off('appointmentCanceled', handleCancellation);
    };
  }, [provider]);
};

/**
 * Hook for managing appointment reminders and alerts
 */
export const useAppointmentReminders = () => {
  const queryClient = useQueryClient();
  const notifiedAppointments = useRef<Set<string>>(new Set());

  useEffect(() => {
    // Check for upcoming appointments every minute
    const checkReminders = () => {
      const appointments = queryClient.getQueryData(['appointments']) as any[];

      if (!appointments) return;

      const now = new Date();
      const upcomingAppointments = appointments.filter(appointment => {
        const appointmentTime = new Date(appointment.expectedAppointmentStartTime);
        const timeDiff = appointmentTime.getTime() - now.getTime();
        const hoursUntil = timeDiff / (1000 * 60 * 60);

        // Check for appointments in the next 2 hours
        return hoursUntil > 0 && hoursUntil <= 2 &&
               (appointment.status === 'confirmed' || appointment.status === 'InProgress');
      });

      upcomingAppointments.forEach(appointment => {
        const appointmentTime = new Date(appointment.expectedAppointmentStartTime);
        const timeDiff = appointmentTime.getTime() - now.getTime();
        const minutesUntil = Math.round(timeDiff / (1000 * 60));

        // Create unique notification key
        const notificationKey = `${appointment.id}-${minutesUntil}`;

        // Only notify for specific intervals and avoid duplicate notifications
        if ((minutesUntil === 60 || minutesUntil === 30 || minutesUntil === 15 || minutesUntil === 5) &&
            !notifiedAppointments.current.has(notificationKey)) {

          // Mark as notified
          notifiedAppointments.current.add(notificationKey);

          // Show browser notification
          if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('Upcoming Appointment', {
              body: `${appointment.customer?.firstName} ${appointment.customer?.lastName} - ${appointment.service?.title} in ${minutesUntil} minutes`,
              icon: '/favicon.ico',
              tag: `reminder-${appointment.id}`,
              requireInteraction: minutesUntil <= 15, // Require interaction for urgent reminders
            });
          }

          // Show toast notification as well
          if (minutesUntil <= 15) {
            toast.info(`Appointment starting in ${minutesUntil} minutes: ${appointment.customer?.firstName} ${appointment.customer?.lastName}`);
          }
        }
      });

      // Clean up old notification keys (older than 2 hours)
      const cutoffTime = now.getTime() - (2 * 60 * 60 * 1000);
      notifiedAppointments.current.forEach(key => {
        const [appointmentId, minutes] = key.split('-');
        const appointment = appointments.find(a => a.id.toString() === appointmentId);
        if (appointment) {
          const appointmentTime = new Date(appointment.expectedAppointmentStartTime);
          if (appointmentTime.getTime() < cutoffTime) {
            notifiedAppointments.current.delete(key);
          }
        }
      });
    };

    const interval = setInterval(checkReminders, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [queryClient]);
};

/**
 * Combined hook for all real-time features
 */
export const useRealTimeFeatures = () => {
  const updates = useRealTimeUpdates();

  useRealTimeNotifications();
  useAppointmentReminders();

  return {
    ...updates,
    // Additional utility methods
    sendQueueUpdate: (queueId: number) => {
      updates.notifyQueueChange(queueId);
    },
    requestCurrentStatus: (queueId?: number) => {
      updates.requestQueueStatus(queueId);
    },
  };
};
