/**
 * React hook for managing profile completion data
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';
import { useAuth } from '../context/AuthContext';
import { ProfileCompletionService } from '../services/profile-completion.service';
import {
  ProfileCompletionResult,
  ProfileCompletionData,
  UseProfileCompletionReturn,
  CompleteSetupRequest,
} from '../types/profile-completion';

// Query keys for React Query
export const PROFILE_COMPLETION_QUERY_KEYS = {
  completion: (userId: string) => ['profile-completion', userId],
  provider: (userId: string) => ['provider', userId],
} as const;

/**
 * Hook for fetching and managing profile completion status
 */
export function useProfileCompletion(): UseProfileCompletionReturn {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Query for profile completion data
  const {
    data: completion,
    isLoading,
    error,
    refetch: refetchCompletion,
  } = useQuery({
    queryKey: PROFILE_COMPLETION_QUERY_KEYS.completion(user?.id || ''),
    queryFn: () => ProfileCompletionService.getProfileCompletion(),
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Refetch function with error handling
  const refetch = useCallback(async () => {
    try {
      await refetchCompletion();
    } catch (error) {
      console.error('Failed to refetch profile completion:', error);
      throw error;
    }
  }, [refetchCompletion]);

  return {
    completion: completion || null,
    isLoading,
    error: error as Error | null,
    refetch,
  };
}

/**
 * Hook for calculating profile completion from local data
 */
export function useProfileCompletionCalculation(data: ProfileCompletionData | null) {
  return useMemo(() => {
    if (!data) return null;
    return ProfileCompletionService.calculateCompletionSafe(data);
  }, [data]);
}

/**
 * Hook for managing profile completion card visibility
 */
export function useProfileCompletionCard() {
  const { completion, isLoading, error, refetch } = useProfileCompletion();

  const isDismissed = useMemo(() => {
    return ProfileCompletionService.isCardDismissed();
  }, []);

  const shouldShowCard = useMemo(() => {
    if (isLoading || error || !completion || isDismissed) {
      return false;
    }
    return completion.overallPercentage < 100;
  }, [completion, isLoading, error, isDismissed]);

  const dismissCard = useCallback(() => {
    ProfileCompletionService.dismissCard();
  }, []);

  const resetDismissal = useCallback(() => {
    ProfileCompletionService.resetCardDismissal();
  }, []);

  return {
    completion,
    isLoading,
    error,
    refetch,
    shouldShowCard,
    isDismissed,
    dismissCard,
    resetDismissal,
  };
}

/**
 * Hook for completing provider setup
 */
export function useCompleteSetup() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: (setupData: CompleteSetupRequest) => 
      ProfileCompletionService.completeSetup(setupData),
    onSuccess: (data) => {
      // Invalidate and refetch profile completion
      if (user?.id) {
        queryClient.invalidateQueries({
          queryKey: PROFILE_COMPLETION_QUERY_KEYS.completion(user.id),
        });
        queryClient.invalidateQueries({
          queryKey: PROFILE_COMPLETION_QUERY_KEYS.provider(user.id),
        });
      }
      
      // Clear completion cache
      ProfileCompletionService.clearAllCache();
      
      console.log('Setup completed successfully:', data.data.summary);
    },
    onError: (error) => {
      console.error('Failed to complete setup:', error);
    },
  });
}

/**
 * Hook for managing profile completion cache
 */
export function useProfileCompletionCache() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const clearCache = useCallback(() => {
    if (user?.id) {
      ProfileCompletionService.clearCache(user.id);
      queryClient.invalidateQueries({
        queryKey: PROFILE_COMPLETION_QUERY_KEYS.completion(user.id),
      });
    }
  }, [user?.id, queryClient]);

  const clearAllCache = useCallback(() => {
    ProfileCompletionService.clearAllCache();
    queryClient.invalidateQueries({
      predicate: (query) => 
        query.queryKey[0] === 'profile-completion',
    });
  }, [queryClient]);

  const getCacheStats = useCallback(() => {
    return ProfileCompletionService.getCacheStats();
  }, []);

  return {
    clearCache,
    clearAllCache,
    getCacheStats,
  };
}

/**
 * Hook for profile completion navigation helpers
 */
export function useProfileCompletionNavigation() {
  const getNavigationRoute = useCallback((section: string, isSetupMode: boolean = false) => {
    return ProfileCompletionService.getNavigationRoute(section, isSetupMode);
  }, []);

  const getSectionDisplayName = useCallback((section: string) => {
    return ProfileCompletionService.getSectionDisplayName(section);
  }, []);

  const getCompletionColor = useCallback((percentage: number) => {
    return ProfileCompletionService.getCompletionColor(percentage);
  }, []);

  const getCompletionStatusText = useCallback((percentage: number) => {
    return ProfileCompletionService.getCompletionStatusText(percentage);
  }, []);

  const formatPercentage = useCallback((percentage: number) => {
    return ProfileCompletionService.formatPercentage(percentage);
  }, []);

  const isFunctionallyComplete = useCallback((percentage: number) => {
    return ProfileCompletionService.isFunctionallyComplete(percentage);
  }, []);

  const isPerfectSetup = useCallback((percentage: number) => {
    return ProfileCompletionService.isPerfectSetup(percentage);
  }, []);

  return {
    getNavigationRoute,
    getSectionDisplayName,
    getCompletionColor,
    getCompletionStatusText,
    formatPercentage,
    isFunctionallyComplete,
    isPerfectSetup,
  };
}

/**
 * Hook for profile completion analytics and insights
 */
export function useProfileCompletionInsights() {
  const { completion } = useProfileCompletion();

  const insights = useMemo(() => {
    if (!completion) return null;

    const { breakdown, overallPercentage } = completion;
    
    // Find the section with the lowest completion
    const sections = Object.entries(breakdown);
    const lowestSection = sections.reduce((lowest, [key, section]) => {
      return section.percentage < lowest.percentage ? { key, ...section } : lowest;
    }, { key: '', percentage: 100, completed: true, details: '' });

    // Calculate how many sections are complete
    const completedSections = sections.filter(([_, section]) => section.completed).length;
    const totalSections = sections.length;

    // Estimate time to completion based on missing sections
    const incompleteSections = totalSections - completedSections;
    const estimatedMinutes = incompleteSections * 10; // Rough estimate: 10 minutes per section

    return {
      overallPercentage,
      completedSections,
      totalSections,
      incompleteSections,
      lowestSection: lowestSection.key ? lowestSection : null,
      estimatedCompletionTime: estimatedMinutes,
      isNearCompletion: overallPercentage >= 80,
      isPerfect: overallPercentage >= 100,
      nextPrioritySection: completion.nextSteps[0] || null,
      criticalIssues: completion.criticalMissing.length,
    };
  }, [completion]);

  return insights;
}

/**
 * Hook for profile completion progress tracking
 */
export function useProfileCompletionProgress() {
  const { completion } = useProfileCompletion();

  const progress = useMemo(() => {
    if (!completion) return null;

    const { breakdown } = completion;
    
    return {
      profilePicture: {
        ...breakdown.profilePicture,
        weight: 10,
        contribution: (breakdown.profilePicture.percentage * 10) / 100,
      },
      providerInfo: {
        ...breakdown.providerInfo,
        weight: 30,
        contribution: (breakdown.providerInfo.percentage * 30) / 100,
      },
      providingPlaces: {
        ...breakdown.providingPlaces,
        weight: 25,
        contribution: (breakdown.providingPlaces.percentage * 25) / 100,
      },
      services: {
        ...breakdown.services,
        weight: 20,
        contribution: (breakdown.services.percentage * 20) / 100,
      },
      queues: {
        ...breakdown.queues,
        weight: 15,
        contribution: (breakdown.queues.percentage * 15) / 100,
      },
    };
  }, [completion]);

  return progress;
}
