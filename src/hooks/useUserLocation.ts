import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useLocations } from './useLocations';
import { ErrorLogger } from '../lib/error-utils';

/**
 * User location information
 */
export interface UserLocationInfo {
  country: string;
  countryCode: string;
  isAlgeria: boolean;
  detectionMethod: 'profile' | 'provider_location' | 'geolocation' | 'ip' | 'default';
  confidence: 'high' | 'medium' | 'low';
}

/**
 * Geolocation position with country information
 */
interface GeolocationResult {
  country: string;
  countryCode: string;
  latitude: number;
  longitude: number;
}

/**
 * Hook for detecting user location from multiple sources
 * Priority: 1. User profile, 2. Provider locations, 3. Browser geolocation, 4. IP detection, 5. Default
 */
export const useUserLocation = () => {
  const [location, setLocation] = useState<UserLocationInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useAuth();
  const { data: locationsData } = useLocations();

  // For testing: Mock Algerian location if in development mode
  const shouldUseMockLocation = import.meta.env.VITE_MOCK_LOCATION === 'true' ||
                                (import.meta.env.DEV && import.meta.env.VITE_MOCK_ALGERIAN_USER === 'true');

  useEffect(() => {
    if (shouldUseMockLocation) {
      console.log('🇩🇿 Using mock Algerian location for testing');
      setLocation({
        country: 'Algeria',
        countryCode: 'DZ',
        isAlgeria: true,
        detectionMethod: 'mock',
        confidence: 'high',
      });
      setIsLoading(false);
      return;
    }
  }, [shouldUseMockLocation]);

  /**
   * Detect country from coordinates using reverse geocoding
   */
  const getCountryFromCoordinates = async (latitude: number, longitude: number): Promise<GeolocationResult | null> => {
    try {
      // Using a free geocoding service (you might want to use a more reliable one in production)
      const response = await fetch(
        `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`
      );
      
      if (!response.ok) {
        throw new Error('Geocoding service unavailable');
      }
      
      const data = await response.json();
      
      return {
        country: data.countryName || 'Unknown',
        countryCode: data.countryCode || '',
        latitude,
        longitude,
      };
    } catch (error) {
      console.warn('Reverse geocoding failed:', error);
      return null;
    }
  };

  /**
   * Get user location from browser geolocation API
   */
  const getLocationFromGeolocation = (): Promise<GeolocationResult | null> => {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        resolve(null);
        return;
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;
          const result = await getCountryFromCoordinates(latitude, longitude);
          resolve(result);
        },
        (error) => {
          console.warn('Geolocation failed:', error.message);
          resolve(null);
        },
        {
          timeout: 10000, // 10 seconds timeout
          enableHighAccuracy: false, // Faster, less accurate
        }
      );
    });
  };

  /**
   * Get user location from IP address (fallback method)
   */
  const getLocationFromIP = async (): Promise<GeolocationResult | null> => {
    try {
      // Using a free IP geolocation service
      const response = await fetch('https://ipapi.co/json/');
      
      if (!response.ok) {
        throw new Error('IP geolocation service unavailable');
      }
      
      const data = await response.json();
      
      return {
        country: data.country_name || 'Unknown',
        countryCode: data.country_code || '',
        latitude: data.latitude || 0,
        longitude: data.longitude || 0,
      };
    } catch (error) {
      console.warn('IP geolocation failed:', error);
      return null;
    }
  };

  /**
   * Detect user location from multiple sources
   */
  const detectUserLocation = async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      // Method 1: Check if user has profile data with country information
      // (This would require extending the user profile to include country)
      // For now, we'll skip this method

      // Method 2: Check provider locations for country information
      if (locationsData && locationsData.length > 0) {
        const primaryLocation = locationsData[0];
        if (primaryLocation.country) {
          const countryCode = getCountryCode(primaryLocation.country);
          setLocation({
            country: primaryLocation.country,
            countryCode,
            isAlgeria: countryCode === 'DZ' || primaryLocation.country.toLowerCase().includes('algeria'),
            detectionMethod: 'provider_location',
            confidence: 'high',
          });
          setIsLoading(false);
          return;
        }
      }

      // Method 3: Try browser geolocation
      const geoResult = await getLocationFromGeolocation();
      if (geoResult && geoResult.countryCode) {
        setLocation({
          country: geoResult.country,
          countryCode: geoResult.countryCode,
          isAlgeria: geoResult.countryCode === 'DZ',
          detectionMethod: 'geolocation',
          confidence: 'medium',
        });
        setIsLoading(false);
        return;
      }

      // Method 4: Try IP-based detection
      const ipResult = await getLocationFromIP();
      if (ipResult && ipResult.countryCode) {
        setLocation({
          country: ipResult.country,
          countryCode: ipResult.countryCode,
          isAlgeria: ipResult.countryCode === 'DZ',
          detectionMethod: 'ip',
          confidence: 'low',
        });
        setIsLoading(false);
        return;
      }

      // Method 5: Default fallback
      setLocation({
        country: 'Unknown',
        countryCode: '',
        isAlgeria: false,
        detectionMethod: 'default',
        confidence: 'low',
      });
    } catch (error) {
      ErrorLogger.log(error as Error, { context: 'useUserLocation.detectUserLocation' });
      setError('Failed to detect user location');
      
      // Set default location on error
      setLocation({
        country: 'Unknown',
        countryCode: '',
        isAlgeria: false,
        detectionMethod: 'default',
        confidence: 'low',
      });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Get country code from country name (basic mapping)
   */
  const getCountryCode = (countryName: string): string => {
    const countryMap: Record<string, string> = {
      'algeria': 'DZ',
      'united states': 'US',
      'canada': 'CA',
      'france': 'FR',
      'germany': 'DE',
      'united kingdom': 'GB',
      'spain': 'ES',
      'italy': 'IT',
      // Add more mappings as needed
    };

    const normalizedName = countryName.toLowerCase();
    return countryMap[normalizedName] || '';
  };

  /**
   * Force refresh location detection
   */
  const refreshLocation = () => {
    detectUserLocation();
  };

  // Run detection on mount and when dependencies change
  useEffect(() => {
    detectUserLocation();
  }, [user, locationsData]);

  return {
    location,
    isLoading,
    error,
    refreshLocation,
    isAlgeria: location?.isAlgeria || false,
    countryCode: location?.countryCode || '',
    detectionMethod: location?.detectionMethod || 'default',
    confidence: location?.confidence || 'low',
  };
};
