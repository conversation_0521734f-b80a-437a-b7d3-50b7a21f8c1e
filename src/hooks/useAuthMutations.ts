import { useMutation, useQuery } from '@tanstack/react-query';
import { AuthService } from '../services/auth.service';
import {
  OtpRequest,
  VerifyOtpRegisterRequest,
  PasswordResetRequest,
  PasswordResetVerifyRequest,
  PasswordResetConfirmRequest,
  ProviderCategory,
} from '../types';
import { ErrorLogger } from '../lib/error-utils';
import toast from 'react-hot-toast';
import { useAuth } from '../context/AuthContext';

/**
 * Hook for requesting email OTP
 */
export const useRequestEmailOtp = () => {
  return useMutation({
    mutationFn: (data: OtpRequest) => AuthService.requestEmailOtp(data),
    onSuccess: (data) => {
      toast.success(data.message || 'OTP sent to your email');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to send OTP';
      ErrorLogger.log(error, { context: 'requestEmailOtp' });
      toast.error(message);
    },
  });
};

/**
 * Hook for requesting phone OTP
 */
export const useRequestPhoneOtp = () => {
  return useMutation({
    mutationFn: (data: OtpRequest) => AuthService.requestPhoneOtp(data),
    onSuccess: (data) => {
      toast.success(data.message || 'OTP sent to your phone');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to send OTP';
      ErrorLogger.log(error, { context: 'requestPhoneOtp' });
      toast.error(message);
    },
  });
};

/**
 * Hook for verifying OTP and completing registration
 */
export const useVerifyOtpRegister = () => {
  const { setAuthenticatedUser } = useAuth();

  return useMutation({
    mutationFn: (data: VerifyOtpRegisterRequest) => AuthService.verifyOtpAndRegister(data),
    onSuccess: async (data) => {
      // Store authentication data
      const token = data.sessionId;
      AuthService.storeTokens(token);
      AuthService.storeUserData(data.user, data.provider);

      // Update AuthContext state to reflect the login immediately
      await setAuthenticatedUser(data.user, data.provider, token);

      toast.success('Registration successful! Welcome to Provider Dashboard.');
    },
    onError: (error: any) => {
      const message = error?.message || 'Registration failed';
      ErrorLogger.log(error, { context: 'verifyOtpRegister' });
      toast.error(message);
    },
  });
};

/**
 * Hook for password reset request
 */
export const usePasswordResetRequest = () => {
  return useMutation({
    mutationFn: (data: PasswordResetRequest) => AuthService.requestPasswordReset(data),
    onSuccess: (data) => {
      toast.success(data.message || 'Password reset OTP sent');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to send password reset OTP';
      ErrorLogger.log(error, { context: 'passwordResetRequest' });
      toast.error(message);
    },
  });
};

/**
 * Hook for password reset OTP verification
 */
export const usePasswordResetVerify = () => {
  return useMutation({
    mutationFn: (data: PasswordResetVerifyRequest) => AuthService.verifyPasswordResetOtp(data),
    onSuccess: (data) => {
      toast.success(data.message || 'OTP verified successfully. Use the reset token to set your new password.');
    },
    onError: (error: any) => {
      const message = error?.message || 'Invalid OTP';
      ErrorLogger.log(error, { context: 'passwordResetVerify' });
      toast.error(message);
    },
  });
};

/**
 * Hook for password reset confirmation
 */
export const usePasswordReset = () => {
  return useMutation({
    mutationFn: (data: PasswordResetConfirmRequest) => AuthService.resetPassword(data),
    onSuccess: (data) => {
      toast.success(data.message || 'Password reset successfully');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to reset password';
      ErrorLogger.log(error, { context: 'passwordReset' });
      toast.error(message);
    },
  });
};

/**
 * Hook for fetching provider categories
 */
export const useProviderCategories = () => {
  // Get current language from localStorage (fallback approach for contexts where LanguageContext might not be available)
  const currentLanguage = localStorage.getItem('dalti-language') || 'en';

  return useQuery({
    queryKey: ['providerCategories', currentLanguage],
    queryFn: () => AuthService.getProviderCategories(currentLanguage),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchProviderCategories' });
    },
  });
};

/**
 * Hook for token refresh
 */
export const useRefreshToken = () => {
  return useMutation({
    mutationFn: (refreshToken: string) => AuthService.refreshToken(refreshToken),
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'refreshToken' });
      // Don't show toast for token refresh errors as they're handled automatically
    },
  });
};
