/**
 * Provider related types
 */

import { User, ProviderCategory, File } from './auth';

// Forward declaration to avoid circular imports
export interface ServiceCategory {
  id: number;
  title: string;
  sProviderId: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface Provider {
  id: number;
  userId: string;
  providerCategoryId?: number;
  title?: string;
  phone?: string;
  presentation?: string;
  isVerified: boolean;
  isSetupComplete: boolean;
  logoId?: string;
  averageRating?: number;
  totalReviews: number;
  createdAt: string;
  updatedAt: string;
  
  // Relations
  user?: User;
  category?: ProviderCategory;
  logo?: File;
  providingPlaces?: Location[];
  services?: Service[];
  serviceCategories?: ServiceCategory[];
  customerFolders?: CustomerFolder[];
  queues?: Queue[];
  reviewsReceived?: Review[];
}

export interface ProviderProfile {
  id: number;
  userId: string;
  title?: string;
  phone?: string;
  presentation?: string;
  isVerified: boolean;
  isSetupComplete: boolean;
  category?: {
    id: number;
    title: string;
  };
  averageRating?: number;
  totalReviews: number;
}

export interface ProviderProfileUpdateRequest {
  title?: string;
  phone?: string;
  presentation?: string;
  providerCategoryId?: number;
}

// Profile completion types moved to profile-completion.ts
// Import them from there: import { ProfileCompletionResult } from './profile-completion';

// Forward declarations for related types
export interface Location {
  id: number;
  sProviderId: number;
  name: string;
  shortName?: string;
  address?: string;
  city?: string;
  mobile?: string;
  isMobileHidden: boolean;
  fax?: string;
  floor?: string;
  parking: boolean;
  elevator: boolean;
  handicapAccess: boolean;
  timezone?: string;
  detailedAddressId?: number;
  createdAt: string;
  updatedAt: string;
  
  // Relations
  provider?: Provider;
  appointments?: Appointment[];
  openings?: Opening[];
  queues?: Queue[];
  detailedAddress?: Address;
}

export interface Service {
  id: number;
  sProviderId: number;
  serviceCategoryId?: number;
  title: string;
  duration: number;
  price: number;
  pointsRequirements: number;
  isPublic: boolean;
  deliveryType: 'at_location' | 'at_customer' | 'both';
  servedRegions: string[];
  description?: string;
  color: string;
  acceptOnline: boolean;
  acceptNew: boolean;
  notificationOn: boolean;
  createdAt: string;
  updatedAt: string;
  
  // Relations
  provider?: Provider;
  category?: ServiceCategory;
  appointments?: Appointment[];
  queueServices?: QueueService[];
}

export interface Queue {
  id: number;
  sProviderId: number;
  sProvidingPlaceId: number;
  title: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  
  // Relations
  provider?: Provider;
  location?: Location;
  services?: Service[];
  openings?: QueueOpening[];
  appointments?: Appointment[];
}

export interface CustomerFolder {
  id: number;
  sProviderId: number;
  userId: string;
  notes?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  
  // Relations
  provider?: Provider;
  customer?: User;
  appointments?: Appointment[];
}

// Additional types will be defined in separate files
export interface Appointment {
  id: number;
  // Will be fully defined in appointment.ts
}

export interface Review {
  id: number;
  // Will be fully defined in review.ts
}

export interface Opening {
  id: number;
  // Will be fully defined in schedule.ts
}

export interface QueueOpening {
  id: number;
  // Will be fully defined in queue.ts
}

export interface QueueService {
  id: number;
  // Will be fully defined in queue.ts
}

export interface Address {
  id: number;
  // Will be fully defined in location.ts
}
