/**
 * Service related types
 */

import { Provider } from './provider';

export interface Service {
  id: number;
  sProviderId: number;
  serviceCategoryId?: number;
  title: string;
  duration: number; // in minutes
  price: number;
  pointsRequirements: number;
  isPublic: boolean;
  deliveryType: 'at_location' | 'at_customer' | 'both';
  servedRegions: string[];
  description?: string;
  color: string;
  acceptOnline: boolean;
  acceptNew: boolean;
  notificationOn: boolean;
  createdAt: string;
  updatedAt: string;
  
  // Relations
  provider?: Provider;
  category?: ServiceCategory;
  appointments?: Appointment[];
  queueServices?: QueueService[];
}

export interface ServiceCreateRequest {
  title: string;
  duration: number;
  price: number;
  pointsRequirements?: number;
  isPublic?: boolean;
  deliveryType: 'at_location' | 'at_customer' | 'both';
  servedRegions?: string[];
  description?: string;
  color: string;
  acceptOnline?: boolean;
  acceptNew?: boolean;
  notificationOn?: boolean;
  serviceCategoryId?: number;
}

export interface ServiceUpdateRequest extends Partial<ServiceCreateRequest> {
  id: number;
}

export interface ServiceCategory {
  id: number;
  title: string;
  sProviderId: number;
  createdAt?: string;
  updatedAt?: string;

  // Relations
  provider?: Provider;
  services?: Service[];
}

export interface ServiceCategoryCreateRequest {
  title: string;
}

export interface ServiceCategoryUpdateRequest extends Partial<ServiceCategoryCreateRequest> {
  id: number;
}

export interface ServiceFilters {
  categoryId?: number;
  isPublic?: boolean;
  deliveryType?: 'at_location' | 'at_customer' | 'both';
  acceptOnline?: boolean;
  acceptNew?: boolean;
  minPrice?: number;
  maxPrice?: number;
  minDuration?: number;
  maxDuration?: number;
  search?: string;
}

export interface ServiceStats {
  totalServices: number;
  activeServices: number;
  averagePrice: number;
  averageDuration: number;
  mostPopularService?: Service;
  recentlyAdded: Service[];
}

// Forward declarations
export interface Appointment {
  id: number;
  serviceId?: number;
  service?: Service;
  // Other appointment fields...
}

export interface QueueService {
  id: number;
  queueId: number;
  serviceId: number;
  service?: Service;
  // Other queue service fields...
}
