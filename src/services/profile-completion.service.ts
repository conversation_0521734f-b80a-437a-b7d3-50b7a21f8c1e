/**
 * Profile completion service for managing completion status and calculations
 */

import { apiClient } from '../lib/api-client';
import { config } from '../lib/config';
import {
  ProfileCompletionResult,
  ProfileCompletionResponse,
  ProfileCompletionData,
  CompleteSetupRequest,
  CompleteSetupResponse,
  DEFAULT_CACHE_CONFIG,
  PROFILE_COMPLETION_STORAGE_KEYS,
} from '../types/profile-completion';
import { calculateProfileCompletion, calculateProfileCompletionSafe } from '../utils/profile-completion';

/**
 * Cache for profile completion results
 */
interface CompletionCache {
  result: ProfileCompletionResult;
  timestamp: number;
  ttl: number;
}

const completionCache = new Map<string, CompletionCache>();

/**
 * Profile completion service
 */
export class ProfileCompletionService {
  /**
   * Get profile completion status from API
   */
  static async getProfileCompletion(): Promise<ProfileCompletionResult> {
    const response = await apiClient.get<ProfileCompletionResponse>(
      config.endpoints.provider.profileCompletion
    );
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to fetch profile completion');
    }
    
    return response.data.data;
  }

  /**
   * Get cached profile completion or fetch from API
   */
  static async getCachedProfileCompletion(userId: string, forceRefresh = false): Promise<ProfileCompletionResult> {
    if (!forceRefresh) {
      const cached = this.getCachedCompletion(userId);
      if (cached) {
        return cached;
      }
    }

    const result = await this.getProfileCompletion();
    this.setCachedCompletion(userId, result);
    return result;
  }

  /**
   * Calculate profile completion from local data
   */
  static calculateCompletion(data: ProfileCompletionData): ProfileCompletionResult {
    return calculateProfileCompletion(data);
  }

  /**
   * Calculate profile completion safely (with error handling)
   */
  static calculateCompletionSafe(data: Partial<ProfileCompletionData>): ProfileCompletionResult {
    return calculateProfileCompletionSafe(data);
  }

  /**
   * Complete provider setup with all data at once
   */
  static async completeSetup(setupData: CompleteSetupRequest): Promise<CompleteSetupResponse> {
    const response = await apiClient.post<CompleteSetupResponse>(
      '/api/auth/provider/complete-setup',
      setupData
    );
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to complete setup');
    }
    
    // Clear cache after successful setup
    this.clearAllCache();
    
    return response.data;
  }

  /**
   * Check if profile completion card is dismissed
   */
  static isCardDismissed(): boolean {
    try {
      const dismissed = localStorage.getItem(PROFILE_COMPLETION_STORAGE_KEYS.DISMISSED_CARD);
      return dismissed === 'true';
    } catch {
      return false;
    }
  }

  /**
   * Mark profile completion card as dismissed
   */
  static dismissCard(): void {
    try {
      localStorage.setItem(PROFILE_COMPLETION_STORAGE_KEYS.DISMISSED_CARD, 'true');
    } catch (error) {
      console.warn('Failed to save card dismissal state:', error);
    }
  }

  /**
   * Reset card dismissal state (for testing/development)
   */
  static resetCardDismissal(): void {
    try {
      localStorage.removeItem(PROFILE_COMPLETION_STORAGE_KEYS.DISMISSED_CARD);
    } catch (error) {
      console.warn('Failed to reset card dismissal state:', error);
    }
  }

  /**
   * Get cached completion result
   */
  private static getCachedCompletion(userId: string): ProfileCompletionResult | null {
    const cached = completionCache.get(userId);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.result;
    }
    return null;
  }

  /**
   * Set cached completion result
   */
  private static setCachedCompletion(
    userId: string,
    result: ProfileCompletionResult,
    ttl: number = DEFAULT_CACHE_CONFIG.ttl
  ): void {
    completionCache.set(userId, {
      result,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * Clear cache for specific user
   */
  static clearCache(userId: string): void {
    completionCache.delete(userId);
  }

  /**
   * Clear all cached completion results
   */
  static clearAllCache(): void {
    completionCache.clear();
  }

  /**
   * Get cache statistics (for debugging)
   */
  static getCacheStats(): { size: number; entries: Array<{ userId: string; age: number }> } {
    const now = Date.now();
    const entries = Array.from(completionCache.entries()).map(([userId, cache]) => ({
      userId,
      age: now - cache.timestamp
    }));

    return {
      size: completionCache.size,
      entries
    };
  }

  /**
   * Validate completion data structure
   */
  static validateCompletionData(data: any): data is ProfileCompletionData {
    return (
      data &&
      typeof data === 'object' &&
      data.user &&
      typeof data.user === 'object' &&
      data.provider &&
      typeof data.provider === 'object'
    );
  }

  /**
   * Get navigation route for completion section
   */
  static getNavigationRoute(section: string, isSetupMode: boolean = false): string | null {
    const setupNavigationMap: Record<string, string> = {
      'profilePicture': '/setup/profile',
      'providerInfo': '/setup/profile',
      'providingPlaces': '/setup/locations',
      'services': '/setup/services',
      'queues': '/queues' // No setup route for queues yet
    };

    const normalNavigationMap: Record<string, string> = {
      'profilePicture': '/profile',
      'providerInfo': '/profile',
      'providingPlaces': '/locations',
      'services': '/services',
      'queues': '/queues'
    };

    const navigationMap = isSetupMode ? setupNavigationMap : normalNavigationMap;
    return navigationMap[section] || null;
  }

  /**
   * Get section display name
   */
  static getSectionDisplayName(section: string): string {
    const displayNames: Record<string, string> = {
      'profilePicture': 'Profile Picture',
      'providerInfo': 'Business Information',
      'providingPlaces': 'Locations',
      'services': 'Services',
      'queues': 'Queues'
    };

    return displayNames[section] || section;
  }

  /**
   * Get completion status color
   */
  static getCompletionColor(percentage: number): string {
    if (percentage >= 100) return 'green';
    if (percentage >= 80) return 'blue';
    if (percentage >= 50) return 'yellow';
    if (percentage >= 25) return 'orange';
    return 'red';
  }

  /**
   * Get completion status text
   */
  static getCompletionStatusText(percentage: number): string {
    if (percentage >= 100) return 'Complete';
    if (percentage >= 80) return 'Almost Complete';
    if (percentage >= 50) return 'In Progress';
    if (percentage >= 25) return 'Getting Started';
    return 'Not Started';
  }

  /**
   * Format completion percentage for display
   */
  static formatPercentage(percentage: number): string {
    return `${Math.round(percentage)}%`;
  }

  /**
   * Check if setup is functionally complete (80% threshold)
   */
  static isFunctionallyComplete(percentage: number): boolean {
    return percentage >= 80;
  }

  /**
   * Check if setup is perfect (100% complete)
   */
  static isPerfectSetup(percentage: number): boolean {
    return percentage >= 100;
  }
}
