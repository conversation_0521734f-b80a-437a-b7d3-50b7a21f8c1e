import { apiClient } from '../lib/api-client';

export interface DashboardMetrics {
  todayAppointments: {
    total: number;
    completed: number;
    pending: number;
    confirmed: number;
    cancelled: number;
  };
  weeklyStats: {
    appointments: number;
    revenue: number;
    newCustomers: number;
  };
  monthlyStats: {
    appointments: number;
    revenue: number;
    newCustomers: number;
  };
}

export interface TodayAppointment {
  id: number;
  customer: {
    firstName: string;
    lastName: string;
  };
  service: {
    title: string;
    duration: number;
  };
  expectedAppointmentStartTime: string;
  status: string;
}

export interface TodayAppointmentsResponse {
  appointments: TodayAppointment[];
}

export interface RevenueChartData {
  labels: string[];
  data: number[];
}

export class DashboardService {
  /**
   * Get dashboard metrics for the authenticated provider
   */
  static async getDashboardMetrics(): Promise<DashboardMetrics> {
    const response = await apiClient.get('/api/auth/providers/dashboard/metrics');
    return response.data;
  }

  /**
   * Get today's appointments for the authenticated provider
   */
  static async getTodayAppointments(): Promise<TodayAppointmentsResponse> {
    const response = await apiClient.get('/api/auth/providers/appointments/today');
    return response.data;
  }

  /**
   * Get revenue chart data for the authenticated provider
   * @param period - 'week' or 'month'
   */
  static async getRevenueChart(period: 'week' | 'month' = 'week'): Promise<RevenueChartData> {
    const response = await apiClient.get(`/api/auth/providers/dashboard/revenue-chart?period=${period}`);
    return response.data;
  }
}
