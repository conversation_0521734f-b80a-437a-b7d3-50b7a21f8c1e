import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { UserService } from '../user.service';
import { apiClient } from '../../lib/api-client';
import { LanguageCode } from '../../context/LanguageContext';

// Mock the API client
vi.mock('../../lib/api-client', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
    delete: vi.fn(),
  },
}));

// Mock config
vi.mock('../../lib/config', () => ({
  config: {
    endpoints: {
      user: {
        profilePicture: '/api/auth/user/profile-picture',
        updatePreferredLanguage: '/api/auth/user/update-prefered-language',
      },
    },
  },
}));

describe('UserService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('updatePreferredLanguage', () => {
    it('should successfully update preferred language', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'Language preference updated successfully',
          data: {
            preferedLanguage: 'EN' as LanguageCode,
          },
        },
      };

      (apiClient.post as Mock).mockResolvedValue(mockResponse);

      await UserService.updatePreferredLanguage('en');

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/auth/user/update-prefered-language',
        {
          preferedLanguage: 'EN',
        }
      );
    });

    it('should convert language code to uppercase', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'Language preference updated successfully',
        },
      };

      (apiClient.post as Mock).mockResolvedValue(mockResponse);

      await UserService.updatePreferredLanguage('fr');

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/auth/user/update-prefered-language',
        {
          preferedLanguage: 'FR',
        }
      );
    });

    it('should handle Arabic language code', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'Language preference updated successfully',
        },
      };

      (apiClient.post as Mock).mockResolvedValue(mockResponse);

      await UserService.updatePreferredLanguage('ar');

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/auth/user/update-prefered-language',
        {
          preferedLanguage: 'AR',
        }
      );
    });

    it('should throw error when API returns failure', async () => {
      const mockResponse = {
        data: {
          success: false,
          message: 'Invalid language code',
        },
      };

      (apiClient.post as Mock).mockResolvedValue(mockResponse);

      await expect(UserService.updatePreferredLanguage('en')).rejects.toThrow(
        'Invalid language code'
      );
    });

    it('should throw default error when API returns failure without message', async () => {
      const mockResponse = {
        data: {
          success: false,
        },
      };

      (apiClient.post as Mock).mockResolvedValue(mockResponse);

      await expect(UserService.updatePreferredLanguage('en')).rejects.toThrow(
        'Failed to update preferred language'
      );
    });

    it('should handle network errors', async () => {
      const networkError = new Error('Network error');
      (apiClient.post as Mock).mockRejectedValue(networkError);

      await expect(UserService.updatePreferredLanguage('en')).rejects.toThrow(
        'Network error'
      );
    });
  });

  describe('getProfilePicture', () => {
    it('should successfully get profile picture', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            hasProfilePicture: true,
            profilePicture: {
              id: '123',
              name: 'profile.jpg',
              type: 'image/jpeg',
              key: 'profile-pictures/123.jpg',
              downloadUrl: 'https://example.com/profile.jpg',
              createdAt: '2024-01-01T00:00:00Z',
            },
          },
        },
      };

      (apiClient.get as Mock).mockResolvedValue(mockResponse);

      const result = await UserService.getProfilePicture();

      expect(result).toEqual(mockResponse.data.data);
      expect(apiClient.get).toHaveBeenCalledWith('/api/auth/user/profile-picture');
    });
  });
});
