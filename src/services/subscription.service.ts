import { apiClient } from '../lib/api-client';
import { config } from '../lib/config';
import {
  SubscriptionPlansResponse,
  UserPaymentStatusResponse,
  CheckoutSessionRequest,
  CheckoutSessionResponse,
  CustomerPortalResponse,
  UsageStatisticsResponse,
  SubscriptionFilters,
  UsagePeriod,
  ChargilyCheckoutResponse,
} from '../types';
import { PaymentMethodsService } from './payment-methods.service';
import { parsePaymentError, logPaymentError } from '../utils/payment-errors.utils';

/**
 * Subscription and Payment service for LemonSqueezy integration
 * Handles all subscription-related API operations
 */
export class SubscriptionService {
  /**
   * Get all available subscription plans
   * Public endpoint - no authentication required
   */
  static async getSubscriptionPlans(): Promise<SubscriptionPlansResponse> {
    const response = await apiClient.get<SubscriptionPlansResponse>(
      config.endpoints.payment.plans
    );
    return response.data;
  }

  /**
   * Get current user's subscription status and payment information
   * Requires authentication
   */
  static async getUserPaymentStatus(): Promise<UserPaymentStatusResponse> {
    const response = await apiClient.get<UserPaymentStatusResponse>(
      config.endpoints.payment.status
    );
    return response.data;
  }

  /**
   * Create a checkout session for subscribing to a plan
   * Supports multiple payment processors (LemonSqueezy, Chargily)
   * Requires authentication
   */
  static async createCheckoutSession(data: CheckoutSessionRequest): Promise<CheckoutSessionResponse | ChargilyCheckoutResponse> {
    try {
      // Route to Chargily Pay if specified
      if (data.paymentProcessor === 'chargily') {
        return PaymentMethodsService.createChargilyCheckout(data);
      }

      // Default to LemonSqueezy for backward compatibility
      const response = await apiClient.post<CheckoutSessionResponse>(
        config.endpoints.payment.checkout,
        data
      );
      return response.data;
    } catch (error) {
      const processor = data.paymentProcessor || 'lemonsqueezy';
      const paymentError = parsePaymentError(error, processor);
      logPaymentError(paymentError, 'createCheckoutSession');
      throw paymentError;
    }
  }

  /**
   * Get customer portal URL for subscription management
   * Requires authentication and active subscription
   */
  static async getCustomerPortalUrl(): Promise<CustomerPortalResponse> {
    const response = await apiClient.get<CustomerPortalResponse>(
      config.endpoints.payment.customerPortal
    );
    return response.data;
  }

  /**
   * Get user's usage statistics and limits
   * Requires authentication
   */
  static async getUsageStatistics(filters?: SubscriptionFilters): Promise<UsageStatisticsResponse> {
    const params: Record<string, any> = {};
    
    if (filters?.period) {
      params.period = filters.period;
    }

    const response = await apiClient.get<UsageStatisticsResponse>(
      config.endpoints.payment.usage,
      { params }
    );
    return response.data;
  }

  /**
   * Get usage statistics for a specific period
   * Convenience method for common usage patterns
   */
  static async getUsageForPeriod(period: UsagePeriod): Promise<UsageStatisticsResponse> {
    return this.getUsageStatistics({ period });
  }

  /**
   * Check if user has an active subscription
   * Convenience method that extracts subscription status
   */
  static async hasActiveSubscription(): Promise<boolean> {
    try {
      const response = await this.getUserPaymentStatus();
      return response.data.subscription.isActive;
    } catch (error) {
      console.error('Error checking subscription status:', error);
      return false;
    }
  }

  /**
   * Check if user has customer portal access
   * Convenience method for portal availability
   */
  static async hasCustomerPortalAccess(): Promise<boolean> {
    try {
      const response = await this.getUserPaymentStatus();
      return response.data.hasCustomerPortal;
    } catch (error) {
      console.error('Error checking customer portal access:', error);
      return false;
    }
  }

  /**
   * Get current user's credit balance
   * Convenience method for credit checking
   */
  static async getCurrentCredits(): Promise<number> {
    try {
      const response = await this.getUserPaymentStatus();
      return response.data.user.credits;
    } catch (error) {
      console.error('Error getting current credits:', error);
      return 0;
    }
  }

  /**
   * Get current user's queue limit
   * Convenience method for queue limit checking
   */
  static async getCurrentQueueLimit(): Promise<number> {
    try {
      const response = await this.getUserPaymentStatus();
      return response.data.user.queues;
    } catch (error) {
      console.error('Error getting queue limit:', error);
      return 0;
    }
  }

  /**
   * Check if user is approaching credit limit
   * Utility method for usage warnings
   */
  static async isApproachingCreditLimit(threshold: number = 0.2): Promise<boolean> {
    try {
      const usageResponse = await this.getUsageStatistics();
      const { current, limits } = usageResponse.data;
      
      if (limits.credits === 0) return false;
      
      const usagePercentage = current.credits / limits.credits;
      return usagePercentage >= (1 - threshold);
    } catch (error) {
      console.error('Error checking credit limit:', error);
      return false;
    }
  }

  /**
   * Check if user is at queue limit
   * Utility method for queue creation restrictions
   */
  static async isAtQueueLimit(): Promise<boolean> {
    try {
      const usageResponse = await this.getUsageStatistics();
      const { current, limits } = usageResponse.data;
      
      return current.queues >= limits.queues;
    } catch (error) {
      console.error('Error checking queue limit:', error);
      return true; // Err on the side of caution
    }
  }

  /**
   * Get subscription plan by ID
   * Utility method to find specific plan details
   */
  static async getSubscriptionPlanById(planId: string) {
    try {
      const response = await this.getSubscriptionPlans();
      return response.data.plans.find(plan => plan.id === planId);
    } catch (error) {
      console.error('Error getting subscription plan:', error);
      return null;
    }
  }

  /**
   * Redirect to checkout URL
   * Utility method for seamless checkout flow
   */
  static redirectToCheckout(checkoutUrl: string): void {
    window.location.href = checkoutUrl;
  }

  /**
   * Create checkout session and redirect to payment processor
   * Unified method for complete checkout flow
   */
  static async createCheckoutAndRedirect(data: CheckoutSessionRequest): Promise<void> {
    try {
      const response = await this.createCheckoutSession(data);

      // Handle different response formats
      if ('sessionUrl' in response.data) {
        // LemonSqueezy response format
        this.redirectToCheckout(response.data.sessionUrl);
      } else if ('checkoutUrl' in response.data) {
        // Chargily response format
        this.redirectToCheckout(response.data.checkoutUrl);
      } else {
        throw new Error('Invalid checkout response format');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw error;
    }
  }

  /**
   * Redirect to customer portal
   * Utility method for seamless portal access
   */
  static async redirectToCustomerPortal(): Promise<void> {
    try {
      const response = await this.getCustomerPortalUrl();
      if (response.success && response.data?.customerPortalUrl) {
        window.location.href = response.data.customerPortalUrl;
      } else {
        throw new Error(response.message || 'Customer portal not available');
      }
    } catch (error) {
      console.error('Error redirecting to customer portal:', error);
      throw error;
    }
  }
}
