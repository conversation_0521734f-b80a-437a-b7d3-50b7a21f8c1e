import { io, Socket } from 'socket.io-client';
import { config } from '../lib/config';
import { TokenManager } from '../lib/api-client';
import { Appointment, AppointmentStatus } from '../types';

/**
 * WebSocket events from server to client
 */
interface ServerToClientEvents {
  queueStateUpdate: (data: QueueStateUpdate) => void;
  appointmentStatusChanged: (data: AppointmentStatusUpdate) => void;
  newAppointmentBooked: (data: NewAppointmentNotification) => void;
  appointmentCanceled: (data: AppointmentCancellation) => void;
  appointmentRescheduled: (data: AppointmentReschedule) => void;
  providerCreditsUpdated: (data: ProviderCreditsUpdate) => void;
}

/**
 * WebSocket events from client to server
 */
interface ClientToServerEvents {
  requestQueueStatus: (queueId?: number) => void;
  notifyQueueChange: (payload: { queueId: number }) => void;
  joinProviderRoom: (providerId: string) => void;
  leaveProviderRoom: (providerId: string) => void;
}

/**
 * Event payload interfaces
 */
export interface QueueStateUpdate {
  queueId: number;
  timestamp: Date;
  state: {
    currentAppointment?: Appointment;
    upcomingAppointments: Appointment[];
    estimatedWaitTime: number;
    queueLength: number;
  };
}

export interface AppointmentStatusUpdate {
  appointmentId: number;
  previousStatus: AppointmentStatus;
  newStatus: AppointmentStatus;
  timestamp: Date;
  changedBy: string;
  reason?: string;
}

export interface NewAppointmentNotification {
  appointmentId: number;
  queueId: number;
  customerName: string;
  serviceName: string;
  expectedStartTime: Date;
  position: number;
}

export interface AppointmentCancellation {
  appointmentId: number;
  queueId: number;
  reason?: string;
  timestamp: Date;
}

export interface AppointmentReschedule {
  appointmentId: number;
  oldStartTime: Date;
  newStartTime: Date;
  timestamp: Date;
}

export interface ProviderCreditsUpdate {
  providerId: string;
  newCredits: number;
  change: number;
  reason: string;
}

/**
 * WebSocket service for real-time updates
 */
export class WebSocketService {
  private socket: Socket<ServerToClientEvents, ClientToServerEvents> | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;

  /**
   * Connect to WebSocket server
   */
  connect(providerId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        return;
      }

      this.isConnecting = true;

      try {
        const token = TokenManager.getToken();
        
        this.socket = io(config.api.baseUrl, {
          auth: {
            token,
            providerId,
          },
          transports: ['websocket', 'polling'],
          timeout: 10000,
          reconnection: true,
          reconnectionAttempts: this.maxReconnectAttempts,
          reconnectionDelay: this.reconnectDelay,
        });

        this.socket.on('connect', () => {
          console.log('✅ WebSocket connected');
          this.reconnectAttempts = 0;
          this.isConnecting = false;
          
          // Join provider-specific room
          this.socket?.emit('joinProviderRoom', providerId);
          
          resolve();
        });

        this.socket.on('disconnect', (reason) => {
          console.log('❌ WebSocket disconnected:', reason);
          this.isConnecting = false;
        });

        this.socket.on('connect_error', (error) => {
          console.error('❌ WebSocket connection error:', error);
          this.isConnecting = false;
          this.reconnectAttempts++;
          
          if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            reject(new Error('Failed to connect to WebSocket after maximum attempts'));
          }
        });

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  /**
   * Check if WebSocket is connected
   */
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Subscribe to queue state updates
   */
  onQueueStateUpdate(callback: (data: QueueStateUpdate) => void): void {
    this.socket?.on('queueStateUpdate', callback);
  }

  /**
   * Subscribe to appointment status changes
   */
  onAppointmentStatusChanged(callback: (data: AppointmentStatusUpdate) => void): void {
    this.socket?.on('appointmentStatusChanged', callback);
  }

  /**
   * Subscribe to new appointment notifications
   */
  onNewAppointmentBooked(callback: (data: NewAppointmentNotification) => void): void {
    this.socket?.on('newAppointmentBooked', callback);
  }

  /**
   * Subscribe to appointment cancellations
   */
  onAppointmentCanceled(callback: (data: AppointmentCancellation) => void): void {
    this.socket?.on('appointmentCanceled', callback);
  }

  /**
   * Subscribe to appointment reschedules
   */
  onAppointmentRescheduled(callback: (data: AppointmentReschedule) => void): void {
    this.socket?.on('appointmentRescheduled', callback);
  }

  /**
   * Subscribe to provider credits updates
   */
  onProviderCreditsUpdated(callback: (data: ProviderCreditsUpdate) => void): void {
    this.socket?.on('providerCreditsUpdated', callback);
  }

  /**
   * Request current queue status
   */
  requestQueueStatus(queueId?: number): void {
    this.socket?.emit('requestQueueStatus', queueId);
  }

  /**
   * Notify about queue changes
   */
  notifyQueueChange(queueId: number): void {
    this.socket?.emit('notifyQueueChange', { queueId });
  }

  /**
   * Remove all event listeners
   */
  removeAllListeners(): void {
    this.socket?.removeAllListeners();
  }

  /**
   * Remove specific event listener
   */
  off(event: keyof ServerToClientEvents, callback?: Function): void {
    if (callback) {
      this.socket?.off(event, callback as any);
    } else {
      this.socket?.off(event);
    }
  }
}

// Export singleton instance
export const webSocketService = new WebSocketService();
