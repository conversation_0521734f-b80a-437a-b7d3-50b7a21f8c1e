import { apiClient } from '../lib/api-client';
import { config } from '../lib/config';
import { UploadUrlResponse } from '../utils/s3-upload.utils';
import {
  Provider,
  ProviderProfile,
  ProviderProfileUpdateRequest,
  ProfileCompletionResult,
  ProfileCompletionResponse,
  CompleteSetupRequest,
  CompleteSetupResponse,
} from '../types';

/**
 * Provider logo response types
 */
export interface ProviderLogoResponse {
  provider: {
    id: number;
    title: string;
    hasLogo: boolean;
  };
  logo?: {
    id: string;
    name: string;
    type: string;
    key: string;
    downloadUrl: string;
    createdAt: string;
  };
}

export interface ProviderLogoApiResponse {
  success: boolean;
  data: ProviderLogoResponse;
}

/**
 * Provider service for profile management operations
 */
export class ProviderService {
  /**
   * Get provider profile
   */
  static async getProfile(): Promise<ProviderProfile> {
    const response = await apiClient.get<{
      success: boolean;
      data: ProviderProfile;
      message: string;
    }>(config.endpoints.provider.profile);

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to fetch provider profile');
    }

    return response.data.data;
  }

  /**
   * Update provider profile
   */
  static async updateProfile(data: ProviderProfileUpdateRequest): Promise<Provider> {
    const response = await apiClient.put<Provider>(
      config.endpoints.provider.profile,
      data
    );
    return response.data;
  }

  /**
   * Get profile completion status
   */
  static async getProfileCompletion(): Promise<ProfileCompletionResult> {
    const response = await apiClient.get<ProfileCompletionResponse>(
      config.endpoints.provider.profileCompletion
    );

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to fetch profile completion');
    }

    return response.data.data;
  }

  /**
   * Complete provider setup with all data at once (for mobile apps)
   */
  static async completeSetup(setupData: CompleteSetupRequest): Promise<CompleteSetupResponse> {
    const response = await apiClient.post<CompleteSetupResponse>(
      '/api/auth/provider/complete-setup',
      setupData
    );

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to complete setup');
    }

    return response.data;
  }

  /**
   * Generate upload URL for provider logo
   * Phase 1 of the two-phase upload workflow
   */
  static async generateLogoUploadUrl(
    fileName: string,
    fileType: string
  ): Promise<UploadUrlResponse> {
    const response = await apiClient.post<{
      success: boolean;
      message: string;
      data: UploadUrlResponse;
    }>(
      config.endpoints.provider.logo,
      {
        fileName,
        fileType,
      }
    );

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to generate upload URL');
    }

    return response.data.data;
  }

  /**
   * Get provider logo information
   */
  static async getLogo(): Promise<ProviderLogoResponse> {
    const response = await apiClient.get<ProviderLogoApiResponse>(
      config.endpoints.provider.logoMobile
    );

    if (!response.data.success) {
      throw new Error('Failed to fetch provider logo');
    }

    return response.data.data;
  }

  /**
   * Delete provider logo
   */
  static async deleteLogo(): Promise<void> {
    const response = await apiClient.delete<{
      success: boolean;
      message: string;
    }>(config.endpoints.provider.logo);

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to delete logo');
    }
  }
}
