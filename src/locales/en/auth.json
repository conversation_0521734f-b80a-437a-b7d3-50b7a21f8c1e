{"signIn": {"title": "Sign In", "subtitle": "Welcome back! Please sign in to your account", "email": "Email Address", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot your password?", "signInButton": "Sign In", "noAccount": "Don't have an account?", "signUpLink": "Sign up here", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password"}, "signUp": {"title": "Create Account", "subtitle": "Join us today! Create your provider account", "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "phone": "Phone Number", "businessName": "Business Name", "password": "Password", "confirmPassword": "Confirm Password", "category": "Service Category", "agreeToTerms": "I agree to the Terms of Service and Privacy Policy", "signUpButton": "Create Account", "sendingCode": "Sending verification code...", "haveAccount": "Already have an account?", "signInLink": "Sign in here", "validation": {"firstNameMin": "First name must be at least 2 characters", "lastNameMin": "Last name must be at least 2 characters", "emailInvalid": "Please enter a valid email address", "phoneMin": "Please enter a valid phone number", "businessNameMin": "Business name must be at least 2 characters", "passwordMin": "Password must be at least 8 characters", "passwordFormat": "Password must contain at least one uppercase letter, one lowercase letter, and one number", "categoryRequired": "Please select a specific service category", "categoryComplete": "Please complete both category selection steps", "agreeTerms": "You must agree to the terms and conditions", "passwordMatch": "Passwords don't match"}, "firstNamePlaceholder": "Enter your first name", "lastNamePlaceholder": "Enter your last name", "emailPlaceholder": "Enter your email address", "phonePlaceholder": "Enter your phone number", "businessNamePlaceholder": "Enter your business name", "passwordPlaceholder": "Create a password", "confirmPasswordPlaceholder": "Confirm your password", "selectCategory": "Select your service category", "serviceCategory": "Service Category", "chooseCategoryPlaceholder": "Choose a service category", "specificService": "Specific Service", "chooseSpecificServicePlaceholder": "Choose your specific service", "servicesAvailable": "{{count}} service{{count, plural, one{} other{s}}} available", "selected": "Selected", "noServicesAvailable": "No specific services available", "noServicesMessage": "This category doesn't have specific service types yet. Please contact support to add your service type, or choose a different category that better matches your business.", "categoryInstructions": "First select your main service category, then choose your specific service type."}, "verification": {"title": "Verify Your Account", "subtitle": "We've sent a verification code to your email", "enterCode": "Enter verification code", "codePlaceholder": "Enter 6-digit code", "verifyButton": "Verify Account", "verifying": "Verifying...", "resendCode": "Resend code", "codeExpires": "Code expires in", "didntReceive": "Didn't receive the code?", "checkSpam": "Please check your spam folder", "wrongEmail": "Wrong email address?", "changeEmail": "Change email"}, "resetPassword": {"title": "Reset Password", "subtitle": "Enter your email to receive a password reset code", "email": "Email Address", "sendCode": "Send Reset Code", "backToSignIn": "Back to Sign In", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "resetButton": "Reset Password", "sending": "Sending...", "verifyTitle": "Verify Reset Code", "verifySubtitle": "Enter the 6-digit code sent to", "verificationCode": "Verification Code", "verifying": "Verifying...", "verifyCodeButton": "Verify Code", "setNewPasswordTitle": "Set New Password", "setNewPasswordSubtitle": "Enter your new password below", "confirmPassword": "Confirm Password", "resetting": "Resetting...", "rememberPassword": "Remember your password?", "emailPlaceholder": "Enter your email address", "newPasswordPlaceholder": "Enter new password", "confirmPasswordPlaceholder": "Confirm new password"}, "validation": {"emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 8 characters", "passwordComplexity": "Password must contain uppercase, lowercase, and number", "passwordsNoMatch": "Passwords do not match", "firstNameRequired": "First name is required", "firstNameTooShort": "First name must be at least 2 characters", "lastNameRequired": "Last name is required", "lastNameTooShort": "Last name must be at least 2 characters", "phoneRequired": "Phone number is required", "phoneInvalid": "Please enter a valid phone number", "businessNameRequired": "Business name is required", "businessNameTooShort": "Business name must be at least 2 characters", "categoryRequired": "Please select a service category", "termsRequired": "You must agree to the terms and conditions", "codeRequired": "Verification code is required", "codeInvalid": "Please enter a valid 6-digit code"}, "errors": {"invalidCredentials": "Invalid email or password", "accountNotFound": "Account not found", "accountDisabled": "Account has been disabled", "emailAlreadyExists": "An account with this email already exists", "phoneAlreadyExists": "An account with this phone number already exists", "invalidCode": "Invalid verification code", "codeExpired": "Verification code has expired", "tooManyAttempts": "Too many attempts. Please try again later", "networkError": "Network error. Please check your connection", "serverError": "Server error. Please try again later", "unknownError": "An unexpected error occurred"}, "success": {"accountCreated": "Account created successfully", "emailVerified": "Email verified successfully", "passwordReset": "Password reset successfully", "codeResent": "Verification code resent", "signInSuccessful": "Signed in successfully"}, "signOut": "Sign out", "signingOut": "Signing out..."}