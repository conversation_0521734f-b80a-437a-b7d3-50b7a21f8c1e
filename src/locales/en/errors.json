{"general": {"unknownError": "An unexpected error occurred", "networkError": "Network connection error", "serverError": "Server error occurred", "timeoutError": "Request timed out", "unauthorizedError": "You are not authorized to perform this action", "forbiddenError": "Access to this resource is forbidden", "notFoundError": "The requested resource was not found", "validationError": "Please check your input and try again", "conflictError": "A conflict occurred with the current state", "rateLimitError": "Too many requests. Please try again later", "maintenanceError": "Service is temporarily unavailable for maintenance"}, "auth": {"invalidCredentials": "Invalid email or password", "accountLocked": "Account has been locked due to multiple failed attempts", "accountDisabled": "Your account has been disabled", "sessionExpired": "Your session has expired. Please sign in again", "emailNotVerified": "Please verify your email address before signing in", "passwordExpired": "Your password has expired. Please reset it", "twoFactorRequired": "Two-factor authentication is required", "invalidToken": "Invalid or expired token", "emailAlreadyExists": "An account with this email already exists", "phoneAlreadyExists": "An account with this phone number already exists", "weakPassword": "Password does not meet security requirements", "passwordReused": "You cannot reuse a recent password", "invalidVerificationCode": "Invalid verification code", "verificationCodeExpired": "Verification code has expired", "tooManyAttempts": "Too many failed attempts. Please try again later"}, "appointments": {"appointmentNotFound": "Appointment not found", "appointmentConflict": "This time slot is already booked", "invalidTimeSlot": "Invalid time slot selected", "pastDateError": "Cannot schedule appointments in the past", "outsideBusinessHours": "Selected time is outside business hours", "customerNotAvailable": "Customer is not available at this time", "providerNotAvailable": "Provider is not available at this time", "serviceNotAvailable": "Selected service is not available", "locationNotAvailable": "Selected location is not available", "appointmentCancelled": "Appointment has been cancelled", "appointmentCompleted": "Appointment has already been completed", "insufficientCredits": "Insufficient credits to book this appointment", "bookingLimitReached": "Booking limit reached for this time period", "minimumNoticeRequired": "Minimum notice period required for booking"}, "customers": {"customerNotFound": "Customer not found", "duplicateCustomer": "A customer with this information already exists", "invalidCustomerData": "Invalid customer information provided", "customerHasAppointments": "Cannot delete customer with existing appointments", "customerBlocked": "This customer has been blocked", "invalidContactInfo": "Invalid contact information provided", "customerExistsInArchive": "Customer exists in archive. If you want to restore it, please use the restore customer endpoint.", "customerNotFoundInArchive": "No archived customer found with the provided information", "restoreCustomerFailed": "Failed to restore customer", "softDeleteFailed": "Failed to delete customer"}, "services": {"serviceNotFound": "Service not found", "serviceUnavailable": "Service is currently unavailable", "invalidServiceData": "Invalid service information provided", "serviceHasAppointments": "Cannot delete service with existing appointments", "duplicateService": "A service with this name already exists", "invalidPricing": "Invalid pricing information", "invalidDuration": "Invalid service duration"}, "locations": {"locationNotFound": "Location not found", "locationUnavailable": "Location is currently unavailable", "invalidLocationData": "Invalid location information provided", "locationHasAppointments": "Cannot delete location with existing appointments", "duplicateLocation": "A location with this name already exists", "invalidAddress": "Invalid address provided", "invalidCoordinates": "Invalid coordinates provided"}, "payments": {"paymentFailed": "Payment processing failed", "invalidPaymentMethod": "Invalid payment method", "insufficientFunds": "Insufficient funds", "paymentDeclined": "Payment was declined", "paymentExpired": "Payment session has expired", "refundFailed": "Refund processing failed", "invalidAmount": "Invalid payment amount", "currencyNotSupported": "<PERSON><PERSON><PERSON><PERSON> not supported", "paymentMethodNotSupported": "Payment method not supported"}, "files": {"fileNotFound": "File not found", "fileTooLarge": "File size exceeds the maximum limit", "invalidFileType": "Invalid file type", "uploadFailed": "File upload failed", "corruptedFile": "File appears to be corrupted", "virusDetected": "File contains malicious content", "storageQuotaExceeded": "Storage quota exceeded"}, "validation": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "invalidUrl": "Please enter a valid URL", "invalidDate": "Please enter a valid date", "invalidTime": "Please enter a valid time", "tooShort": "Input is too short", "tooLong": "Input is too long", "invalidFormat": "Invalid format", "invalidCharacters": "Contains invalid characters", "numberTooSmall": "Number is too small", "numberTooLarge": "Number is too large", "invalidRange": "Value is outside the valid range"}}