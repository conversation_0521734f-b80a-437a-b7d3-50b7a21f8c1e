{"appointments": {"title": "Appointments Management", "description": "Manage your appointments and customer bookings", "noAppointments": "No appointments found", "noAppointmentsFiltered": "No appointments match your current filters. Try adjusting your search criteria.", "getStarted": "Get started by creating your first appointment.", "createFirstAppointment": "Create Your First Appointment", "failedToLoad": "Failed to load appointments", "details": {"title": "Appointment Details", "subtitle": "View and manage appointment information", "customerInformation": "Customer Information", "serviceDetails": "Service Details", "service": "Service", "minutes": "minutes", "schedule": "Schedule", "location": "Location", "cancelAppointment": "<PERSON>cel Appointment", "cancelReasonPlaceholder": "Reason for cancellation (optional)", "cancelling": "Cancelling...", "confirmCancel": "Confirm Cancel", "completeAppointment": "Complete Appointment", "completionNotesPlaceholder": "Completion notes (optional)", "completing": "Completing...", "markComplete": "Mark Complete", "rescheduleAppointment": "Reschedule Appointment", "rescheduling": "Rescheduling...", "reschedule": "Reschedule", "confirming": "Confirming...", "starting": "Starting...", "start": "Start", "complete": "Complete", "marking": "Marking...", "noShow": "No Show", "appointmentStarted": "Appointment started"}}, "filters": {"all": "All", "active": "Active", "inactive": "Inactive", "public": "Public", "private": "Private", "clearFilters": "Clear Filters", "applyFilters": "Apply Filters", "dateRange": "Date Range", "allDates": "All Dates", "today": "Today", "next7Days": "Next 7 Days", "next30Days": "Next 30 Days", "startDate": "Start Date", "endDate": "End Date", "category": "Category", "allCategories": "All Categories", "deliveryType": "Delivery Type", "allTypes": "All Types", "atLocation": "At Location", "atCustomer": "At Customer", "both": "Both"}, "common": {"loading": "Loading...", "error": "Error loading data", "retry": "Retry", "search": "Search...", "filter": "Filter", "sort": "Sort", "actions": "Actions", "status": "Status", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "public": "Public", "private": "Private", "online": "Online", "offline": "Offline", "management": "Management"}, "customers": {"title": "Customer Management", "description": "Manage your customer relationships and track their appointment history", "addNewCustomer": "Add New Customer", "editCustomer": "Edit Customer", "deleteCustomer": "Delete Customer", "viewCustomer": "View Customer", "noCustomers": "No customers found", "noCustomersFound": "No customers found", "noCustomersFiltered": "No customers match your current filters. Try adjusting your search criteria.", "getStarted": "Get started by adding your first customer to build relationships.", "addFirstCustomer": "Add your first customer to get started", "failedToLoad": "Failed to load customers", "customerStatistics": "Customer Statistics", "totalCustomers": "Total Customers", "activeCustomers": "Active Customers", "inactiveCustomers": "Inactive Customers", "newThisMonth": "New This Month", "avgAppointments": "Avg Appointments", "vsLastMonth": "vs last month", "customerDistribution": "Customer Distribution", "customerEngagement": "Customer Engagement", "totalAppointments": "Total Appointments", "avgPerCustomer": "Avg per Customer", "retentionRate": "Retention Rate", "repeatCustomers": "Repeat Customers", "active": "Active", "inactive": "Inactive", "searchPlaceholder": "Search customers...", "export": {"title": "Export Customers", "description": "Export your customer data in various formats", "exportFormat": "Export Format", "formats": {"csvDescription": "Comma-separated values", "jsonDescription": "JavaScript Object Notation", "excelDescription": "Microsoft Excel format"}, "fieldsToExport": "Fields to Export ({{count}} selected)", "selectAll": "Select All", "deselectAll": "Deselect All", "fieldLabels": {"firstName": "First Name", "lastName": "Last Name", "mobileNumber": "Mobile Number", "email": "Email", "nationalId": "National ID", "notes": "Notes", "appointmentCount": "Appointment Count", "createdAt": "Created At", "isActive": "Is Active"}, "fields": {"firstName": "First Name", "lastName": "Last Name", "mobileNumber": "Mobile Number", "email": "Email", "nationalId": "National ID", "notes": "Notes", "appointmentCount": "Appointment Count", "createdAt": "Created At", "status": "Status"}, "exportOptions": "Export Options", "includeInactiveCustomers": "Include inactive customers", "readyToExport": "Ready to export {{customerCount}} customers with {{fieldCount}} fields", "exportButton": "Export {{format}}", "noCustomersToExport": "No customers to export", "exportFailed": "Export failed. Please try again."}, "addCustomer": "Add Customer", "quickFilters": "Quick Filters", "allCustomers": "All Customers", "withEmail": "With Email", "highEngagement": "High Engagement", "customerSince": "Customer Since", "appointments": "Appointments", "appointmentCount": "Appointment Count", "lastUpdated": "Last Updated", "view": "View", "edit": "Edit", "delete": "Delete", "restore": {"title": "Restore Customer", "description": "Enter customer information to restore from archive", "instructions": "Provide at least one of the following fields to search for and restore an archived customer.", "customerExistsInArchive": "Customer exists in archive", "customerExistsMessage": "A customer with this information already exists in the archive. Would you like to restore them?", "restoreButton": "Restore Customer", "noCustomerFound": "No archived customer found with the provided information.", "restoreSuccess": "Customer restored successfully!", "restoreFailed": "Failed to restore customer"}, "actions": "Actions", "statistics": "Statistics", "details": {"overview": "Overview", "editCustomer": "Edit Customer", "customerSince": "Customer since {{date}}", "contactInformation": "Contact Information", "nationalId": "National ID", "customerStatistics": "Customer Statistics", "totalAppointments": "Total Appointments", "relationshipStatus": "Relationship Status", "customerSinceLabel": "Customer Since", "providerNotes": "Provider Notes", "appointmentHistory": "Appointment History", "appointmentHistoryDescription": "Detailed appointment history will be displayed here when integrated with the appointments system.", "totalAppointmentsCount": "Total Appointments: {{count}}", "providerNotesCommunications": "Provider Notes & Communications", "customerNotes": "Customer Notes", "lastUpdated": "Last updated: {{date}}", "noNotesYet": "No Notes Yet", "addNotesDescription": "Add notes about this customer to keep track of important information.", "addNotes": "Add Notes"}, "softDelete": {"confirmTitle": "Delete Customer", "confirmMessage": "Are you sure you want to delete {customerName}? This action will move the customer to archive and can be restored later.", "deleteButton": "Delete Customer", "deleteSuccess": "Customer deleted successfully!", "deleteFailed": "Failed to delete customer"}}, "services": {"title": "Services Management", "subtitle": "Create and manage your service offerings", "description": "Manage your services, pricing, and categories", "manageCategories": "Manage Categories", "createService": "Create Service", "yourServices": "Your Services", "manageOfferings": "Manage your service offerings and pricing", "search": "Search", "status": "Status", "searchPlaceholder": "Search services...", "allServices": "All Services", "public": "Public", "private": "Private", "noServicesFound": "No services found", "noServicesFiltered": "No services match your current filters. Try adjusting your search criteria.", "getStarted": "Get started by creating your first service.", "createFirstService": "Create Your First Service", "failedToLoad": "Failed to load services", "categoryManager": {"title": "Manage Service Categories", "subtitle": "Create and organize your service categories", "createCategory": "Create Category", "editCategory": "Edit Category", "backToList": "Back to List", "categoryName": "Category Name", "categoryNamePlaceholder": "Enter category name", "createFirstCategory": "Create First Category", "noCategoriesFound": "No categories found. Create your first category to organize your services.", "edit": "Edit", "delete": "Delete", "deleteConfirmation": "Are you sure you want to delete this category?", "categoryNameRequired": "Category name must be at least 2 characters", "loading": "Loading categories...", "creating": "Creating...", "updating": "Updating...", "deleting": "Deleting..."}, "form": {"createNewService": "Create New Service", "editService": "Edit Service", "addNewServiceDescription": "Add a new service to your offerings", "updateServiceDescription": "Update service details", "serviceName": "Service Name", "duration": "Duration (minutes)", "price": "Price", "pointsRequired": "Points Required", "deliveryType": "Delivery Type", "category": "Category", "color": "Color", "description": "Description", "servedRegions": "Served Regions", "serviceSettings": "Service Settings", "placeholders": {"serviceName": "Enter service name", "duration": "60", "price": "0.00", "points": "0", "description": "Describe your service...", "regions": "Enter regions separated by commas (e.g., Downtown, Uptown, Suburbs)"}, "deliveryOptions": {"atLocation": "At Location", "atCustomer": "At Customer", "both": "Both"}, "noCategory": "No Category", "colors": {"blue": "Blue", "green": "Green", "yellow": "Yellow", "red": "Red", "purple": "Purple", "cyan": "<PERSON><PERSON>", "orange": "Orange", "lime": "Lime"}, "settings": {"publicService": "Public Service", "publicServiceDesc": "Visible to customers", "onlineBooking": "Online Booking", "onlineBookingDesc": "Allow online appointments", "acceptNewCustomers": "Accept New Customers", "acceptNewCustomersDesc": "Open to new clients", "notifications": "Notifications", "notificationsDesc": "Receive booking alerts"}, "helpText": {"pointsTooltip": "Credits/points customers need to book this service. Default is 1 point per booking.", "regionsHelp": "Leave empty if service is available everywhere"}, "createService": "Create Service", "updateService": "Update Service"}, "deleteConfirmTitle": "Delete Service", "deleteConfirmMessage": "Are you sure you want to delete this service? This action cannot be undone and will remove all associated data."}, "locations": {"title": "Locations Management", "subtitle": "Set up your business locations and operating hours", "description": "Manage your service locations and operating hours", "loadingLocations": "Loading locations...", "yourLocations": "Your Locations", "manageLocations": "Manage your business locations and operating hours", "createLocation": "Create Location", "editLocation": "Edit Location", "deleteLocation": "Delete Location", "manageHours": "Manage Hours", "addLocation": "Add Location", "city": "City", "amenities": "Amenities", "search": "Search", "cityPlaceholder": "Filter by city...", "searchPlaceholder": "Search locations...", "allLocations": "All Locations", "withParking": "With Parking", "withElevator": "With Elevator", "handicapAccessible": "Handicap Accessible", "noLocationsFound": "No locations found", "noLocationsFiltered": "No locations match your current filters. Try adjusting your search criteria.", "getStarted": "Get started by adding your first business location.", "addFirstLocation": "Add Your First Location", "failedToLoad": "Failed to load locations", "noLocations": "No locations found", "form": {"createLocation": "Create Location", "editLocation": "Edit Location", "addNewLocationDescription": "Add a new service location", "updateLocationDescription": "Update location details", "basicInformation": "Basic Information", "addressInformation": "Address Information", "contactInformation": "Contact Information", "amenitiesAccessibility": "Amenities & Accessibility", "coordinates": "Coordinates", "openingHours": "Opening Hours", "locationName": "Location Name", "shortName": "Short Name", "streetAddress": "Street Address", "city": "City", "country": "Country", "postalCode": "Postal Code", "floorSuite": "Floor/Suite", "timezone": "Timezone", "mobilePhone": "Mobile Phone", "fax": "Fax", "latitude": "Latitude", "longitude": "Longitude", "dayOfWeek": "Day of Week", "placeholders": {"enterName": "Enter location name", "enterShortName": "Optional short name", "enterAddress": "Enter street address", "enterCity": "Enter city", "enterCountry": "Enter country", "enterPostalCode": "Enter postal code", "floorNumber": "Floor or suite number", "enterMobile": "Enter mobile number", "enterFax": "Enter fax number", "latitudeExample": "e.g., 40.7128", "longitudeExample": "e.g., -74.0060"}, "buttons": {"useMyTimezone": "Use my timezone", "selectTimezone": "Select timezone", "standardHours": "Standard Hours", "addDay": "Add Day", "useCurrentLocation": "Use my current location", "gettingLocation": "Getting location...", "createLocation": "Create Location", "updateLocation": "Update Location", "creating": "Creating...", "updating": "Updating..."}, "checkboxes": {"hideMobile": "Hide mobile number from customers", "hideMobileDesc": "Mobile number will not be displayed publicly", "parkingAvailable": "Parking Available", "parkingDesc": "On-site parking for customers", "elevatorAccess": "Elevator Access", "elevatorDesc": "Elevator available in building", "handicapAccessible": "Handicap Accessible", "handicapDesc": "Wheelchair accessible entrance"}, "helpText": {"coordinatesHelp": "Coordinates help customers find your location more easily on maps", "noOpeningHours": "No opening hours set. Click \"Add Day\" to get started."}, "daysOfWeek": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}}, "deleteConfirmTitle": "Delete Location", "deleteConfirmMessage": "Are you sure you want to delete this location? This action cannot be undone."}, "queues": {"title": "Queue Management", "subtitle": "Configure appointment queues and waiting systems", "description": "Manage your service queues and waiting lists", "createQueue": "Create Queue", "editQueue": "<PERSON>", "deleteQueue": "Delete Queue", "viewQueue": "View Queue", "loadingQueues": "Loading queues...", "yourQueues": "Your Queues", "manageQueues": "Manage your appointment queues and waiting systems", "noQueues": "No queues found", "addFirstQueue": "Add your first queue to get started", "queueStatistics": "Queue Statistics", "activeQueues": "Active Queues", "totalWaiting": "Total Waiting", "averageWaitTime": "Average Wait Time", "failedToLoad": "Failed to load queues", "noQueuesFound": "No queues found", "noQueuesFiltered": "No queues match your current filters. Try adjusting your search criteria.", "getStarted": "Get started by creating your first queue to manage customer wait times.", "createFirstQueue": "Create Your First Queue", "status": "Status", "location": "Location", "service": "Service", "search": "Search", "allQueues": "All Queues", "active": "Active", "inactive": "Inactive", "allLocations": "All Locations", "allServices": "All Services", "searchPlaceholder": "Search queues...", "deleteConfirmTitle": "Delete Queue", "deleteConfirmMessage": "Are you sure you want to delete this queue? This action cannot be undone and will remove all associated data.", "deleteConfirmButton": "Delete Queue", "cancelButton": "Cancel", "queueLimits": "Queue Limits", "queuesUsed": "Queues Used", "currentPlan": "Current Plan", "planFeatures": "Plan Features", "realTimeUpdates": "Real-time Updates", "advancedAnalytics": "Advanced Analytics", "prioritySupport": "Priority Support", "queueLimitReached": "Queue Limit Reached", "upgradeToCreateMore": "Upgrade your plan to create more queues and unlock additional features.", "readyToCreate": "Ready to Create", "canCreateMore": "You can create {count} more queue(s).", "unlimited": "unlimited", "free": "Free", "queueDetails": {"activeSession": "Active Session", "inProgress": "In Progress", "customer": "Customer", "service": "Service", "timeRemaining": "Time Remaining", "started": "Started", "noActiveSession": "No Active Session", "noActiveSessionDesc": "No appointment is currently in progress for this queue", "queueStats": "Queue Statistics", "confirmedAppointments": "Confirmed Appointments", "upcomingToday": "Upcoming Today", "upcomingAppointments": "Upcoming Appointments", "next5": "Next 5", "noUpcomingAppointments": "No upcoming appointments for this queue"}, "form": {"createQueue": "Create Queue", "editQueue": "<PERSON>", "createNewQueueDescription": "Create a new queue for managing customer wait times", "updateQueueDescription": "Update queue details", "queueTitle": "Queue Title", "location": "Location", "services": "Services", "openingHours": "Opening Hours", "queueTitlePlaceholder": "Enter queue title", "selectLocation": "Select a location", "standardHours": "Standard Hours", "addDay": "Add Day", "remove": "Remove", "addTimeSlot": "Add Time Slot", "cancel": "Cancel", "createQueueBtn": "Create Queue", "updateQueueBtn": "Update Queue", "submitting": "Submitting...", "queueIsActive": "<PERSON><PERSON> is active", "active": "Active", "to": "to", "noOpeningHours": "No opening hours set. Click \"Add Day\" to get started.", "daysOfWeek": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}}, "cards": {"queue": {"active": "Active", "inactive": "Inactive", "highPriority": "High Priority", "mediumPriority": "Medium Priority", "lowPriority": "Low Priority", "current": "Current", "estimatedWait": "<PERSON>st. <PERSON> (min)", "capacity": "Capacity", "services": "Services ({count})", "service": "Service {index}", "moreServices": "+{count} more", "edit": "Edit", "delete": "Delete", "activateQueue": "Activate Queue", "deactivateQueue": "Deactivate Queue", "updated": "Updated {date}", "maxCapacity": "/ {max} max"}, "location": {"active": "Active", "noAddress": "No address specified", "floor": "Floor: {floor}", "fax": "Fax: {fax}", "amenities": "Amenities", "parking": "Parking", "elevator": "Elevator", "handicapAccess": "Handicap Access", "edit": "Edit", "hours": "Hours", "deleteLocation": "Delete Location", "deleting": "Deleting..."}}, "profile": {"title": "Profile", "businessProfile": "Business Profile", "accountInformation": "Account Information", "profilePicture": "Profile Picture", "businessLogo": "Business Logo", "businessName": "Business Name", "phoneNumber": "Phone Number", "businessDescription": "Business Description", "email": "Email", "accountName": "Account Name", "businessCategory": "Business Category", "businessNamePlaceholder": "Enter your business name", "phoneNumberPlaceholder": "Enter your phone number", "businessDescriptionPlaceholder": "Tell customers about your business...", "editProfile": "Edit Profile", "cancel": "Cancel", "uploadPicture": "Upload Picture", "uploadLogo": "Upload Logo", "removePicture": "Remove Picture", "removeLogo": "Remove Logo", "saveChanges": "Save Changes", "uploading": "Uploading...", "removing": "Removing...", "saving": "Saving...", "noCategorySelected": "No category selected", "completion": {"completeProfileSetup": "Complete Your Profile Setup", "profileSectionsComplete": "Profile sections: {percentage}% complete", "showLess": "Show Less", "showDetails": "Show Details", "profileProgress": "Profile Progress", "uploadBusinessLogo": "Upload Business Logo", "completeBusinessInfo": "Complete Business Information", "addLogoDescription": "Add a professional logo to build trust with customers", "missingFields": "Missing: {fields}", "uploadLogo": "Upload Logo", "completeInfo": "Complete Info", "overallSetup": "Overall setup: {percentage}% complete", "continueSetup": "Continue Setup →", "profileSectionsCompleted": "Profile sections complete!", "continueOtherSections": "Continue with other sections →"}}, "subscription": {"title": "Subscription", "management": "Subscription Management", "description": "Manage your subscription, view usage, and upgrade your plan", "upgradePlan": "Upgrade Plan", "changePlan": "Change Plan", "overview": {"usageStatistics": "Usage Statistics", "thisMonth": "This Month", "creditsUsed": "Credits Used", "of": "of", "percentUsed": "{percentage}% used", "remaining": "{remaining} remaining", "queuesActive": "Queues Active", "available": "{available} available", "currentPlan": "Current Plan:", "planName": "Plan Name", "price": "Price", "credits": "Credits", "queues": "Queues", "perMonth": "/month", "unlimited": "Unlimited", "features": "Features", "current": "Current", "recommended": "Recommended", "mostPopular": "Most Popular", "subscriptionManagement": "Subscription Management", "portalUnavailable": "Portal Unavailable", "manageSubscription": "Manage Subscription", "openingPortal": "Opening Portal...", "loading": "Loading...", "manageBilling": "Manage Billing", "portalAccessMessage": "Customer portal access requires an active subscription. Upgrade your plan to access subscription management features.", "portalDescriptionActive": "Access your customer portal to manage your {plan}, update payment methods, and view billing history.", "creditsDisplay": "{amount} Credits", "plans": {"oneTimePurchaseDescription": "One-time purchase of {amount} credits for your account", "mostPopularPlan": "Our most popular plan", "allYouNeedToStart": "All you need to get started", "freePlanForEveryone": "Free plan for everyone", "priorityCustomerSupport": "Priority customer support", "basicSupport": "Basic support", "noExpirationDate": "No expiration date", "queues": "Queues {count}", "queue": "Queue {count}", "creditsPerMonth": "{amount} Credits/month", "oneTimePurchase": "One-time Purchase", "monthlySubscription": "Monthly Subscription", "needHelpChoosing": "Need help choosing?", "planFeaturesDescription": "All plans include our core features. You can upgrade or downgrade at any time, and unused credits never expire."}}, "plans": "Plans", "usage": "Usage", "failedToLoad": "Failed to load subscription data", "currentPlanDetails": "Current Plan Details", "usageInsights": "Usage Insights", "choosePlan": "Choose Your Plan", "planDescription": "Select the perfect plan for your business needs. Upgrade or downgrade at any time.", "selectPlanDescription": "Select the plan that best fits your business needs", "currentPlan": "Current Plan", "purchaseCredits": "Purchase Credits", "getStarted": "Get Started", "subscribe": "Subscribe"}}, "confirmations": {"deleteService": "Are you sure you want to delete this service?", "deleteLocation": "Are you sure you want to delete this location?", "deleteCustomer": "Are you sure you want to delete this customer?", "deleteQueue": "Are you sure you want to delete this queue?", "deleteWarning": "This action cannot be undone."}, "pagination": {"showing": "Showing", "to": "to", "of": "of", "page": "Page"}, "businessSetup": {"title": "Business Setup", "subtitle": "Configure your business services, locations, and appointment queues", "description": "Configure your business services, locations, and appointment queues", "servicesDesc": "Manage your service offerings and pricing", "locationsDesc": "Set up your business locations and operating hours", "queuesDesc": "Configure appointment queues and waiting systems"}, "serviceSession": {"title": "Service Session", "description": "Active service session management", "sessionTimer": "Session Timer", "currentCustomer": "Current Customer", "comingNext": "Coming Next", "extendTimer": "Extend Timer", "completeSession": "Complete Session", "completing": "Completing...", "appointmentNotFound": "Appointment Not Found", "sessionNotActive": "Session Not Active", "couldNotLoad": "The appointment session could not be loaded.", "notInProgress": "This appointment is not currently in progress.", "completedFromSessionPage": "Session completed from service session page", "sessionCompletedSuccessfully": "Session completed successfully!", "timerExtended": "Timer extended by {{minutes}} minutes", "noUpcomingAppointments": "No upcoming appointments", "extendOptions": {"5minutes": "5 minutes", "10minutes": "10 minutes", "15minutes": "15 minutes", "30minutes": "30 minutes"}, "durationMinutes": "{{duration}} minutes"}}